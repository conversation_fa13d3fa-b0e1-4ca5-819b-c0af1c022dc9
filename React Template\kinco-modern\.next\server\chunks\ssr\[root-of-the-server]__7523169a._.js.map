{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kinco-day-care-kindergarten-react-template-30-06-2025/Kinco/React%20Template/kinco-modern/src/app/blog/page.js"], "sourcesContent": ["'use client';\n\nimport Link from \"next/link\";\n\nexport default function Blog() {\n  const blogPosts = [\n    {\n      id: 1,\n      title: \"5 Tips for Preparing Your Child for Kindergarten\",\n      excerpt: \"Starting kindergarten is a big milestone. Here are some practical tips to help your child transition smoothly.\",\n      date: \"December 15, 2024\",\n      author: \"<PERSON>\",\n      image: \"/assets/images/common/sc-blog1.jpg\",\n      category: \"Education\"\n    },\n    {\n      id: 2,\n      title: \"The Importance of Play-Based Learning\",\n      excerpt: \"Discover how play-based learning helps children develop essential skills while having fun.\",\n      date: \"December 10, 2024\", \n      author: \"<PERSON>\",\n      image: \"/assets/images/common/sc-blog2.jpg\",\n      category: \"Learning\"\n    },\n    {\n      id: 3,\n      title: \"Building Social Skills in Early Childhood\",\n      excerpt: \"Learn effective strategies to help your child develop strong social and emotional skills.\",\n      date: \"December 5, 2024\",\n      author: \"<PERSON>\", \n      image: \"/assets/images/common/sc-blog3.jpg\",\n      category: \"Development\"\n    },\n    {\n      id: 4,\n      title: \"Healthy Eating Habits for Growing Minds\",\n      excerpt: \"Nutrition plays a crucial role in child development. Here's how to establish healthy eating habits.\",\n      date: \"November 30, 2024\",\n      author: \"<PERSON>\",\n      image: \"/assets/images/common/sc-program1.jpg\",\n      category: \"Health\"\n    },\n    {\n      id: 5,\n      title: \"Creating a Learning Environment at Home\",\n      excerpt: \"Simple ways to create an engaging and educational environment that supports your child's learning.\",\n      date: \"November 25, 2024\",\n      author: \"David Martinez\",\n      image: \"/assets/images/common/sc-program2.jpg\", \n      category: \"Home Learning\"\n    },\n    {\n      id: 6,\n      title: \"The Benefits of Outdoor Education\",\n      excerpt: \"Explore how outdoor activities and nature-based learning contribute to child development.\",\n      date: \"November 20, 2024\",\n      author: \"Jennifer Lee\",\n      image: \"/assets/images/common/sc-program3.jpg\",\n      category: \"Outdoor Learning\"\n    }\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm\">\n        <div className=\"container mx-auto px-4 py-4\">\n          <div className=\"flex justify-between items-center\">\n            <Link href=\"/\" className=\"flex items-center\">\n              <img src=\"/assets/images/logo/logodark-2.png\" alt=\"Kinco School\" className=\"h-12\" />\n            </Link>\n            <nav className=\"hidden md:flex space-x-8\">\n              <Link href=\"/\" className=\"text-gray-600 hover:text-blue-600 transition-colors\">Home</Link>\n              <Link href=\"/about\" className=\"text-gray-600 hover:text-blue-600 transition-colors\">About</Link>\n              <Link href=\"/classes\" className=\"text-gray-600 hover:text-blue-600 transition-colors\">Classes</Link>\n              <Link href=\"/program\" className=\"text-gray-600 hover:text-blue-600 transition-colors\">Programs</Link>\n              <Link href=\"/teacher\" className=\"text-gray-600 hover:text-blue-600 transition-colors\">Teachers</Link>\n              <Link href=\"/blog\" className=\"text-blue-600 font-semibold\">Blog</Link>\n              <Link href=\"/contact\" className=\"text-gray-600 hover:text-blue-600 transition-colors\">Contact</Link>\n            </nav>\n          </div>\n        </div>\n      </header>\n\n      {/* Page Banner */}\n      <section className=\"bg-gradient-to-r from-blue-500 to-purple-600 text-white py-16\">\n        <div className=\"container mx-auto px-4 text-center\">\n          <h1 className=\"text-4xl font-bold mb-4\">Our Blog</h1>\n          <nav className=\"text-blue-200\">\n            <Link href=\"/\" className=\"hover:text-white\">Home</Link>\n            <span className=\"mx-2\">/</span>\n            <span>Blog</span>\n          </nav>\n        </div>\n      </section>\n\n      {/* Blog Posts */}\n      <section className=\"py-16\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-4xl font-bold mb-4 text-gray-800\">Latest Articles</h2>\n            <p className=\"text-gray-600 max-w-2xl mx-auto\">\n              Stay updated with the latest insights, tips, and news about early childhood education\n            </p>\n          </div>\n\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {blogPosts.map((post) => (\n              <article key={post.id} className=\"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow\">\n                <img \n                  src={post.image} \n                  alt={post.title}\n                  className=\"w-full h-48 object-cover\"\n                />\n                <div className=\"p-6\">\n                  <div className=\"flex items-center justify-between mb-3\">\n                    <span className=\"bg-blue-100 text-blue-800 text-xs font-semibold px-2 py-1 rounded\">\n                      {post.category}\n                    </span>\n                    <span className=\"text-gray-500 text-sm\">{post.date}</span>\n                  </div>\n                  <h3 className=\"text-xl font-bold mb-3 text-gray-800 hover:text-blue-600 transition-colors\">\n                    <Link href={`/blog/${post.id}`}>\n                      {post.title}\n                    </Link>\n                  </h3>\n                  <p className=\"text-gray-600 mb-4\">{post.excerpt}</p>\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-sm text-gray-500\">By {post.author}</span>\n                    <Link \n                      href={`/blog/${post.id}`}\n                      className=\"text-blue-600 hover:text-blue-800 font-semibold text-sm\"\n                    >\n                      Read More →\n                    </Link>\n                  </div>\n                </div>\n              </article>\n            ))}\n          </div>\n\n          {/* Pagination */}\n          <div className=\"flex justify-center mt-12\">\n            <nav className=\"flex space-x-2\">\n              <button className=\"px-3 py-2 text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50\">\n                Previous\n              </button>\n              <button className=\"px-3 py-2 text-white bg-blue-600 border border-blue-600 rounded-md\">\n                1\n              </button>\n              <button className=\"px-3 py-2 text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50\">\n                2\n              </button>\n              <button className=\"px-3 py-2 text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50\">\n                3\n              </button>\n              <button className=\"px-3 py-2 text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50\">\n                Next\n              </button>\n            </nav>\n          </div>\n        </div>\n      </section>\n\n      {/* Newsletter Signup */}\n      <section className=\"bg-blue-600 text-white py-16\">\n        <div className=\"container mx-auto px-4 text-center\">\n          <h2 className=\"text-3xl font-bold mb-4\">Stay Updated</h2>\n          <p className=\"text-xl mb-8 text-blue-100\">\n            Subscribe to our newsletter for the latest educational tips and school updates\n          </p>\n          <div className=\"max-w-md mx-auto flex\">\n            <input \n              type=\"email\" \n              placeholder=\"Enter your email\"\n              className=\"flex-1 px-4 py-3 rounded-l-lg text-gray-900\"\n            />\n            <button className=\"bg-yellow-400 text-blue-900 px-6 py-3 rounded-r-lg font-semibold hover:bg-yellow-300 transition-colors\">\n              Subscribe\n            </button>\n          </div>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"bg-gray-800 text-white py-8\">\n        <div className=\"container mx-auto px-4 text-center\">\n          <div className=\"mb-4\">\n            <Link href=\"/\" className=\"text-2xl font-bold text-blue-400\">\n              Kinco School\n            </Link>\n          </div>\n          <p className=\"text-gray-400 mb-4\">\n            Providing quality education and care for your children\n          </p>\n          <div className=\"flex justify-center space-x-6\">\n            <Link href=\"/about\" className=\"text-gray-400 hover:text-white\">About</Link>\n            <Link href=\"/classes\" className=\"text-gray-400 hover:text-white\">Classes</Link>\n            <Link href=\"/blog\" className=\"text-white\">Blog</Link>\n            <Link href=\"/contact\" className=\"text-gray-400 hover:text-white\">Contact</Link>\n          </div>\n          <div className=\"mt-6 pt-6 border-t border-gray-700\">\n            <p className=\"text-gray-400\">© 2024 Kinco School. All rights reserved.</p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,MAAM,YAAY;QAChB;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,MAAM;YACN,QAAQ;YACR,OAAO;YACP,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,MAAM;YACN,QAAQ;YACR,OAAO;YACP,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,MAAM;YACN,QAAQ;YACR,OAAO;YACP,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,MAAM;YACN,QAAQ;YACR,OAAO;YACP,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,MAAM;YACN,QAAQ;YACR,OAAO;YACP,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,MAAM;YACN,QAAQ;YACR,OAAO;YACP,UAAU;QACZ;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CACvB,cAAA,8OAAC;oCAAI,KAAI;oCAAqC,KAAI;oCAAe,WAAU;;;;;;;;;;;0CAE7E,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDAAsD;;;;;;kDAC/E,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAAsD;;;;;;kDACpF,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAsD;;;;;;kDACtF,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAsD;;;;;;kDACtF,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAsD;;;;;;kDACtF,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAQ,WAAU;kDAA8B;;;;;;kDAC3D,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAsD;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO9F,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA0B;;;;;;sCACxC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAmB;;;;;;8CAC5C,8OAAC;oCAAK,WAAU;8CAAO;;;;;;8CACvB,8OAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;0BAMZ,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAE,WAAU;8CAAkC;;;;;;;;;;;;sCAKjD,8OAAC;4BAAI,WAAU;sCACZ,UAAU,GAAG,CAAC,CAAC,qBACd,8OAAC;oCAAsB,WAAU;;sDAC/B,8OAAC;4CACC,KAAK,KAAK,KAAK;4CACf,KAAK,KAAK,KAAK;4CACf,WAAU;;;;;;sDAEZ,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEACb,KAAK,QAAQ;;;;;;sEAEhB,8OAAC;4DAAK,WAAU;sEAAyB,KAAK,IAAI;;;;;;;;;;;;8DAEpD,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;kEAC3B,KAAK,KAAK;;;;;;;;;;;8DAGf,8OAAC;oDAAE,WAAU;8DAAsB,KAAK,OAAO;;;;;;8DAC/C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;;gEAAwB;gEAAI,KAAK,MAAM;;;;;;;sEACvD,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;4DACxB,WAAU;sEACX;;;;;;;;;;;;;;;;;;;mCAxBO,KAAK,EAAE;;;;;;;;;;sCAkCzB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAO,WAAU;kDAAsF;;;;;;kDAGxG,8OAAC;wCAAO,WAAU;kDAAqE;;;;;;kDAGvF,8OAAC;wCAAO,WAAU;kDAAsF;;;;;;kDAGxG,8OAAC;wCAAO,WAAU;kDAAsF;;;;;;kDAGxG,8OAAC;wCAAO,WAAU;kDAAsF;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAShH,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA0B;;;;;;sCACxC,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAG1C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,WAAU;;;;;;8CAEZ,8OAAC;oCAAO,WAAU;8CAAyG;;;;;;;;;;;;;;;;;;;;;;;0BAQjI,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CAAmC;;;;;;;;;;;sCAI9D,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAGlC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAAiC;;;;;;8CAC/D,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAiC;;;;;;8CACjE,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAQ,WAAU;8CAAa;;;;;;8CAC1C,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAiC;;;;;;;;;;;;sCAEnE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMzC", "debugId": null}}]}