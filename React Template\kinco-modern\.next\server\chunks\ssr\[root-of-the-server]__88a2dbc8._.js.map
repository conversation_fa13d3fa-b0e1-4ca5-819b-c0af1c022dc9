{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kinco-day-care-kindergarten-react-template-30-06-2025/Kinco/React%20Template/kinco-modern/src/app/classes/page.js"], "sourcesContent": ["'use client';\n\nimport Link from \"next/link\";\n\nexport default function Classes() {\n  const classes = [\n    {\n      id: 1,\n      title: \"Drawing & Painting\",\n      age: \"3-5 years\",\n      time: \"9:00 AM - 11:00 AM\",\n      description: \"Creative art classes to develop imagination and fine motor skills\",\n      image: \"/assets/images/common/sc-program1.jpg\"\n    },\n    {\n      id: 2,\n      title: \"Computer Learning\",\n      age: \"4-6 years\", \n      time: \"2:00 PM - 4:00 PM\",\n      description: \"Introduction to basic computer skills and digital literacy\",\n      image: \"/assets/images/common/sc-program2.jpg\"\n    },\n    {\n      id: 3,\n      title: \"Basic English JR\",\n      age: \"3-4 years\",\n      time: \"10:00 AM - 12:00 PM\", \n      description: \"Foundation English language learning through fun activities\",\n      image: \"/assets/images/common/sc-program3.jpg\"\n    },\n    {\n      id: 4,\n      title: \"Music & Dance\",\n      age: \"3-6 years\",\n      time: \"3:00 PM - 5:00 PM\",\n      description: \"Develop rhythm, coordination and musical appreciation\",\n      image: \"/assets/images/common/sc-program4.jpg\"\n    }\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm\">\n        <div className=\"container mx-auto px-4 py-4\">\n          <div className=\"flex justify-between items-center\">\n            <Link href=\"/\" className=\"text-2xl font-bold text-blue-600\">\n              Kinco School\n            </Link>\n            <nav className=\"hidden md:flex space-x-6\">\n              <Link href=\"/about\" className=\"text-gray-600 hover:text-blue-600\">About</Link>\n              <Link href=\"/classes\" className=\"text-blue-600 font-semibold\">Classes</Link>\n              <Link href=\"/contact\" className=\"text-gray-600 hover:text-blue-600\">Contact</Link>\n            </nav>\n          </div>\n        </div>\n      </header>\n\n      {/* Page Banner */}\n      <section className=\"bg-gradient-to-r from-blue-500 to-purple-600 text-white py-16\">\n        <div className=\"container mx-auto px-4 text-center\">\n          <h1 className=\"text-4xl font-bold mb-4\">Our Classes</h1>\n          <nav className=\"text-blue-200\">\n            <Link href=\"/\" className=\"hover:text-white\">Home</Link>\n            <span className=\"mx-2\">/</span>\n            <span>Classes</span>\n          </nav>\n        </div>\n      </section>\n\n      {/* Classes Grid */}\n      <section className=\"py-16\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-4xl font-bold mb-4 text-gray-800\">Available Classes</h2>\n            <p className=\"text-gray-600 max-w-2xl mx-auto\">\n              Discover our comprehensive range of classes designed to nurture your child's development\n            </p>\n          </div>\n\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-2 gap-8\">\n            {classes.map((classItem) => (\n              <div key={classItem.id} className=\"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow\">\n                <img \n                  src={classItem.image} \n                  alt={classItem.title}\n                  className=\"w-full h-48 object-cover\"\n                />\n                <div className=\"p-6\">\n                  <h3 className=\"text-xl font-bold mb-2 text-gray-800\">{classItem.title}</h3>\n                  <div className=\"flex justify-between text-sm text-gray-500 mb-3\">\n                    <span>Age: {classItem.age}</span>\n                    <span>Time: {classItem.time}</span>\n                  </div>\n                  <p className=\"text-gray-600 mb-4\">{classItem.description}</p>\n                  <Link \n                    href={`/classes/${classItem.id}`}\n                    className=\"bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors inline-block\"\n                  >\n                    Learn More\n                  </Link>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"bg-blue-600 text-white py-16\">\n        <div className=\"container mx-auto px-4 text-center\">\n          <h2 className=\"text-3xl font-bold mb-4\">Ready to Enroll Your Child?</h2>\n          <p className=\"text-xl mb-8 text-blue-100\">\n            Join our community of happy families and give your child the best start in life\n          </p>\n          <Link \n            href=\"/contact\" \n            className=\"bg-yellow-400 text-blue-900 px-8 py-3 rounded-lg font-semibold hover:bg-yellow-300 transition-colors\"\n          >\n            Contact Us Today\n          </Link>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"bg-gray-800 text-white py-8\">\n        <div className=\"container mx-auto px-4 text-center\">\n          <div className=\"mb-4\">\n            <Link href=\"/\" className=\"text-2xl font-bold text-blue-400\">\n              Kinco School\n            </Link>\n          </div>\n          <p className=\"text-gray-400 mb-4\">\n            Providing quality education and care for your children\n          </p>\n          <div className=\"flex justify-center space-x-6\">\n            <Link href=\"/about\" className=\"text-gray-400 hover:text-white\">About</Link>\n            <Link href=\"/classes\" className=\"text-white\">Classes</Link>\n            <Link href=\"/contact\" className=\"text-gray-400 hover:text-white\">Contact</Link>\n          </div>\n          <div className=\"mt-6 pt-6 border-t border-gray-700\">\n            <p className=\"text-gray-400\">© 2024 Kinco School. All rights reserved.</p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,MAAM,UAAU;QACd;YACE,IAAI;YACJ,OAAO;YACP,KAAK;YACL,MAAM;YACN,aAAa;YACb,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,KAAK;YACL,MAAM;YACN,aAAa;YACb,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,KAAK;YACL,MAAM;YACN,aAAa;YACb,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,KAAK;YACL,MAAM;YACN,aAAa;YACb,OAAO;QACT;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CAAmC;;;;;;0CAG5D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAAoC;;;;;;kDAClE,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAA8B;;;;;;kDAC9D,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO5E,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA0B;;;;;;sCACxC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAmB;;;;;;8CAC5C,8OAAC;oCAAK,WAAU;8CAAO;;;;;;8CACvB,8OAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;0BAMZ,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAE,WAAU;8CAAkC;;;;;;;;;;;;sCAKjD,8OAAC;4BAAI,WAAU;sCACZ,QAAQ,GAAG,CAAC,CAAC,0BACZ,8OAAC;oCAAuB,WAAU;;sDAChC,8OAAC;4CACC,KAAK,UAAU,KAAK;4CACpB,KAAK,UAAU,KAAK;4CACpB,WAAU;;;;;;sDAEZ,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAwC,UAAU,KAAK;;;;;;8DACrE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;gEAAK;gEAAM,UAAU,GAAG;;;;;;;sEACzB,8OAAC;;gEAAK;gEAAO,UAAU,IAAI;;;;;;;;;;;;;8DAE7B,8OAAC;oDAAE,WAAU;8DAAsB,UAAU,WAAW;;;;;;8DACxD,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,CAAC,SAAS,EAAE,UAAU,EAAE,EAAE;oDAChC,WAAU;8DACX;;;;;;;;;;;;;mCAhBK,UAAU,EAAE;;;;;;;;;;;;;;;;;;;;;0BA2B9B,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA0B;;;;;;sCACxC,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAG1C,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;;;;;;0BAOL,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CAAmC;;;;;;;;;;;sCAI9D,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAGlC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAAiC;;;;;;8CAC/D,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAa;;;;;;8CAC7C,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAiC;;;;;;;;;;;;sCAEnE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMzC", "debugId": null}}]}