{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kinco-day-care-kindergarten-react-template-30-06-2025/Kinco/React%20Template/kinco-modern/src/app/page.js"], "sourcesContent": ["'use client';\n\nimport { useEffect } from \"react\";\nimport <PERSON> from \"next/link\";\nimport { Swiper, SwiperSlide } from \"swiper/react\";\nimport { Navigation, Pagination, Autoplay } from \"swiper/modules\";\n\nexport default function Home() {\n  useEffect(() => {\n    // Basic setup\n    document.querySelector(\"body\").className = \"main\";\n  }, []);\n\n  const programs = [\n    {\n      id: 1,\n      title: \"Drawing & Painting\",\n      description: \"Creative art classes to develop imagination and fine motor skills\",\n      image: \"/assets/images/common/sc-program1.jpg\"\n    },\n    {\n      id: 2,\n      title: \"Computer Learning\",\n      description: \"Introduction to basic computer skills and digital literacy\",\n      image: \"/assets/images/common/sc-program2.jpg\"\n    },\n    {\n      id: 3,\n      title: \"Basic English JR\",\n      description: \"Foundation English language learning through fun activities\",\n      image: \"/assets/images/common/sc-program3.jpg\"\n    },\n    {\n      id: 4,\n      title: \"Music & Dance\",\n      description: \"Develop rhythm, coordination and musical appreciation\",\n      image: \"/assets/images/common/sc-program4.jpg\"\n    }\n  ];\n\n  return (\n    <div className=\"min-h-screen\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm fixed w-full top-0 z-50\">\n        <div className=\"container mx-auto px-4 py-4\">\n          <div className=\"flex justify-between items-center\">\n            <Link href=\"/\" className=\"flex items-center\">\n              <img src=\"/assets/images/logo/logodark-2.png\" alt=\"Kinco School\" className=\"h-12\" />\n            </Link>\n            <nav className=\"hidden md:flex space-x-6\">\n              <Link href=\"/\" className=\"text-blue-600 font-semibold\">Home</Link>\n              <Link href=\"/about\" className=\"text-gray-600 hover:text-blue-600 transition-colors\">About</Link>\n              <Link href=\"/classes\" className=\"text-gray-600 hover:text-blue-600 transition-colors\">Classes</Link>\n              <Link href=\"/program\" className=\"text-gray-600 hover:text-blue-600 transition-colors\">Programs</Link>\n              <Link href=\"/teacher\" className=\"text-gray-600 hover:text-blue-600 transition-colors\">Teachers</Link>\n              <Link href=\"/gallery\" className=\"text-gray-600 hover:text-blue-600 transition-colors\">Gallery</Link>\n              <Link href=\"/blog\" className=\"text-gray-600 hover:text-blue-600 transition-colors\">Blog</Link>\n              <Link href=\"/events\" className=\"text-gray-600 hover:text-blue-600 transition-colors\">Events</Link>\n              <Link href=\"/contact\" className=\"text-gray-600 hover:text-blue-600 transition-colors\">Contact</Link>\n            </nav>\n            <div className=\"md:hidden\">\n              <button className=\"text-gray-600\">\n                <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n                </svg>\n              </button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Hero Slider */}\n      <section className=\"relative pt-20\">\n        <Swiper\n          modules={[Navigation, Pagination, Autoplay]}\n          spaceBetween={0}\n          slidesPerView={1}\n          navigation\n          pagination={{ clickable: true }}\n          autoplay={{ delay: 5000 }}\n          loop={true}\n          className=\"h-screen\"\n        >\n          <SwiperSlide>\n            <div className=\"relative h-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center\">\n              <div className=\"absolute inset-0 bg-black opacity-20\"></div>\n              <div className=\"container mx-auto px-4 relative z-10\">\n                <div className=\"grid md:grid-cols-2 gap-12 items-center text-white\">\n                  <div>\n                    <p className=\"text-yellow-300 font-semibold mb-4\">We Care Child Study</p>\n                    <h1 className=\"text-5xl md:text-6xl font-bold mb-6\">\n                      Start Learning With <span className=\"text-yellow-300\">Kinco School</span>\n                    </h1>\n                    <ul className=\"mb-8 space-y-2\">\n                      <li className=\"flex items-center\">\n                        <span className=\"text-green-400 mr-3\">✓</span>\n                        Outdoor Games\n                      </li>\n                      <li className=\"flex items-center\">\n                        <span className=\"text-green-400 mr-3\">✓</span>\n                        Sport Activities\n                      </li>\n                      <li className=\"flex items-center\">\n                        <span className=\"text-green-400 mr-3\">✓</span>\n                        Nutritious Foods\n                      </li>\n                    </ul>\n                    <div className=\"space-x-4\">\n                      <Link href=\"/contact\" className=\"bg-yellow-400 text-blue-900 px-8 py-3 rounded-lg font-semibold hover:bg-yellow-300 transition-colors\">\n                        Contact Us\n                      </Link>\n                      <Link href=\"/about\" className=\"border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors\">\n                        Learn More\n                      </Link>\n                    </div>\n                  </div>\n                  <div className=\"text-center\">\n                    <img src=\"/assets/images/common/slider-1.png\" alt=\"Children Learning\" className=\"max-w-full h-auto\" />\n                  </div>\n                </div>\n              </div>\n            </div>\n          </SwiperSlide>\n\n          <SwiperSlide>\n            <div className=\"relative h-full bg-gradient-to-r from-green-500 to-blue-600 flex items-center\">\n              <div className=\"absolute inset-0 bg-black opacity-20\"></div>\n              <div className=\"container mx-auto px-4 relative z-10\">\n                <div className=\"grid md:grid-cols-2 gap-12 items-center text-white\">\n                  <div>\n                    <p className=\"text-yellow-300 font-semibold mb-4\">Quality Education</p>\n                    <h1 className=\"text-5xl md:text-6xl font-bold mb-6\">\n                      Best Care For Your <span className=\"text-yellow-300\">Little Ones</span>\n                    </h1>\n                    <p className=\"text-xl mb-8 leading-relaxed\">\n                      Providing a safe, nurturing environment where children can learn, grow, and develop their full potential through innovative teaching methods.\n                    </p>\n                    <div className=\"space-x-4\">\n                      <Link href=\"/contact\" className=\"bg-yellow-400 text-blue-900 px-8 py-3 rounded-lg font-semibold hover:bg-yellow-300 transition-colors\">\n                        Contact Us\n                      </Link>\n                      <Link href=\"/program\" className=\"border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors\">\n                        Our Programs\n                      </Link>\n                    </div>\n                  </div>\n                  <div className=\"text-center\">\n                    <img src=\"/assets/images/common/slider-2.jpg\" alt=\"Happy Children\" className=\"max-w-full h-auto rounded-lg\" />\n                  </div>\n                </div>\n              </div>\n            </div>\n          </SwiperSlide>\n        </Swiper>\n      </section>\n\n      {/* Discovery/Features Section */}\n      <section className=\"py-16 bg-gray-50\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-4xl font-bold mb-4 text-gray-800\">Why Choose Kinco School?</h2>\n            <p className=\"text-gray-600 max-w-2xl mx-auto\">\n              We offer comprehensive programs designed to nurture your child's development\n            </p>\n          </div>\n          <div className=\"grid md:grid-cols-4 gap-8\">\n            <div className=\"bg-white p-6 rounded-lg shadow-md text-center hover:shadow-lg transition-shadow\">\n              <div className=\"text-blue-600 text-5xl mb-4\">🎮</div>\n              <h3 className=\"text-xl font-semibold mb-3 text-gray-800\">Study & Game</h3>\n              <p className=\"text-gray-600 mb-4\">Sed ut perspiciatis unde omnis iste natu voluptatem accus antium dolorem</p>\n              <Link href=\"/classes\" className=\"text-blue-600 hover:text-blue-800 font-semibold\">Read More →</Link>\n            </div>\n            <div className=\"bg-white p-6 rounded-lg shadow-md text-center hover:shadow-lg transition-shadow\">\n              <div className=\"text-blue-600 text-5xl mb-4\">📚</div>\n              <h3 className=\"text-xl font-semibold mb-3 text-gray-800\">A to Z Programs</h3>\n              <p className=\"text-gray-600 mb-4\">Sed ut perspiciatis unde omnis iste natu voluptatem accus antium dolorem</p>\n              <Link href=\"/program\" className=\"text-blue-600 hover:text-blue-800 font-semibold\">Read More →</Link>\n            </div>\n            <div className=\"bg-white p-6 rounded-lg shadow-md text-center hover:shadow-lg transition-shadow\">\n              <div className=\"text-blue-600 text-5xl mb-4\">👨‍🏫</div>\n              <h3 className=\"text-xl font-semibold mb-3 text-gray-800\">Expert Teacher</h3>\n              <p className=\"text-gray-600 mb-4\">Sed ut perspiciatis unde omnis iste natu voluptatem accus antium dolorem</p>\n              <Link href=\"/teacher\" className=\"text-blue-600 hover:text-blue-800 font-semibold\">Read More →</Link>\n            </div>\n            <div className=\"bg-white p-6 rounded-lg shadow-md text-center hover:shadow-lg transition-shadow\">\n              <div className=\"text-blue-600 text-5xl mb-4\">🏆</div>\n              <h3 className=\"text-xl font-semibold mb-3 text-gray-800\">Best Awards</h3>\n              <p className=\"text-gray-600 mb-4\">Sed ut perspiciatis unde omnis iste natu voluptatem accus antium dolorem</p>\n              <Link href=\"/about\" className=\"text-blue-600 hover:text-blue-800 font-semibold\">Read More →</Link>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* About Section */}\n      <section className=\"py-16\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"grid md:grid-cols-2 gap-12 items-center\">\n            <div>\n              <div className=\"flex items-center mb-4\">\n                <div className=\"text-blue-600 text-2xl mr-3\">🎓</div>\n                <p className=\"text-blue-600 font-semibold\">About Kinco</p>\n              </div>\n              <h2 className=\"text-4xl font-bold mb-6 text-gray-800\">\n                We Provide The Best Education For Your Children\n              </h2>\n              <p className=\"text-gray-600 mb-6 leading-relaxed\">\n                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut elit tellus, luctus nec ullamcorper mattis, pulvinar dapibus leo. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.\n              </p>\n              <p className=\"text-gray-600 mb-8 leading-relaxed\">\n                Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.\n              </p>\n              <Link href=\"/about\" className=\"bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors\">\n                Learn More\n              </Link>\n            </div>\n            <div className=\"relative\">\n              <img\n                src=\"/assets/images/common/sc-about1.jpg\"\n                alt=\"About Kinco\"\n                className=\"rounded-lg shadow-lg w-full\"\n              />\n              <div className=\"absolute -bottom-6 -right-6 bg-yellow-400 text-blue-900 p-6 rounded-lg shadow-lg\">\n                <div className=\"text-3xl font-bold\">15+</div>\n                <div className=\"text-sm font-semibold\">Years Experience</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Latest Programs Section */}\n      <section className=\"py-16 bg-gray-50\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"text-center mb-12\">\n            <div className=\"flex items-center justify-center mb-4\">\n              <div className=\"text-blue-600 text-2xl mr-3\">📖</div>\n              <p className=\"text-blue-600 font-semibold\">Latest Programs</p>\n            </div>\n            <h2 className=\"text-4xl font-bold mb-4 text-gray-800\">Our Latest Programs</h2>\n            <p className=\"text-gray-600 max-w-2xl mx-auto\">\n              Discover our comprehensive range of educational programs designed to nurture your child's development\n            </p>\n          </div>\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-8\">\n            {programs.map((program) => (\n              <div key={program.id} className=\"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow\">\n                <img\n                  src={program.image}\n                  alt={program.title}\n                  className=\"w-full h-48 object-cover\"\n                />\n                <div className=\"p-6\">\n                  <h3 className=\"text-xl font-bold mb-3 text-gray-800\">{program.title}</h3>\n                  <p className=\"text-gray-600 mb-4\">{program.description}</p>\n                  <Link\n                    href=\"/classes\"\n                    className=\"text-blue-600 hover:text-blue-800 font-semibold\"\n                  >\n                    Learn More →\n                  </Link>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Statistics Section */}\n      <section className=\"py-16 bg-blue-600 text-white\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"grid md:grid-cols-4 gap-8 text-center\">\n            <div>\n              <div className=\"text-4xl font-bold mb-2\">500+</div>\n              <div className=\"text-blue-200\">Happy Students</div>\n            </div>\n            <div>\n              <div className=\"text-4xl font-bold mb-2\">50+</div>\n              <div className=\"text-blue-200\">Expert Teachers</div>\n            </div>\n            <div>\n              <div className=\"text-4xl font-bold mb-2\">15+</div>\n              <div className=\"text-blue-200\">Years Experience</div>\n            </div>\n            <div>\n              <div className=\"text-4xl font-bold mb-2\">25+</div>\n              <div className=\"text-blue-200\">Awards Won</div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"py-16\">\n        <div className=\"container mx-auto px-4 text-center\">\n          <h2 className=\"text-4xl font-bold mb-4 text-gray-800\">Ready to Enroll Your Child?</h2>\n          <p className=\"text-xl mb-8 text-gray-600 max-w-2xl mx-auto\">\n            Join our community of happy families and give your child the best start in life with our comprehensive educational programs.\n          </p>\n          <div className=\"space-x-4\">\n            <Link\n              href=\"/contact\"\n              className=\"bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors\"\n            >\n              Contact Us Today\n            </Link>\n            <Link\n              href=\"/about\"\n              className=\"border-2 border-blue-600 text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-600 hover:text-white transition-colors\"\n            >\n              Learn More\n            </Link>\n          </div>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"bg-gray-800 text-white py-12\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"grid md:grid-cols-4 gap-8\">\n            <div>\n              <img src=\"/assets/images/logo/logofootert.png\" alt=\"Kinco School\" className=\"h-12 mb-4\" />\n              <p className=\"text-gray-400 mb-4\">\n                Providing quality education and care for your children in a safe, nurturing environment.\n              </p>\n              <div className=\"flex space-x-4\">\n                <a href=\"#\" className=\"text-gray-400 hover:text-white\">\n                  <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d=\"M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z\"/>\n                  </svg>\n                </a>\n                <a href=\"#\" className=\"text-gray-400 hover:text-white\">\n                  <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d=\"M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z\"/>\n                  </svg>\n                </a>\n              </div>\n            </div>\n            <div>\n              <h3 className=\"text-lg font-semibold mb-4\">Quick Links</h3>\n              <ul className=\"space-y-2\">\n                <li><Link href=\"/about\" className=\"text-gray-400 hover:text-white\">About Us</Link></li>\n                <li><Link href=\"/classes\" className=\"text-gray-400 hover:text-white\">Classes</Link></li>\n                <li><Link href=\"/program\" className=\"text-gray-400 hover:text-white\">Programs</Link></li>\n                <li><Link href=\"/teacher\" className=\"text-gray-400 hover:text-white\">Teachers</Link></li>\n              </ul>\n            </div>\n            <div>\n              <h3 className=\"text-lg font-semibold mb-4\">Programs</h3>\n              <ul className=\"space-y-2\">\n                <li><a href=\"#\" className=\"text-gray-400 hover:text-white\">Drawing & Painting</a></li>\n                <li><a href=\"#\" className=\"text-gray-400 hover:text-white\">Computer Learning</a></li>\n                <li><a href=\"#\" className=\"text-gray-400 hover:text-white\">Basic English</a></li>\n                <li><a href=\"#\" className=\"text-gray-400 hover:text-white\">Music & Dance</a></li>\n              </ul>\n            </div>\n            <div>\n              <h3 className=\"text-lg font-semibold mb-4\">Contact Info</h3>\n              <div className=\"space-y-2 text-gray-400\">\n                <p>📍 123 Education Street, Learning City</p>\n                <p>📞 (555) 123-4567</p>\n                <p>✉️ <EMAIL></p>\n                <p>🕒 Mon-Fri: 7AM-6PM</p>\n              </div>\n            </div>\n          </div>\n          <div className=\"border-t border-gray-700 mt-8 pt-8 text-center\">\n            <p className=\"text-gray-400\">© 2024 Kinco School. All rights reserved.</p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;AALA;;;;;AAOe,SAAS;;IACtB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,cAAc;YACd,SAAS,aAAa,CAAC,QAAQ,SAAS,GAAG;QAC7C;yBAAG,EAAE;IAEL,MAAM,WAAW;QACf;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;QACT;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CACvB,cAAA,6LAAC;oCAAI,KAAI;oCAAqC,KAAI;oCAAe,WAAU;;;;;;;;;;;0CAE7E,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDAA8B;;;;;;kDACvD,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAAsD;;;;;;kDACpF,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAsD;;;;;;kDACtF,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAsD;;;;;;kDACtF,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAsD;;;;;;kDACtF,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAsD;;;;;;kDACtF,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAQ,WAAU;kDAAsD;;;;;;kDACnF,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAU,WAAU;kDAAsD;;;;;;kDACrF,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAsD;;;;;;;;;;;;0CAExF,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAO,WAAU;8CAChB,cAAA,6LAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjE,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASjF,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC,6IAAA,CAAA,SAAM;oBACL,SAAS;wBAAC,4LAAA,CAAA,aAAU;wBAAE,4LAAA,CAAA,aAAU;wBAAE,wLAAA,CAAA,WAAQ;qBAAC;oBAC3C,cAAc;oBACd,eAAe;oBACf,UAAU;oBACV,YAAY;wBAAE,WAAW;oBAAK;oBAC9B,UAAU;wBAAE,OAAO;oBAAK;oBACxB,MAAM;oBACN,WAAU;;sCAEV,6LAAC,6IAAA,CAAA,cAAW;sCACV,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAAqC;;;;;;sEAClD,6LAAC;4DAAG,WAAU;;gEAAsC;8EAC9B,6LAAC;oEAAK,WAAU;8EAAkB;;;;;;;;;;;;sEAExD,6LAAC;4DAAG,WAAU;;8EACZ,6LAAC;oEAAG,WAAU;;sFACZ,6LAAC;4EAAK,WAAU;sFAAsB;;;;;;wEAAQ;;;;;;;8EAGhD,6LAAC;oEAAG,WAAU;;sFACZ,6LAAC;4EAAK,WAAU;sFAAsB;;;;;;wEAAQ;;;;;;;8EAGhD,6LAAC;oEAAG,WAAU;;sFACZ,6LAAC;4EAAK,WAAU;sFAAsB;;;;;;wEAAQ;;;;;;;;;;;;;sEAIlD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,+JAAA,CAAA,UAAI;oEAAC,MAAK;oEAAW,WAAU;8EAAuG;;;;;;8EAGvI,6LAAC,+JAAA,CAAA,UAAI;oEAAC,MAAK;oEAAS,WAAU;8EAA2H;;;;;;;;;;;;;;;;;;8DAK7J,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,KAAI;wDAAqC,KAAI;wDAAoB,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO1F,6LAAC,6IAAA,CAAA,cAAW;sCACV,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAAqC;;;;;;sEAClD,6LAAC;4DAAG,WAAU;;gEAAsC;8EAC/B,6LAAC;oEAAK,WAAU;8EAAkB;;;;;;;;;;;;sEAEvD,6LAAC;4DAAE,WAAU;sEAA+B;;;;;;sEAG5C,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,+JAAA,CAAA,UAAI;oEAAC,MAAK;oEAAW,WAAU;8EAAuG;;;;;;8EAGvI,6LAAC,+JAAA,CAAA,UAAI;oEAAC,MAAK;oEAAW,WAAU;8EAA2H;;;;;;;;;;;;;;;;;;8DAK/J,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,KAAI;wDAAqC,KAAI;wDAAiB,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU3F,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,6LAAC;oCAAE,WAAU;8CAAkC;;;;;;;;;;;;sCAIjD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAA8B;;;;;;sDAC7C,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAClC,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;sDAAkD;;;;;;;;;;;;8CAEpF,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAA8B;;;;;;sDAC7C,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAClC,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;sDAAkD;;;;;;;;;;;;8CAEpF,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAA8B;;;;;;sDAC7C,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAClC,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;sDAAkD;;;;;;;;;;;;8CAEpF,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAA8B;;;;;;sDAC7C,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAClC,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAS,WAAU;sDAAkD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOxF,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAA8B;;;;;;0DAC7C,6LAAC;gDAAE,WAAU;0DAA8B;;;;;;;;;;;;kDAE7C,6LAAC;wCAAG,WAAU;kDAAwC;;;;;;kDAGtD,6LAAC;wCAAE,WAAU;kDAAqC;;;;;;kDAGlD,6LAAC;wCAAE,WAAU;kDAAqC;;;;;;kDAGlD,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAAgG;;;;;;;;;;;;0CAIhI,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,KAAI;wCACJ,KAAI;wCACJ,WAAU;;;;;;kDAEZ,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAqB;;;;;;0DACpC,6LAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQjD,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAA8B;;;;;;sDAC7C,6LAAC;4CAAE,WAAU;sDAA8B;;;;;;;;;;;;8CAE7C,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,6LAAC;oCAAE,WAAU;8CAAkC;;;;;;;;;;;;sCAIjD,6LAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;oCAAqB,WAAU;;sDAC9B,6LAAC;4CACC,KAAK,QAAQ,KAAK;4CAClB,KAAK,QAAQ,KAAK;4CAClB,WAAU;;;;;;sDAEZ,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAwC,QAAQ,KAAK;;;;;;8DACnE,6LAAC;oDAAE,WAAU;8DAAsB,QAAQ,WAAW;;;;;;8DACtD,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;8DACX;;;;;;;;;;;;;mCAZK,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;0BAuB5B,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAI,WAAU;kDAA0B;;;;;;kDACzC,6LAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAEjC,6LAAC;;kDACC,6LAAC;wCAAI,WAAU;kDAA0B;;;;;;kDACzC,6LAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAEjC,6LAAC;;kDACC,6LAAC;wCAAI,WAAU;kDAA0B;;;;;;kDACzC,6LAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAEjC,6LAAC;;kDACC,6LAAC;wCAAI,WAAU;kDAA0B;;;;;;kDACzC,6LAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOvC,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAE,WAAU;sCAA+C;;;;;;sCAG5D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;0BAQP,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAI,KAAI;4CAAsC,KAAI;4CAAe,WAAU;;;;;;sDAC5E,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAGlC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,MAAK;oDAAI,WAAU;8DACpB,cAAA,6LAAC;wDAAI,WAAU;wDAAU,MAAK;wDAAe,SAAQ;kEACnD,cAAA,6LAAC;4DAAK,GAAE;;;;;;;;;;;;;;;;8DAGZ,6LAAC;oDAAE,MAAK;oDAAI,WAAU;8DACpB,cAAA,6LAAC;wDAAI,WAAU;wDAAU,MAAK;wDAAe,SAAQ;kEACnD,cAAA,6LAAC;4DAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAKhB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAS,WAAU;kEAAiC;;;;;;;;;;;8DACnE,6LAAC;8DAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;kEAAiC;;;;;;;;;;;8DACrE,6LAAC;8DAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;kEAAiC;;;;;;;;;;;8DACrE,6LAAC;8DAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;kEAAiC;;;;;;;;;;;;;;;;;;;;;;;8CAGzE,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DAAG,cAAA,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAiC;;;;;;;;;;;8DAC3D,6LAAC;8DAAG,cAAA,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAiC;;;;;;;;;;;8DAC3D,6LAAC;8DAAG,cAAA,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAiC;;;;;;;;;;;8DAC3D,6LAAC;8DAAG,cAAA,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAiC;;;;;;;;;;;;;;;;;;;;;;;8CAG/D,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAE;;;;;;8DACH,6LAAC;8DAAE;;;;;;8DACH,6LAAC;8DAAE;;;;;;8DACH,6LAAC;8DAAE;;;;;;;;;;;;;;;;;;;;;;;;sCAIT,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMzC;GA9WwB;KAAA", "debugId": null}}]}