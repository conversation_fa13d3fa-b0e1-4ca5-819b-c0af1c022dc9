/* Swiper styles */
@import 'swiper/css';
@import 'swiper/css/navigation';
@import 'swiper/css/pagination';

@import "tailwindcss";

/* Theme Variables */
:root {
  --primary-color: #ff6b35;
  --primary-color2: #ffa726;
  --primary-color3: #2196f3;
  --secondary-color: #4caf50;
  --text-color: #333;
  --text-color-light: #666;
  --bg-color: #f8f9fa;
}

/* Base Styles */
body {
  font-family: 'Nunito', sans-serif;
  font-size: 16px;
  line-height: 1.6;
  color: var(--text-color);
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

* {
  box-sizing: border-box;
}

/* Typography Improvements */
h1, .h1 {
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1.2;
}

h2, .h2 {
  font-size: 2rem;
  font-weight: 600;
  line-height: 1.3;
}

h3, .h3 {
  font-size: 1.75rem;
  font-weight: 600;
  line-height: 1.3;
}

h4, .h4 {
  font-size: 1.5rem;
  font-weight: 500;
  line-height: 1.4;
}

h5, .h5 {
  font-size: 1.25rem;
  font-weight: 500;
  line-height: 1.4;
}

h6, .h6 {
  font-size: 1rem;
  font-weight: 500;
  line-height: 1.4;
}

.display-1 {
  font-size: 5rem;
  font-weight: 700;
  line-height: 1.1;
}

.display-2 {
  font-size: 4.5rem;
  font-weight: 700;
  line-height: 1.1;
}

.display-3 {
  font-size: 4rem;
  font-weight: 700;
  line-height: 1.1;
}

.display-4 {
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.1;
}

.display-5 {
  font-size: 3rem;
  font-weight: 700;
  line-height: 1.1;
}

.lead {
  font-size: 1.25rem;
  font-weight: 300;
  line-height: 1.6;
}

.fs-1 { font-size: 2.5rem !important; }
.fs-2 { font-size: 2rem !important; }
.fs-3 { font-size: 1.75rem !important; }
.fs-4 { font-size: 1.5rem !important; }
.fs-5 { font-size: 1.25rem !important; }
.fs-6 { font-size: 1rem !important; }

/* Theme Integration */
.tf-slider-1 {
  position: relative;
  overflow: hidden;
}

.tf-slider-1 .overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 1;
}

.tf-slider-1 .container-fluid {
  position: relative;
  z-index: 2;
}

.item-slider-1 {
  min-height: 100vh;
  display: flex;
  align-items: center;
  background-size: cover;
  background-position: center;
  position: relative;
}

.box-content {
  padding: 2rem 0;
}

.sub {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.title {
  font-size: 3.5rem;
  font-weight: 800;
  line-height: 1.2;
  margin-bottom: 1rem;
}

.box-custom .wrap {
  font-size: 3.5rem;
  font-weight: 800;
  line-height: 1.2;
}

.des {
  font-size: 1.1rem;
  line-height: 1.8;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.tf-btn {
  display: inline-block;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.tf-btn.style-1 {
  background: var(--primary-color);
  color: white;
}

.tf-btn.style-1:hover {
  background: transparent;
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.tf-btn.style-2 {
  background: var(--primary-color3);
  color: white;
}

.tf-btn.style-2:hover {
  background: transparent;
  border-color: var(--primary-color3);
  color: var(--primary-color3);
}

/* Color Classes */
.clr-pri-1 {
  color: var(--primary-color) !important;
}

.clr-pri-2 {
  color: white !important;
}

.clr-pri-3 {
  color: var(--primary-color3) !important;
}

/* Section Styles */
.tf-section {
  padding: 5rem 0;
}

.tf-about-us-1 .about-us-1 {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  align-items: center;
}

.box-sub-tag {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.sub-tag-icon {
  margin-right: 1rem;
  font-size: 1.5rem;
  color: var(--primary-color3);
}

/* Responsive Typography */
@media (max-width: 1200px) {
  .display-1 { font-size: 4rem; }
  .display-2 { font-size: 3.5rem; }
  .display-3 { font-size: 3rem; }
  .display-4 { font-size: 2.5rem; }
  .display-5 { font-size: 2rem; }

  h1, .h1 { font-size: 2rem; }
  h2, .h2 { font-size: 1.75rem; }
  h3, .h3 { font-size: 1.5rem; }
}

@media (max-width: 768px) {
  html { font-size: 14px; }
  body { font-size: 14px; }

  .display-1 { font-size: 3rem; }
  .display-2 { font-size: 2.75rem; }
  .display-3 { font-size: 2.5rem; }
  .display-4 { font-size: 2rem; }
  .display-5 { font-size: 1.75rem; }

  h1, .h1 { font-size: 1.75rem; }
  h2, .h2 { font-size: 1.5rem; }
  h3, .h3 { font-size: 1.25rem; }
  h4, .h4 { font-size: 1.125rem; }
  h5, .h5 { font-size: 1rem; }

  .lead { font-size: 1.125rem; }

  .title, .box-custom .wrap {
    font-size: 2rem;
  }

  .tf-about-us-1 .about-us-1 {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
}

@media (max-width: 576px) {
  html { font-size: 13px; }
  body { font-size: 13px; }

  .display-1 { font-size: 2.5rem; }
  .display-2 { font-size: 2.25rem; }
  .display-3 { font-size: 2rem; }
  .display-4 { font-size: 1.75rem; }
  .display-5 { font-size: 1.5rem; }

  h1, .h1 { font-size: 1.5rem; }
  h2, .h2 { font-size: 1.25rem; }
  h3, .h3 { font-size: 1.125rem; }
  h4, .h4 { font-size: 1rem; }
  h5, .h5 { font-size: 0.875rem; }

  .lead { font-size: 1rem; }
}

/* Swiper Overrides */
.swiper-button-next,
.swiper-button-prev {
  color: white !important;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  width: 50px !important;
  height: 50px !important;
}

.swiper-button-next:after,
.swiper-button-prev:after {
  font-size: 20px !important;
}

.swiper-pagination-bullet {
  background: rgba(255, 255, 255, 0.5) !important;
}

.swiper-pagination-bullet-active {
  background: white !important;
}

/* Hero Section Improvements */
.hero-section {
  position: relative;
  overflow: hidden;
}

.hero-slide {
  position: relative;
}

.hero-content {
  animation: fadeInUp 1s ease-out;
}

.hero-image-placeholder {
  animation: fadeInRight 1s ease-out 0.3s both;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Feature Cards */
.feature-card {
  transition: all 0.3s ease;
  border: 1px solid rgba(0,0,0,0.05);
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0,0,0,0.15) !important;
}

.icon-circle {
  transition: all 0.3s ease;
}

.feature-card:hover .icon-circle {
  transform: scale(1.1);
}

/* Program Cards */
.program-item {
  transition: all 0.3s ease;
}

.program-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0,0,0,0.15) !important;
}

.program-image-placeholder {
  transition: all 0.3s ease;
}

.program-item:hover .program-image-placeholder {
  transform: scale(1.05);
}

/* About Section */
.about-image-placeholder {
  transition: all 0.3s ease;
}

.about-image:hover .about-image-placeholder {
  transform: scale(1.02);
}

.experience-badge {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* Button Improvements */
.btn {
  transition: all 0.3s ease;
  font-weight: 500;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.btn-warning:hover {
  background-color: #e0a800;
  border-color: #d39e00;
}

/* Page Layout */
.page-content {
  padding-top: 80px;
}

.page-banner {
  padding-top: 120px;
  padding-bottom: 60px;
}

/* Statistics Section */
.tf-counter {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.counter-number {
  animation: countUp 2s ease-out;
}

@keyframes countUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Header Styles */
.header-style-1 {
  transition: all 0.3s ease;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: white !important;
  box-shadow: 0 2px 15px rgba(0,0,0,0.1);
  backdrop-filter: blur(10px);
}

.header-inner {
  padding: 1rem 0;
}

.header-logo img {
  max-height: 50px;
  width: auto;
}

.main-menu ul {
  margin: 0;
  padding: 0;
  list-style: none;
  display: flex;
  align-items: center;
}

.menu-item {
  margin: 0 0.25rem;
}

.menu-link {
  transition: all 0.3s ease;
  font-size: 0.95rem;
  font-weight: 500;
}

.menu-link:hover {
  background-color: var(--bs-primary) !important;
  color: white !important;
  transform: translateY(-1px);
}

.transition-all {
  transition: all 0.3s ease;
}

.hover-primary:hover {
  color: var(--bs-primary) !important;
}

/* Logo Styles */
.logo-placeholder {
  transition: all 0.3s ease;
}

.logo-placeholder:hover {
  transform: scale(1.05);
}

.logo-icon {
  font-size: 1.2rem;
}

.menu-link {
  padding: 0.5rem 1rem;
  text-decoration: none;
  font-weight: 500;
  color: #333;
  transition: all 0.3s ease;
  border-radius: 25px;
}

.menu-link:hover {
  color: var(--primary-color3) !important;
  background: rgba(33, 150, 243, 0.1);
}

.menu-link.fw-semibold.text-primary {
  background: var(--primary-color3);
  color: white !important;
}

.hover-primary:hover {
  color: var(--primary-color3) !important;
}

.mobile-menu-toggle {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #333;
}

/* Features Section */
.features-section {
  padding: 5rem 0;
}

.feature-card {
  transition: all 0.3s ease;
  border: 1px solid rgba(0,0,0,0.05);
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0,0,0,0.1) !important;
}

.feature-icon .icon-circle {
  transition: all 0.3s ease;
}

.feature-card:hover .icon-circle {
  transform: scale(1.1);
}

.feature-card .btn {
  transition: all 0.3s ease;
}

.feature-card:hover .btn {
  transform: translateY(-2px);
}

/* About Section */
.about-section {
  padding: 5rem 0;
}

.about-content {
  padding-right: 2rem;
}

.about-image {
  position: relative;
}

.experience-badge {
  backdrop-filter: blur(10px);
  border: 3px solid white;
}

.feature-item {
  font-weight: 500;
  margin-bottom: 0.5rem;
}

@media (max-width: 991px) {
  .about-content {
    padding-right: 0;
    text-align: center;
  }

  .experience-badge {
    position: static !important;
    display: inline-block;
    margin-top: 2rem;
  }
}

/* Program Items */
.program-item {
  transition: all 0.3s ease;
}

.program-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0,0,0,0.1) !important;
}

/* Counter Section */
.tf-counter {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Footer Styles */
.footer-style-2 {
  background: #1a1a1a !important;
}

.social-link {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255,255,255,0.1);
  transition: all 0.3s ease;
}

.social-link:hover {
  background: var(--primary-color3);
  color: white !important;
}

/* Utility Classes */
.list-check {
  list-style: none;
  padding: 0;
}

.list-check li {
  margin-bottom: 0.5rem;
}

/* Mobile Responsive */
@media (max-width: 991px) {
  .main-menu {
    display: none !important;
  }

  .mobile-menu-toggle {
    display: block !important;
  }
}

@media (min-width: 992px) {
  .mobile-menu-toggle {
    display: none !important;
  }
}

/* Hero Section Styles */
.hero-section {
  position: relative;
  overflow: hidden;
}

.hero-swiper {
  width: 100%;
  height: 100vh;
}

.hero-slide {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
}

.hero-content {
  padding: 2rem 0;
}

.hero-title {
  line-height: 1.2;
  margin-bottom: 1.5rem;
}

.hero-subtitle {
  font-size: 1.1rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.hero-description {
  font-size: 1.1rem;
  line-height: 1.6;
  opacity: 0.9;
}

.hero-features li {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.9);
}

.hero-buttons .btn {
  font-weight: 600;
  border-radius: 50px;
  transition: all 0.3s ease;
}

.hero-buttons .btn-warning:hover {
  background-color: #e0a800;
  transform: translateY(-2px);
}

.hero-buttons .btn-outline-light:hover {
  background-color: white;
  color: #333;
  transform: translateY(-2px);
}

/* Swiper Navigation Styling */
.hero-swiper .swiper-button-next,
.hero-swiper .swiper-button-prev {
  color: white;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  width: 50px;
  height: 50px;
  margin-top: -25px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.hero-swiper .swiper-button-next:hover,
.hero-swiper .swiper-button-prev:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.hero-swiper .swiper-button-next:after,
.hero-swiper .swiper-button-prev:after {
  font-size: 18px;
  font-weight: bold;
}

.hero-swiper .swiper-pagination {
  bottom: 30px;
}

.hero-swiper .swiper-pagination-bullet {
  background: rgba(255, 255, 255, 0.5);
  width: 12px;
  height: 12px;
  margin: 0 5px;
  transition: all 0.3s ease;
}

.hero-swiper .swiper-pagination-bullet-active {
  background: white;
  transform: scale(1.2);
}

/* Responsive Hero Section */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }

  .hero-content {
    text-align: center;
    margin-bottom: 2rem;
  }

  .hero-buttons {
    text-align: center;
  }

  .hero-buttons .btn {
    display: block;
    width: 100%;
    margin-bottom: 1rem;
  }

  .hero-buttons .btn:last-child {
    margin-bottom: 0;
  }
}
