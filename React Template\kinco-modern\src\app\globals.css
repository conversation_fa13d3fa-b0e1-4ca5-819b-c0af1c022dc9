/* Swiper styles */
@import 'swiper/css';
@import 'swiper/css/navigation';
@import 'swiper/css/pagination';

@import "tailwindcss";

/* Theme Variables */
:root {
  --primary-color: #ff6b35;
  --primary-color2: #ffa726;
  --primary-color3: #2196f3;
  --secondary-color: #4caf50;
  --text-color: #333;
  --text-color-light: #666;
  --bg-color: #f8f9fa;
}

/* Base Styles */
body {
  font-family: 'Nunito', sans-serif;
  line-height: 1.6;
  color: var(--text-color);
}

html {
  scroll-behavior: smooth;
}

* {
  box-sizing: border-box;
}

/* Theme Integration */
.tf-slider-1 {
  position: relative;
  overflow: hidden;
}

.tf-slider-1 .overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 1;
}

.tf-slider-1 .container-fluid {
  position: relative;
  z-index: 2;
}

.item-slider-1 {
  min-height: 100vh;
  display: flex;
  align-items: center;
  background-size: cover;
  background-position: center;
  position: relative;
}

.box-content {
  padding: 2rem 0;
}

.sub {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.title {
  font-size: 3.5rem;
  font-weight: 800;
  line-height: 1.2;
  margin-bottom: 1rem;
}

.box-custom .wrap {
  font-size: 3.5rem;
  font-weight: 800;
  line-height: 1.2;
}

.des {
  font-size: 1.1rem;
  line-height: 1.8;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.tf-btn {
  display: inline-block;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.tf-btn.style-1 {
  background: var(--primary-color);
  color: white;
}

.tf-btn.style-1:hover {
  background: transparent;
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.tf-btn.style-2 {
  background: var(--primary-color3);
  color: white;
}

.tf-btn.style-2:hover {
  background: transparent;
  border-color: var(--primary-color3);
  color: var(--primary-color3);
}

/* Color Classes */
.clr-pri-1 {
  color: var(--primary-color) !important;
}

.clr-pri-2 {
  color: white !important;
}

.clr-pri-3 {
  color: var(--primary-color3) !important;
}

/* Section Styles */
.tf-section {
  padding: 5rem 0;
}

.tf-about-us-1 .about-us-1 {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  align-items: center;
}

.box-sub-tag {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.sub-tag-icon {
  margin-right: 1rem;
  font-size: 1.5rem;
  color: var(--primary-color3);
}

/* Responsive */
@media (max-width: 768px) {
  .title, .box-custom .wrap {
    font-size: 2.5rem;
  }

  .tf-about-us-1 .about-us-1 {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
}

/* Swiper Overrides */
.swiper-button-next,
.swiper-button-prev {
  color: white !important;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  width: 50px !important;
  height: 50px !important;
}

.swiper-button-next:after,
.swiper-button-prev:after {
  font-size: 20px !important;
}

.swiper-pagination-bullet {
  background: rgba(255, 255, 255, 0.5) !important;
}

.swiper-pagination-bullet-active {
  background: white !important;
}

/* Header Styles */
.header-style-1 {
  transition: all 0.3s ease;
}

.menu-link:hover {
  color: var(--primary-color3) !important;
}

.hover-primary:hover {
  color: var(--primary-color3) !important;
}

/* Discovery Section */
.discovery-item {
  transition: all 0.3s ease;
}

.discovery-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0,0,0,0.1) !important;
}

/* Program Items */
.program-item {
  transition: all 0.3s ease;
}

.program-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0,0,0,0.1) !important;
}

/* Counter Section */
.tf-counter {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Footer Styles */
.footer-style-2 {
  background: #1a1a1a !important;
}

.social-link {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255,255,255,0.1);
  transition: all 0.3s ease;
}

.social-link:hover {
  background: var(--primary-color3);
  color: white !important;
}

/* Utility Classes */
.list-check {
  list-style: none;
  padding: 0;
}

.list-check li {
  margin-bottom: 0.5rem;
}

/* Mobile Responsive */
@media (max-width: 991px) {
  .main-menu {
    display: none !important;
  }

  .mobile-menu-toggle {
    display: block !important;
  }
}

@media (min-width: 992px) {
  .mobile-menu-toggle {
    display: none !important;
  }
}
