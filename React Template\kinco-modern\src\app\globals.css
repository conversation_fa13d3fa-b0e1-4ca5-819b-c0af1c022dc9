/* Swiper styles */
@import 'swiper/css';
@import 'swiper/css/navigation';
@import 'swiper/css/pagination';

@import "tailwindcss";

/* Custom overrides */
.inner-current-item a {
  color: var(--primary-color3) !important;
}

.owl-carousel {
  display: block !important;
}

.flat-accordion .flat-toggle .toggle-content {
  display: none;
}

.flat-accordion .flat-toggle.active .toggle-content {
  display: block;
}

/* Bootstrap and theme compatibility */
body {
  font-family: 'Nunito', sans-serif;
  line-height: 1.6;
}

/* Ensure proper styling for Next.js */
html {
  scroll-behavior: smooth;
}

* {
  box-sizing: border-box;
}
