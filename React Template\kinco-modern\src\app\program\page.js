'use client';

import Link from "next/link";

export default function Programs() {
  const programs = [
    {
      id: 1,
      title: "Toddler Program",
      age: "18 months - 3 years",
      duration: "Half Day / Full Day",
      description: "A nurturing environment for our youngest learners to explore, play, and develop basic social skills.",
      features: ["Sensory play activities", "Basic motor skill development", "Social interaction", "Potty training support"],
      image: "/assets/images/common/sc-program1.jpg"
    },
    {
      id: 2,
      title: "Preschool Program",
      age: "3 - 4 years",
      duration: "Half Day / Full Day",
      description: "Structured learning activities that prepare children for kindergarten while maintaining a fun, play-based approach.",
      features: ["Pre-literacy skills", "Basic math concepts", "Creative arts", "Science exploration"],
      image: "/assets/images/common/sc-program2.jpg"
    },
    {
      id: 3,
      title: "Pre-K Program",
      age: "4 - 5 years",
      duration: "Full Day",
      description: "Comprehensive kindergarten preparation program focusing on academic readiness and independence.",
      features: ["Reading readiness", "Math foundations", "Writing skills", "Critical thinking"],
      image: "/assets/images/common/sc-program3.jpg"
    },
    {
      id: 4,
      title: "After School Care",
      age: "5 - 12 years",
      duration: "3:00 PM - 6:00 PM",
      description: "Safe and engaging after-school program with homework help, activities, and nutritious snacks.",
      features: ["Homework assistance", "Recreational activities", "Healthy snacks", "Field trips"],
      image: "/assets/images/common/sc-program4.jpg"
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex justify-between items-center">
            <Link href="/" className="text-2xl font-bold text-blue-600">
              Kinco School
            </Link>
            <nav className="hidden md:flex space-x-6">
              <Link href="/about" className="text-gray-600 hover:text-blue-600">About</Link>
              <Link href="/classes" className="text-gray-600 hover:text-blue-600">Classes</Link>
              <Link href="/program" className="text-blue-600 font-semibold">Programs</Link>
              <Link href="/contact" className="text-gray-600 hover:text-blue-600">Contact</Link>
            </nav>
          </div>
        </div>
      </header>

      {/* Page Banner */}
      <section className="bg-gradient-to-r from-blue-500 to-purple-600 text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl font-bold mb-4">Our Programs</h1>
          <nav className="text-blue-200">
            <Link href="/" className="hover:text-white">Home</Link>
            <span className="mx-2">/</span>
            <span>Programs</span>
          </nav>
        </div>
      </section>

      {/* Programs Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold mb-4 text-gray-800">Educational Programs</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              We offer comprehensive programs designed to meet the developmental needs of children at every stage
            </p>
          </div>

          <div className="space-y-12">
            {programs.map((program, index) => (
              <div key={program.id} className={`grid md:grid-cols-2 gap-8 items-center ${index % 2 === 1 ? 'md:grid-flow-col-dense' : ''}`}>
                <div className={index % 2 === 1 ? 'md:col-start-2' : ''}>
                  <img 
                    src={program.image} 
                    alt={program.title}
                    className="w-full h-64 object-cover rounded-lg shadow-md"
                  />
                </div>
                <div className={index % 2 === 1 ? 'md:col-start-1' : ''}>
                  <div className="bg-white p-8 rounded-lg shadow-md">
                    <h3 className="text-2xl font-bold mb-2 text-gray-800">{program.title}</h3>
                    <div className="flex flex-wrap gap-4 text-sm text-gray-500 mb-4">
                      <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full">Age: {program.age}</span>
                      <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full">{program.duration}</span>
                    </div>
                    <p className="text-gray-600 mb-6">{program.description}</p>
                    <div className="mb-6">
                      <h4 className="font-semibold text-gray-800 mb-3">Program Features:</h4>
                      <ul className="grid grid-cols-2 gap-2">
                        {program.features.map((feature, idx) => (
                          <li key={idx} className="flex items-center text-sm text-gray-600">
                            <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                            {feature}
                          </li>
                        ))}
                      </ul>
                    </div>
                    <Link 
                      href={`/program/${program.id}`}
                      className="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700 transition-colors inline-block"
                    >
                      Learn More
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Enrollment CTA */}
      <section className="bg-blue-600 text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">Ready to Enroll?</h2>
          <p className="text-xl mb-8 text-blue-100">
            Give your child the best start in life with our comprehensive educational programs
          </p>
          <div className="space-x-4">
            <Link 
              href="/contact" 
              className="bg-yellow-400 text-blue-900 px-8 py-3 rounded-lg font-semibold hover:bg-yellow-300 transition-colors"
            >
              Contact Us
            </Link>
            <Link 
              href="/about" 
              className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors"
            >
              Learn More
            </Link>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-800 text-white py-8">
        <div className="container mx-auto px-4 text-center">
          <div className="mb-4">
            <Link href="/" className="text-2xl font-bold text-blue-400">
              Kinco School
            </Link>
          </div>
          <p className="text-gray-400 mb-4">
            Providing quality education and care for your children
          </p>
          <div className="flex justify-center space-x-6">
            <Link href="/about" className="text-gray-400 hover:text-white">About</Link>
            <Link href="/classes" className="text-gray-400 hover:text-white">Classes</Link>
            <Link href="/program" className="text-white">Programs</Link>
            <Link href="/contact" className="text-gray-400 hover:text-white">Contact</Link>
          </div>
          <div className="mt-6 pt-6 border-t border-gray-700">
            <p className="text-gray-400">© 2024 Kinco School. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
