'use client';

import Link from "next/link";
import Header from "../../components/Header";
import Footer from "../../components/Footer";

export default function Events() {
  const upcomingEvents = [
    {
      id: 1,
      title: "Annual Science Fair",
      date: "January 15, 2025",
      time: "9:00 AM - 3:00 PM",
      location: "Main Auditorium",
      description: "Students will showcase their science projects and experiments. Parents and families are welcome to attend.",
      image: "/assets/images/common/sc-event1.jpg",
      category: "Academic"
    },
    {
      id: 2,
      title: "Winter Art Exhibition",
      date: "January 22, 2025", 
      time: "10:00 AM - 2:00 PM",
      location: "Art Gallery",
      description: "Display of student artwork created during the winter semester. Refreshments will be provided.",
      image: "/assets/images/common/sc-event2.jpg",
      category: "Arts"
    },
    {
      id: 3,
      title: "Parent-Teacher Conference",
      date: "February 5, 2025",
      time: "1:00 PM - 6:00 PM", 
      location: "Individual Classrooms",
      description: "Meet with your child's teacher to discuss progress and development. Please schedule your appointment.",
      image: "/assets/images/common/sc-event3.jpg",
      category: "Academic"
    }
  ];

  const pastEvents = [
    {
      id: 4,
      title: "Holiday Concert",
      date: "December 20, 2024",
      time: "7:00 PM",
      location: "Main Auditorium", 
      description: "Students performed holiday songs and showcased their musical talents.",
      image: "/assets/images/common/sc-program1.jpg",
      category: "Music"
    },
    {
      id: 5,
      title: "Thanksgiving Feast",
      date: "November 25, 2024",
      time: "11:30 AM - 1:00 PM",
      location: "Cafeteria",
      description: "Students and families came together to celebrate Thanksgiving with a traditional feast.",
      image: "/assets/images/common/sc-program2.jpg", 
      category: "Community"
    },
    {
      id: 6,
      title: "Fall Sports Day",
      date: "October 15, 2024",
      time: "9:00 AM - 3:00 PM",
      location: "Outdoor Field",
      description: "Students participated in various sports activities and team games.",
      image: "/assets/images/common/sc-program3.jpg",
      category: "Sports"
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Header */}
      <Header />
              <Link href="/program" className="text-gray-600 hover:text-blue-600 transition-colors">Programs</Link>
              <Link href="/teacher" className="text-gray-600 hover:text-blue-600 transition-colors">Teachers</Link>
              <Link href="/events" className="text-blue-600 font-semibold">Events</Link>
              <Link href="/contact" className="text-gray-600 hover:text-blue-600 transition-colors">Contact</Link>
            </nav>
          </div>
        </div>
      </header>

      {/* Page Banner */}
      <section className="bg-gradient-to-r from-blue-500 to-purple-600 text-white page-banner">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl font-bold mb-4">School Events</h1>
          <nav className="text-blue-200">
            <Link href="/" className="hover:text-white">Home</Link>
            <span className="mx-2">/</span>
            <span>Events</span>
          </nav>
        </div>
      </section>

      {/* Upcoming Events */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold mb-4 text-gray-800">Upcoming Events</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Join us for these exciting upcoming events and activities
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            {upcomingEvents.map((event) => (
              <div key={event.id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                <img 
                  src={event.image} 
                  alt={event.title}
                  className="w-full h-48 object-cover"
                />
                <div className="p-6">
                  <div className="flex items-center justify-between mb-3">
                    <span className="bg-green-100 text-green-800 text-xs font-semibold px-2 py-1 rounded">
                      {event.category}
                    </span>
                    <span className="bg-blue-100 text-blue-800 text-xs font-semibold px-2 py-1 rounded">
                      Upcoming
                    </span>
                  </div>
                  <h3 className="text-xl font-bold mb-3 text-gray-800">{event.title}</h3>
                  <div className="space-y-2 mb-4 text-sm text-gray-600">
                    <div className="flex items-center">
                      <span className="mr-2">📅</span>
                      {event.date}
                    </div>
                    <div className="flex items-center">
                      <span className="mr-2">🕒</span>
                      {event.time}
                    </div>
                    <div className="flex items-center">
                      <span className="mr-2">📍</span>
                      {event.location}
                    </div>
                  </div>
                  <p className="text-gray-600 mb-4">{event.description}</p>
                  <button className="w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 transition-colors">
                    Register Now
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Past Events */}
      <section className="py-16 bg-gray-100">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold mb-4 text-gray-800">Past Events</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Take a look at some of our recent successful events and activities
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {pastEvents.map((event) => (
              <div key={event.id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                <img 
                  src={event.image} 
                  alt={event.title}
                  className="w-full h-48 object-cover"
                />
                <div className="p-6">
                  <div className="flex items-center justify-between mb-3">
                    <span className="bg-purple-100 text-purple-800 text-xs font-semibold px-2 py-1 rounded">
                      {event.category}
                    </span>
                    <span className="bg-gray-100 text-gray-800 text-xs font-semibold px-2 py-1 rounded">
                      Completed
                    </span>
                  </div>
                  <h3 className="text-xl font-bold mb-3 text-gray-800">{event.title}</h3>
                  <div className="space-y-2 mb-4 text-sm text-gray-600">
                    <div className="flex items-center">
                      <span className="mr-2">📅</span>
                      {event.date}
                    </div>
                    <div className="flex items-center">
                      <span className="mr-2">🕒</span>
                      {event.time}
                    </div>
                    <div className="flex items-center">
                      <span className="mr-2">📍</span>
                      {event.location}
                    </div>
                  </div>
                  <p className="text-gray-600">{event.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Event Calendar CTA */}
      <section className="py-16 bg-blue-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">Stay Updated with Our Events</h2>
          <p className="text-xl mb-8 text-blue-100">
            Subscribe to our calendar to never miss an important school event
          </p>
          <div className="space-x-4">
            <Link 
              href="/contact" 
              className="bg-yellow-400 text-blue-900 px-8 py-3 rounded-lg font-semibold hover:bg-yellow-300 transition-colors"
            >
              Subscribe to Calendar
            </Link>
            <Link 
              href="/contact" 
              className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors"
            >
              Contact Us
            </Link>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-800 text-white py-8">
        <div className="container mx-auto px-4 text-center">
          <div className="mb-4">
            <Link href="/" className="text-2xl font-bold text-blue-400">
              Kinco School
            </Link>
          </div>
          <p className="text-gray-400 mb-4">
            Providing quality education and care for your children
          </p>
          <div className="flex justify-center space-x-6">
            <Link href="/about" className="text-gray-400 hover:text-white">About</Link>
            <Link href="/classes" className="text-gray-400 hover:text-white">Classes</Link>
            <Link href="/events" className="text-white">Events</Link>
            <Link href="/contact" className="text-gray-400 hover:text-white">Contact</Link>
          </div>
          <div className="mt-6 pt-6 border-t border-gray-700">
            <p className="text-gray-400">© 2024 Kinco School. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
