'use client';

import { useEffect } from "react";
import Link from "next/link";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation, Pagination } from "swiper/modules";
import Layout from "../lib/layouts/Layout";
import Header1 from "../lib/layouts/header/Header1";
import Footer2 from "../lib/layouts/Footer2";
import { activeNavMenu } from "../lib/utils";

export default function Home() {
  useEffect(() => {
    activeNavMenu();
  }, []);

  return (
    <Layout noFooter noHeader bodyClass={"main"}>
      <Header1 />

      {/* Hero Section */}
      <section className="tf-slider-1">
        <div className="overlay" />
        <div className="container-fluid">
          <div className="row">
            <div className="col-12">
              <div className="slider-1">
                <div className="themesflat-carousel clearfix">
                  <Swiper
                    modules={[Navigation, Pagination]}
                    spaceBetween={0}
                    slidesPerView={1}
                    navigation
                    pagination={{ clickable: true }}
                    loop={true}
                    className="owl-carousel owl-theme none dots-none"
                  >
                    <SwiperSlide className="owl-item">
                      <div className="item-slider-1">
                        <div className="box-content">
                          <div className="sub clr-pri-2">
                            We Care Child Study
                          </div>
                          <div className="title clr-pri-2">
                            Start Learning With
                          </div>
                          <div className="box-custom">
                            <div className="wrap clr-pri-1">Kinco School</div>
                          </div>
                          <div className="des clr-pri-2">
                            Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut elit tellus, luctus nec ullamcorper mattis, pulvinar dapibus leo.
                          </div>
                          <div className="box-btn">
                            <Link href="/about" className="tf-btn style-1">
                              <span>Learn More</span>
                            </Link>
                          </div>
                        </div>
                        <div className="box-feature">
                          <img
                            src="/assets/images/common/slider-1.png"
                            alt="Kinco"
                          />
                        </div>
                      </div>
                    </SwiperSlide>

                    <SwiperSlide className="owl-item">
                      <div className="item-slider-1">
                        <div className="box-content">
                          <div className="sub clr-pri-2">
                            Quality Education
                          </div>
                          <div className="title clr-pri-2">
                            Best Care For Your
                          </div>
                          <div className="box-custom">
                            <div className="wrap clr-pri-1">Little Ones</div>
                          </div>
                          <div className="des clr-pri-2">
                            Providing a safe, nurturing environment where children can learn, grow, and develop their full potential.
                          </div>
                          <div className="box-btn">
                            <a href="/contact" className="tf-btn style-1">
                              <span>Contact Us</span>
                            </a>
                          </div>
                        </div>
                        <div className="box-feature">
                          <img
                            src="/assets/images/common/slider-2.jpg"
                            alt="Kinco"
                          />
                        </div>
                      </div>
                    </SwiperSlide>
                  </Swiper>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section className="tf-section tf-about-us-1">
        <div className="container">
          <div className="row">
            <div className="col-12">
              <div className="about-us-1">
                <div className="box-content">
                  <div className="box-sub-tag">
                    <div className="sub-tag-icon">
                      <i className="flaticon-kindergarten" />
                    </div>
                    <div className="sub-tag-title">
                      <p>About Kinco</p>
                    </div>
                  </div>
                  <div className="title">
                    We Provide The Best Education For Your Children
                  </div>
                  <div className="des">
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut elit tellus, luctus nec ullamcorper mattis, pulvinar dapibus leo. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
                  </div>
                  <div className="box-btn">
                    <a href="/about" className="tf-btn style-2">
                      <span>Learn More</span>
                    </a>
                  </div>
                </div>
                <div className="box-feature">
                  <img
                    src="/assets/images/common/sc-about1.jpg"
                    alt="About Kinco"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <Footer2 />
    </Layout>
  );
}
