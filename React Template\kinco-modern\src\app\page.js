'use client';

import { useEffect } from "react";
import <PERSON> from "next/link";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation, Pagination, Autoplay } from "swiper/modules";

export default function Home() {
  useEffect(() => {
    // Basic setup
    document.querySelector("body").className = "main";
  }, []);

  const programs = [
    {
      id: 1,
      title: "Drawing & Painting",
      description: "Creative art classes to develop imagination and fine motor skills",
      image: "/assets/images/common/sc-program1.jpg"
    },
    {
      id: 2,
      title: "Computer Learning",
      description: "Introduction to basic computer skills and digital literacy",
      image: "/assets/images/common/sc-program2.jpg"
    },
    {
      id: 3,
      title: "Basic English JR",
      description: "Foundation English language learning through fun activities",
      image: "/assets/images/common/sc-program3.jpg"
    },
    {
      id: 4,
      title: "Music & Dance",
      description: "Develop rhythm, coordination and musical appreciation",
      image: "/assets/images/common/sc-program4.jpg"
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Header */}
      <header className="bg-white shadow-sm fixed w-full top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex justify-between items-center">
            <Link href="/" className="flex items-center">
              <img src="/assets/images/logo/logodark-2.png" alt="Kinco School" className="h-12" />
            </Link>
            <nav className="hidden md:flex space-x-6">
              <Link href="/" className="text-blue-600 font-semibold">Home</Link>
              <Link href="/about" className="text-gray-600 hover:text-blue-600 transition-colors">About</Link>
              <Link href="/classes" className="text-gray-600 hover:text-blue-600 transition-colors">Classes</Link>
              <Link href="/program" className="text-gray-600 hover:text-blue-600 transition-colors">Programs</Link>
              <Link href="/teacher" className="text-gray-600 hover:text-blue-600 transition-colors">Teachers</Link>
              <Link href="/gallery" className="text-gray-600 hover:text-blue-600 transition-colors">Gallery</Link>
              <Link href="/blog" className="text-gray-600 hover:text-blue-600 transition-colors">Blog</Link>
              <Link href="/events" className="text-gray-600 hover:text-blue-600 transition-colors">Events</Link>
              <Link href="/contact" className="text-gray-600 hover:text-blue-600 transition-colors">Contact</Link>
            </nav>
            <div className="md:hidden">
              <button className="text-gray-600">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Slider */}
      <section className="relative pt-20">
        <Swiper
          modules={[Navigation, Pagination, Autoplay]}
          spaceBetween={0}
          slidesPerView={1}
          navigation
          pagination={{ clickable: true }}
          autoplay={{ delay: 5000 }}
          loop={true}
          className="h-screen"
        >
          <SwiperSlide>
            <div className="relative h-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center">
              <div className="absolute inset-0 bg-black opacity-20"></div>
              <div className="container mx-auto px-4 relative z-10">
                <div className="grid md:grid-cols-2 gap-12 items-center text-white">
                  <div>
                    <p className="text-yellow-300 font-semibold mb-4">We Care Child Study</p>
                    <h1 className="text-5xl md:text-6xl font-bold mb-6">
                      Start Learning With <span className="text-yellow-300">Kinco School</span>
                    </h1>
                    <ul className="mb-8 space-y-2">
                      <li className="flex items-center">
                        <span className="text-green-400 mr-3">✓</span>
                        Outdoor Games
                      </li>
                      <li className="flex items-center">
                        <span className="text-green-400 mr-3">✓</span>
                        Sport Activities
                      </li>
                      <li className="flex items-center">
                        <span className="text-green-400 mr-3">✓</span>
                        Nutritious Foods
                      </li>
                    </ul>
                    <div className="space-x-4">
                      <Link href="/contact" className="bg-yellow-400 text-blue-900 px-8 py-3 rounded-lg font-semibold hover:bg-yellow-300 transition-colors">
                        Contact Us
                      </Link>
                      <Link href="/about" className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors">
                        Learn More
                      </Link>
                    </div>
                  </div>
                  <div className="text-center">
                    <img src="/assets/images/common/slider-1.png" alt="Children Learning" className="max-w-full h-auto" />
                  </div>
                </div>
              </div>
            </div>
          </SwiperSlide>

          <SwiperSlide>
            <div className="relative h-full bg-gradient-to-r from-green-500 to-blue-600 flex items-center">
              <div className="absolute inset-0 bg-black opacity-20"></div>
              <div className="container mx-auto px-4 relative z-10">
                <div className="grid md:grid-cols-2 gap-12 items-center text-white">
                  <div>
                    <p className="text-yellow-300 font-semibold mb-4">Quality Education</p>
                    <h1 className="text-5xl md:text-6xl font-bold mb-6">
                      Best Care For Your <span className="text-yellow-300">Little Ones</span>
                    </h1>
                    <p className="text-xl mb-8 leading-relaxed">
                      Providing a safe, nurturing environment where children can learn, grow, and develop their full potential through innovative teaching methods.
                    </p>
                    <div className="space-x-4">
                      <Link href="/contact" className="bg-yellow-400 text-blue-900 px-8 py-3 rounded-lg font-semibold hover:bg-yellow-300 transition-colors">
                        Contact Us
                      </Link>
                      <Link href="/program" className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors">
                        Our Programs
                      </Link>
                    </div>
                  </div>
                  <div className="text-center">
                    <img src="/assets/images/common/slider-2.jpg" alt="Happy Children" className="max-w-full h-auto rounded-lg" />
                  </div>
                </div>
              </div>
            </div>
          </SwiperSlide>
        </Swiper>
      </section>

      {/* Discovery/Features Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold mb-4 text-gray-800">Why Choose Kinco School?</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              We offer comprehensive programs designed to nurture your child's development
            </p>
          </div>
          <div className="grid md:grid-cols-4 gap-8">
            <div className="bg-white p-6 rounded-lg shadow-md text-center hover:shadow-lg transition-shadow">
              <div className="text-blue-600 text-5xl mb-4">🎮</div>
              <h3 className="text-xl font-semibold mb-3 text-gray-800">Study & Game</h3>
              <p className="text-gray-600 mb-4">Sed ut perspiciatis unde omnis iste natu voluptatem accus antium dolorem</p>
              <Link href="/classes" className="text-blue-600 hover:text-blue-800 font-semibold">Read More →</Link>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-md text-center hover:shadow-lg transition-shadow">
              <div className="text-blue-600 text-5xl mb-4">📚</div>
              <h3 className="text-xl font-semibold mb-3 text-gray-800">A to Z Programs</h3>
              <p className="text-gray-600 mb-4">Sed ut perspiciatis unde omnis iste natu voluptatem accus antium dolorem</p>
              <Link href="/program" className="text-blue-600 hover:text-blue-800 font-semibold">Read More →</Link>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-md text-center hover:shadow-lg transition-shadow">
              <div className="text-blue-600 text-5xl mb-4">👨‍🏫</div>
              <h3 className="text-xl font-semibold mb-3 text-gray-800">Expert Teacher</h3>
              <p className="text-gray-600 mb-4">Sed ut perspiciatis unde omnis iste natu voluptatem accus antium dolorem</p>
              <Link href="/teacher" className="text-blue-600 hover:text-blue-800 font-semibold">Read More →</Link>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-md text-center hover:shadow-lg transition-shadow">
              <div className="text-blue-600 text-5xl mb-4">🏆</div>
              <h3 className="text-xl font-semibold mb-3 text-gray-800">Best Awards</h3>
              <p className="text-gray-600 mb-4">Sed ut perspiciatis unde omnis iste natu voluptatem accus antium dolorem</p>
              <Link href="/about" className="text-blue-600 hover:text-blue-800 font-semibold">Read More →</Link>
            </div>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div>
              <div className="flex items-center mb-4">
                <div className="text-blue-600 text-2xl mr-3">🎓</div>
                <p className="text-blue-600 font-semibold">About Kinco</p>
              </div>
              <h2 className="text-4xl font-bold mb-6 text-gray-800">
                We Provide The Best Education For Your Children
              </h2>
              <p className="text-gray-600 mb-6 leading-relaxed">
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut elit tellus, luctus nec ullamcorper mattis, pulvinar dapibus leo. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
              </p>
              <p className="text-gray-600 mb-8 leading-relaxed">
                Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
              </p>
              <Link href="/about" className="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                Learn More
              </Link>
            </div>
            <div className="relative">
              <img
                src="/assets/images/common/sc-about1.jpg"
                alt="About Kinco"
                className="rounded-lg shadow-lg w-full"
              />
              <div className="absolute -bottom-6 -right-6 bg-yellow-400 text-blue-900 p-6 rounded-lg shadow-lg">
                <div className="text-3xl font-bold">15+</div>
                <div className="text-sm font-semibold">Years Experience</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Latest Programs Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <div className="flex items-center justify-center mb-4">
              <div className="text-blue-600 text-2xl mr-3">📖</div>
              <p className="text-blue-600 font-semibold">Latest Programs</p>
            </div>
            <h2 className="text-4xl font-bold mb-4 text-gray-800">Our Latest Programs</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Discover our comprehensive range of educational programs designed to nurture your child's development
            </p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {programs.map((program) => (
              <div key={program.id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                <img
                  src={program.image}
                  alt={program.title}
                  className="w-full h-48 object-cover"
                />
                <div className="p-6">
                  <h3 className="text-xl font-bold mb-3 text-gray-800">{program.title}</h3>
                  <p className="text-gray-600 mb-4">{program.description}</p>
                  <Link
                    href="/classes"
                    className="text-blue-600 hover:text-blue-800 font-semibold"
                  >
                    Learn More →
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Statistics Section */}
      <section className="py-16 bg-blue-600 text-white">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-4xl font-bold mb-2">500+</div>
              <div className="text-blue-200">Happy Students</div>
            </div>
            <div>
              <div className="text-4xl font-bold mb-2">50+</div>
              <div className="text-blue-200">Expert Teachers</div>
            </div>
            <div>
              <div className="text-4xl font-bold mb-2">15+</div>
              <div className="text-blue-200">Years Experience</div>
            </div>
            <div>
              <div className="text-4xl font-bold mb-2">25+</div>
              <div className="text-blue-200">Awards Won</div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-4xl font-bold mb-4 text-gray-800">Ready to Enroll Your Child?</h2>
          <p className="text-xl mb-8 text-gray-600 max-w-2xl mx-auto">
            Join our community of happy families and give your child the best start in life with our comprehensive educational programs.
          </p>
          <div className="space-x-4">
            <Link
              href="/contact"
              className="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
            >
              Contact Us Today
            </Link>
            <Link
              href="/about"
              className="border-2 border-blue-600 text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-600 hover:text-white transition-colors"
            >
              Learn More
            </Link>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-800 text-white py-12">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <img src="/assets/images/logo/logofootert.png" alt="Kinco School" className="h-12 mb-4" />
              <p className="text-gray-400 mb-4">
                Providing quality education and care for your children in a safe, nurturing environment.
              </p>
              <div className="flex space-x-4">
                <a href="#" className="text-gray-400 hover:text-white">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                  </svg>
                </a>
                <a href="#" className="text-gray-400 hover:text-white">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
                  </svg>
                </a>
              </div>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-4">Quick Links</h3>
              <ul className="space-y-2">
                <li><Link href="/about" className="text-gray-400 hover:text-white">About Us</Link></li>
                <li><Link href="/classes" className="text-gray-400 hover:text-white">Classes</Link></li>
                <li><Link href="/program" className="text-gray-400 hover:text-white">Programs</Link></li>
                <li><Link href="/teacher" className="text-gray-400 hover:text-white">Teachers</Link></li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-4">Programs</h3>
              <ul className="space-y-2">
                <li><a href="#" className="text-gray-400 hover:text-white">Drawing & Painting</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white">Computer Learning</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white">Basic English</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white">Music & Dance</a></li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-4">Contact Info</h3>
              <div className="space-y-2 text-gray-400">
                <p>📍 123 Education Street, Learning City</p>
                <p>📞 (555) 123-4567</p>
                <p>✉️ <EMAIL></p>
                <p>🕒 Mon-Fri: 7AM-6PM</p>
              </div>
            </div>
          </div>
          <div className="border-t border-gray-700 mt-8 pt-8 text-center">
            <p className="text-gray-400">© 2024 Kinco School. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
