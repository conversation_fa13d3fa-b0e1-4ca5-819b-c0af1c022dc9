'use client';

import { useEffect } from "react";
import Link from "next/link";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation, Pagination, Autoplay } from "swiper/modules";
import Header from "../components/Header";
import Footer from "../components/Footer";

export default function Home() {
  useEffect(() => {
    // Basic setup
    document.querySelector("body").className = "main";
  }, []);

  const programs = [
    {
      id: 1,
      title: "Drawing & Painting",
      description: "Creative art classes to develop imagination and fine motor skills",
      icon: "fa-paint-brush",
      color: "primary"
    },
    {
      id: 2,
      title: "Computer Learning",
      description: "Introduction to basic computer skills and digital literacy",
      icon: "fa-laptop",
      color: "info"
    },
    {
      id: 3,
      title: "Basic English JR",
      description: "Foundation English language learning through fun activities",
      icon: "fa-book",
      color: "success"
    },
    {
      id: 4,
      title: "Music & Dance",
      description: "Develop rhythm, coordination and musical appreciation",
      icon: "fa-music",
      color: "warning"
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Header */}
      <Header />

      {/* Hero Slider */}
      <section className="hero-section" style={{ paddingTop: '80px' }}>
        <Swiper
          modules={[Navigation, Pagination, Autoplay]}
          spaceBetween={0}
          slidesPerView={1}
          navigation={{
            nextEl: '.swiper-button-next',
            prevEl: '.swiper-button-prev',
          }}
          pagination={{
            clickable: true,
            el: '.swiper-pagination'
          }}
          autoplay={{ delay: 5000, disableOnInteraction: false }}
          loop={true}
          className="hero-swiper"
          style={{ height: '100vh' }}
        >
          <SwiperSlide>
            <div
              className="hero-slide d-flex align-items-center"
              style={{
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                minHeight: '100vh',
                position: 'relative'
              }}
            >
              <div className="container">
                <div className="row align-items-center">
                  <div className="col-lg-6">
                    <div className="hero-content text-white">
                      <p className="hero-subtitle text-warning fw-semibold mb-3 fs-5">🌟 We Care Child Study</p>
                      <h1 className="hero-title display-3 fw-bold mb-4">
                        Best Care For Your <span className="text-warning">Little Ones</span>
                      </h1>
                      <p className="hero-description lead mb-4">
                        Providing a safe, nurturing environment where children can learn, grow,
                        and develop their full potential through innovative teaching methods.
                      </p>
                      <ul className="hero-features list-unstyled mb-4">
                        <li className="mb-2 fs-5">
                          <i className="fa fa-check-circle text-success me-3"></i>
                          <span>🎮 Outdoor Games & Activities</span>
                        </li>
                        <li className="mb-2 fs-5">
                          <i className="fa fa-check-circle text-success me-3"></i>
                          <span>🏃 Sport & Physical Development</span>
                        </li>
                        <li className="mb-2 fs-5">
                          <i className="fa fa-check-circle text-success me-3"></i>
                          <span>🥗 Nutritious & Healthy Meals</span>
                        </li>
                      </ul>
                      <div className="hero-buttons">
                        <Link href="/contact" className="btn btn-warning btn-lg me-3 px-5 py-3 rounded-pill fw-semibold">
                          📞 Contact Us
                        </Link>
                        <Link href="/about" className="btn btn-outline-light btn-lg px-5 py-3 rounded-pill fw-semibold">
                          📚 Learn More
                        </Link>
                      </div>
                    </div>
                  </div>
                  <div className="col-lg-6">
                    <div className="hero-image text-center">
                      <div
                        className="hero-image-placeholder d-flex align-items-center justify-content-center rounded-3 shadow-lg"
                        style={{
                          height: '500px',
                          background: 'linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.1) 100%)',
                          border: '2px dashed rgba(255,255,255,0.3)'
                        }}
                      >
                        <div className="text-center text-white">
                          <i className="fa fa-graduation-cap" style={{fontSize: '4rem', marginBottom: '1rem'}}></i>
                          <h4 className="fw-bold">Happy Children Learning</h4>
                          <p className="mb-0">Building bright futures together</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </SwiperSlide>

          <SwiperSlide>
            <div
              className="hero-slide d-flex align-items-center"
              style={{
                background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                minHeight: '100vh',
                position: 'relative'
              }}
            >
              <div className="container">
                <div className="row align-items-center">
                  <div className="col-lg-6">
                    <div className="hero-content text-white">
                      <p className="hero-subtitle text-warning fw-semibold mb-3 fs-5">🎓 Quality Education</p>
                      <h1 className="hero-title display-3 fw-bold mb-4">
                        Start Learning With <span className="text-warning">Smart Kinco</span>
                      </h1>
                      <p className="hero-description lead mb-4">
                        Building bright futures through innovative teaching methods, creative learning
                        experiences, and a nurturing environment where every child can thrive.
                      </p>
                      <div className="hero-buttons">
                        <Link href="/contact" className="btn btn-warning btn-lg me-3 px-5 py-3 rounded-pill fw-semibold">
                          📞 Contact Us
                        </Link>
                        <Link href="/program" className="btn btn-outline-light btn-lg px-5 py-3 rounded-pill fw-semibold">
                          🎯 Our Programs
                        </Link>
                      </div>
                    </div>
                  </div>
                  <div className="col-lg-6">
                    <div className="hero-image text-center">
                      <div
                        className="hero-image-placeholder d-flex align-items-center justify-content-center rounded-3 shadow-lg"
                        style={{
                          height: '500px',
                          background: 'linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.1) 100%)',
                          border: '2px dashed rgba(255,255,255,0.3)'
                        }}
                      >
                        <div className="text-center text-white">
                          <i className="fa fa-heart" style={{fontSize: '4rem', marginBottom: '1rem'}}></i>
                          <h4 className="fw-bold">Caring Environment</h4>
                          <p className="mb-0">Where every child feels loved</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </SwiperSlide>

          {/* Navigation */}
          <div className="swiper-button-next"></div>
          <div className="swiper-button-prev"></div>
          <div className="swiper-pagination"></div>
        </Swiper>
      </section>

      {/* Discovery/Features Section */}
      <section className="features-section py-5 bg-light">
        <div className="container">
          <div className="row">
            <div className="col-12">
              <div className="text-center mb-5">
                <div className="d-flex align-items-center justify-content-center mb-3">
                  <div className="me-3">
                    <i className="fa fa-graduation-cap text-primary" style={{fontSize: '2rem'}}></i>
                  </div>
                  <p className="text-primary fw-semibold mb-0 fs-5">Why Choose Us</p>
                </div>
                <h2 className="display-4 fw-bold mb-4">Why Choose Kinco School?</h2>
                <p className="text-muted lead mx-auto" style={{maxWidth: '600px'}}>
                  We offer comprehensive programs designed to nurture your child's development
                </p>
              </div>
              <div className="row g-4">
                <div className="col-lg-3 col-md-6">
                  <div className="feature-card bg-white p-4 rounded-3 shadow-sm text-center h-100 border-0">
                    <div className="feature-icon mb-4">
                      <div className="icon-circle bg-primary bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center" style={{width: '80px', height: '80px'}}>
                        <i className="fa fa-gamepad text-primary" style={{fontSize: '2rem'}}></i>
                      </div>
                    </div>
                    <h3 className="h5 fw-bold mb-3">Study & Game</h3>
                    <p className="text-muted mb-4">Sed ut perspiciatis unde omnis iste natu voluptatem accus antium dolorem</p>
                    <Link href="/classes" className="btn btn-outline-primary btn-sm rounded-pill">
                      Read More <i className="fa fa-arrow-right ms-1"></i>
                    </Link>
                  </div>
                </div>
                <div className="col-lg-3 col-md-6">
                  <div className="feature-card bg-white p-4 rounded-3 shadow-sm text-center h-100 border-0">
                    <div className="feature-icon mb-4">
                      <div className="icon-circle bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center" style={{width: '80px', height: '80px'}}>
                        <i className="fa fa-book text-success" style={{fontSize: '2rem'}}></i>
                      </div>
                    </div>
                    <h3 className="h5 fw-bold mb-3">A to Z Programs</h3>
                    <p className="text-muted mb-4">Sed ut perspiciatis unde omnis iste natu voluptatem accus antium dolorem</p>
                    <Link href="/program" className="btn btn-outline-success btn-sm rounded-pill">
                      Read More <i className="fa fa-arrow-right ms-1"></i>
                    </Link>
                  </div>
                </div>
                <div className="col-lg-3 col-md-6">
                  <div className="feature-card bg-white p-4 rounded-3 shadow-sm text-center h-100 border-0">
                    <div className="feature-icon mb-4">
                      <div className="icon-circle bg-info bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center" style={{width: '80px', height: '80px'}}>
                        <i className="fa fa-user text-info" style={{fontSize: '2rem'}}></i>
                      </div>
                    </div>
                    <h3 className="h5 fw-bold mb-3">Expert Teacher</h3>
                    <p className="text-muted mb-4">Sed ut perspiciatis unde omnis iste natu voluptatem accus antium dolorem</p>
                    <Link href="/teacher" className="btn btn-outline-info btn-sm rounded-pill">
                      Read More <i className="fa fa-arrow-right ms-1"></i>
                    </Link>
                  </div>
                </div>
                <div className="col-lg-3 col-md-6">
                  <div className="feature-card bg-white p-4 rounded-3 shadow-sm text-center h-100 border-0">
                    <div className="feature-icon mb-4">
                      <div className="icon-circle bg-warning bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center" style={{width: '80px', height: '80px'}}>
                        <i className="fa fa-trophy text-warning" style={{fontSize: '2rem'}}></i>
                      </div>
                    </div>
                    <h3 className="h5 fw-bold mb-3">Best Awards</h3>
                    <p className="text-muted mb-4">Sed ut perspiciatis unde omnis iste natu voluptatem accus antium dolorem</p>
                    <Link href="/about" className="btn btn-outline-warning btn-sm rounded-pill">
                      Read More <i className="fa fa-arrow-right ms-1"></i>
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section className="about-section py-5">
        <div className="container">
          <div className="row align-items-center">
            <div className="col-lg-6 mb-4 mb-lg-0">
              <div className="about-content">
                <div className="d-flex align-items-center mb-3">
                  <div className="me-3">
                    <i className="fa fa-heart text-primary" style={{fontSize: '2rem'}}></i>
                  </div>
                  <p className="text-primary fw-semibold mb-0 fs-5">About Kinco</p>
                </div>
                <h2 className="display-5 fw-bold mb-4">
                  We Provide The Best Education For Your Children
                </h2>
                <p className="text-muted mb-4 lead">
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut elit tellus,
                  luctus nec ullamcorper mattis, pulvinar dapibus leo. Sed do eiusmod tempor
                  incididunt ut labore et dolore magna aliqua.
                </p>
                <p className="text-muted mb-4">
                  Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut
                  aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit.
                </p>
                <div className="about-features mb-4">
                  <div className="row g-3">
                    <div className="col-6">
                      <div className="feature-item d-flex align-items-center">
                        <i className="fa fa-check-circle text-success me-2"></i>
                        <span>Safe Environment</span>
                      </div>
                    </div>
                    <div className="col-6">
                      <div className="feature-item d-flex align-items-center">
                        <i className="fa fa-check-circle text-success me-2"></i>
                        <span>Expert Teachers</span>
                      </div>
                    </div>
                    <div className="col-6">
                      <div className="feature-item d-flex align-items-center">
                        <i className="fa fa-check-circle text-success me-2"></i>
                        <span>Quality Education</span>
                      </div>
                    </div>
                    <div className="col-6">
                      <div className="feature-item d-flex align-items-center">
                        <i className="fa fa-check-circle text-success me-2"></i>
                        <span>Fun Activities</span>
                      </div>
                    </div>
                  </div>
                </div>
                <Link href="/about" className="btn btn-primary btn-lg rounded-pill px-4">
                  Learn More <i className="fa fa-arrow-right ms-2"></i>
                </Link>
              </div>
            </div>
            <div className="col-lg-6">
              <div className="about-image position-relative">
                <div
                  className="about-image-placeholder d-flex align-items-center justify-content-center rounded-3 shadow-lg"
                  style={{
                    height: '400px',
                    background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
                    border: '2px dashed #dee2e6'
                  }}
                >
                  <div className="text-center text-muted">
                    <i className="fa fa-users" style={{fontSize: '4rem', marginBottom: '1rem'}}></i>
                    <h4 className="fw-bold">About Our School</h4>
                    <p className="mb-0">Creating memorable learning experiences</p>
                  </div>
                </div>
                <div className="experience-badge position-absolute bg-warning text-dark p-4 rounded-3 shadow-lg"
                     style={{bottom: '-30px', right: '-30px', minWidth: '120px'}}>
                  <div className="text-center">
                    <div className="h2 fw-bold mb-1">15+</div>
                    <div className="small fw-semibold">Years Experience</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Latest Programs Section */}
      <section className="tf-section tf-latest-program bg-light">
        <div className="container">
          <div className="row">
            <div className="col-12">
              <div className="latest-program-1">
                <div className="text-center mb-5">
                  <div className="box-sub-tag d-flex align-items-center justify-content-center mb-3">
                    <div className="sub-tag-icon me-3">
                      <i className="flaticon-book text-primary fs-2"></i>
                    </div>
                    <div className="sub-tag-title">
                      <p className="text-primary fw-semibold mb-0">Latest Programs</p>
                    </div>
                  </div>
                  <h2 className="h1 fw-bold mb-4">Our Latest Programs</h2>
                  <p className="text-muted lead">
                    Discover our comprehensive range of educational programs designed to nurture your child's development
                  </p>
                </div>
                <div className="row g-4">
                  {programs.map((program) => (
                    <div key={program.id} className="col-lg-3 col-md-6">
                      <div className="program-item bg-white rounded shadow-sm overflow-hidden h-100 border-0">
                        <div className="program-image-placeholder d-flex align-items-center justify-content-center"
                             style={{height: '200px', background: `var(--bs-${program.color})`}}>
                          <div className="text-center text-white">
                            <i className={`fa ${program.icon}`} style={{fontSize: '3rem'}}></i>
                          </div>
                        </div>
                        <div className="program-content p-4">
                          <h3 className="h5 fw-bold mb-3">{program.title}</h3>
                          <p className="text-muted mb-3">{program.description}</p>
                          <Link
                            href="/classes"
                            className={`text-${program.color} text-decoration-none fw-semibold`}
                          >
                            Learn More <i className="fa fa-arrow-right ms-1"></i>
                          </Link>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Statistics Section */}
      <section className="tf-section tf-counter bg-primary text-white">
        <div className="container">
          <div className="row">
            <div className="col-12">
              <div className="counter-1">
                <div className="row g-4 text-center">
                  <div className="col-lg-3 col-md-6">
                    <div className="counter-item">
                      <div className="counter-number h1 fw-bold mb-2">500+</div>
                      <div className="counter-title text-white-50">Happy Students</div>
                    </div>
                  </div>
                  <div className="col-lg-3 col-md-6">
                    <div className="counter-item">
                      <div className="counter-number h1 fw-bold mb-2">50+</div>
                      <div className="counter-title text-white-50">Expert Teachers</div>
                    </div>
                  </div>
                  <div className="col-lg-3 col-md-6">
                    <div className="counter-item">
                      <div className="counter-number h1 fw-bold mb-2">15+</div>
                      <div className="counter-title text-white-50">Years Experience</div>
                    </div>
                  </div>
                  <div className="col-lg-3 col-md-6">
                    <div className="counter-item">
                      <div className="counter-number h1 fw-bold mb-2">25+</div>
                      <div className="counter-title text-white-50">Awards Won</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="tf-section tf-cta">
        <div className="container">
          <div className="row">
            <div className="col-12">
              <div className="cta-1 text-center">
                <h2 className="h1 fw-bold mb-4">Ready to Enroll Your Child?</h2>
                <p className="lead text-muted mb-5 mx-auto" style={{maxWidth: '600px'}}>
                  Join our community of happy families and give your child the best start in life with our comprehensive educational programs.
                </p>
                <div className="cta-buttons">
                  <Link
                    href="/contact"
                    className="tf-btn style-1 me-3"
                  >
                    <span>Contact Us Today</span>
                  </Link>
                  <Link
                    href="/about"
                    className="tf-btn style-2"
                  >
                    <span>Learn More</span>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <Footer />
    </div>
  );
}
