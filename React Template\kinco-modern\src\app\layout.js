import 'bootstrap/dist/css/bootstrap.min.css';
import 'animate.css';
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import "./globals.css";

export const metadata = {
  title: "Kinco - Day Care & Kindergarten React Template",
  description: "Modern day care and kindergarten website built with Next.js 15 and React 19",
};

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <head>
        <link rel="icon" href="/assets/images/Favicon.png" />
        <link rel="stylesheet" href="/assets/css/font-awesome.css" />
        <link rel="stylesheet" href="/assets/css/style.css" />
        <link rel="stylesheet" href="/assets/css/responsive.css" />
      </head>
      <body>
        {children}
      </body>
    </html>
  );
}
