/* Media Queries
-------------------------------------------------------------- */
@media only screen and (max-width: 1825px) {
    #site-header .site-header-inner {
        padding: 0 0;
    }

    .fun-fact1 {
        left: 0;
    }

    .fun-fact2 {
        right: 0;
    }

    .sc-contact {
        margin: 0 0;
    }
    .inner-page .header-right {
        right: 15px;
    }
    .inner-page .header-contact {
        align-items: center;
        margin-left: 30px;
    }
    .inner-page #mainnav .menu > li {
        margin-right: 30px;
    }
    .tf-program-details2 .wrap-details .sc-about-2 {
        padding: 4% 4% !important;
    }
}

@media only screen and (max-width: 1445px) {
    #mainnav .menu {
        padding-left: 0px;
    }
    .sc-img {
        display: none;
    }
    .header-contact,
    .inner-contact {
        margin-left: 30px;
    }

    .gallery .tf-section.tf-gallery .title-heading.st-3 {
        padding: 0 23%;
    }

    .header2 #mainnav .menu > li {
        margin-right: 30px;
    }

    #mainnav .menu > li.menu-item-has-children > a::after {
        right: -15px;
    }

    .sc-contact {
        padding: 30px;
    }
    .inner-page .nav-wrap {
        left: 24%;
    }
    .inner-page #header-search,
    .inner-page .menu-bar-right {
        display: none;
    }

    .slider-courses  .owl-nav .owl-next {
        right: -100px;
    }

    .slider-courses  .owl-nav .owl-prev {
        left: -100px;
    }
}

@media only screen and (max-width: 1440px) {
    .fl-services .box-content {
        padding: 20px 80px 0 30px;
    }
    .feature-blog-1 {
        top: -50px;
        left: -20px;
    }
}

@media only screen and (max-width: 1375px) {
    .tab-time-table .content-tab .content-inner .list-date,
    .calendar-box {
        width: 1170px;
    }
    .tab-time-table .content-tab .content-inner {
        overflow-x: auto;
    }

    .slider-courses  .owl-nav .owl-next,
    .slider-courses  .owl-nav .owl-prev {
        display: none;
    }
}

@media only screen and (max-width: 1199px) {
    .tf-program-details .bg1, 
    .tf-program-details .bg2,
    #header-search,
    .menu-bar-right,
    .fun-fact2,
    .fun-fact1 {
        display: none;
    }

    .list-author.m-t-62 {
        margin: 0;
    }

    .infor-product .img .img-top {
        padding: 0 30px;
    }

    .teacher-details {
        flex-wrap: wrap;
    }
    .sc-contact .wrap,
    .sc-contact .inner-sc-contact {
        width: 50%;
    }

    .item-slider-2 .box-content,
    .item-slider-2 .title,
    .item-slider-2 .wrap,
    .sc-fun-fact {
        padding: 0 0 !important;
    }

    .sc-discovery .inner-discovery {
        padding: 30px 15px;
    }

    .fl-btn.st-10 {
        padding: 0 15px;
    }

    .title-heading.st-4 {
        padding: 0 10% !important;
    }

    .header-contact,
    .inner-contact {
        margin-left: 0;
    }

    .meta-post ul li {
        margin-right: 15px;
    }

    #mainnav .menu > li {
        margin-right: 30px;
    }

    .tab-time-table .menu-tab li {
        padding: 10px 20px;
    }

    .widget-quote .box-feature {
        margin: 0 50px;
    }

    .widget-quote .box-content {
        padding: 20px 30px 0;
    }

    .sc-event-box {
        margin: 0 0 30px 0;
    }

    .tag-article .title {
        margin-right: 10px;
    }

    .sc-contact .inner-contact {
        margin-left: 15px;
    }
    .teacher .tf-section.tf-counter2 .title-heading .title,
    .widget-logo .wrap {
        padding-right: 0;
    }

    .widget-business .inner {
        padding: 30px 15px;
    }

    .infor-product-details,
    .widget.widget-link {
        padding-left: 15px;
    }

    .item-slider-1 .box-content {
        padding-left: 9%;
    }

    .item-slider-2 {
        align-items: center;
    }

    .sc-contact .inner-sc-contact {
        justify-content: end;
    }
    .item-slider-1 .wrap {
        padding: 20px;
        font-size: 45px;
    }

    .pd0-135 {
        padding: 0;
    }

    .fl-subcribe {
        padding: 70px 100px 98px;
    }
    .inner-page #mainnav .menu li a,
    .header2 #mainnav .menu li a {
        font-size: 15px;
    }
    .inner-contact ul li:last-child {
        font-size: 20px;
    }

    .about .sc-discovery-2,
    .sc-discovery-2 {
        padding-left: 15px;
        padding-right: 15px;
    }

    .inner-page .sc-employee .box-content {
        top: 160px;
    }
    .inner-page.teacher .sc-employee {
        margin-bottom: 330px;
    }

    .wrap-couter {
        margin-right: -370px;
    }
    .tf-classe-detail .teacher-desc {
        width: 100%;
    }

    .teacher-desc {
        padding-left: 0;
    }
    .teacher-image {
        padding: 11px 10px 12px 10px;
        width: calc(43% - 22px);
    }

    .sc-pricing {
        padding: 35px 15px 60px 20px;
    }

    .tf-faq-2 .flat-tabs .menu-tab li {
        padding: 14px 20px;
    }

    .sc-program .content {
        padding: 25px;
    }

    .sc-program-content .wrap-box p {
        margin-right: 0;
    }

    .tf-testimonial .item-fb {
        width: 370px;
        height: 385px;
    }

    .sc-product {
        width: calc(50% - 30px);
    }

    .sc-artice .box-content {
        padding: 126px 15px 25px 15px
    }
    .tf-teacher-details .teacher-infor {
        width: calc(55% - 22px);
    }
    .tf-teacher-details .teacher-image {
        width: calc(43% - 22px);
    }
    .tf-teacher-details .teacher-desc {
        width: calc(70% - 22px);
    }
}

@media (max-width: 1199px) and (min-width: 991px) {
    .item-courses .box-content {
        margin: -59px 15px 0 15px;
        position: relative;
        padding: 15px 12px;
    }
    #sidebar.classe-details .widget .inner-infor {
        padding: 0 15px 40px;
    }
    .sc-calendar {
        padding: 30px 20px 43px 20px;
    }
    .sc-event-box .content {
        padding-left: 30px;
    }
}

@media only screen and (max-width: 991px) {
    .tf-about .wrap-image .image img,
    .tf-program-details2 .wrap-details .image,
    .tf-program-details2 .wrap-details .sc-about-2,
    .tab-l-100 img,
    .fl-services .box-feature,
    .fl-services .box-content,
    .heading.st-1 .title-heading, 
    .heading.st-1 .heading-btn,
    .feature-about2 img {
        width: 100%;
    }

    .sc-quote .owl-carousel {
        padding-right: 50px;
    }

    .home2 .tf-section-top.tf-discovery {
        margin-top: -45px;
    }

    .testimonial .sc-quote .inner {
        width: 75%;
    }
    #site-header .site-header-inner.st-2,
    .testimonial .sc-quote .list-author,
    .testimonial .sc-quote .owl-carousel {
        padding: 0px;
    }
    .tf-program-details2 .wrap-details .sc-about-2 {
        padding:70px 15px !important;
    }

    .sc-event-box {
        width: 940px;
    }

    .sc-event-box.style2 {
        width: auto;
    }

    .wrap-sc-event {
        overflow-y: auto;
        padding: 50px;
        margin: -50px;
    }

    .slider-courses .owl-carousel .owl-dots {
        display: none;
    }

    .tab-time-table .content-tab {
        padding: 45px 20px 75px;;
    }

    .sc-pricing {
        width: calc(100% - 30px);
    }

    .tf-section.tf-product .fx,
    .tab-time-table .menu-tab,
    .wrap-details.fx,
    .tab-faq.flat-tabs .menu-tab,
    .content-tab .content-inner {
        flex-wrap: wrap;
    }

    .tf-section.tf-testimonial {
        padding: 130px 0 150px !important;
        margin-top: -80px;
    }

    .sc-event-box {
        padding: 30px 25px;
    }

    .tf-section.tf-counter {
        padding: 90px 0 !important;
        margin-bottom: -20px;
    }

    .sc-contact .inner-sc-contact {
        width: 60%;
    }

    .widget-footer .widget {
        width: 50%;
    }

    .sc-contact .wrap {
        width: 40%;
    }

    .sc-discovery {
        width: 33.33%;
    }

    .inner-contact ul li:last-child {
        font-size: 18px;
    }

    .header-contact {
        margin-right: 70px;
    }

    .tf-about .wrap-image .image.m-r30,
    .inner-page .header-contact {
        margin-right: 0;
    }

    .tf-about .wrap-image .fx .image {
        width: calc(50% - 30px);
        margin-left: 30px;
    }

    .tf-about .wrap-image > .fx {
        margin-left: -30px;
    }

    .inner-page .header-right {
        right: 70px;
    }

    .tf-slider-2 {
        padding: 150px 0 !important;
    }

    .tf-section.tf-program-details3 {
        padding-bottom: 0 !important;
    }

    .tf-section.tf-event {
        padding-bottom: 130px !important;
    }

    .tf-slider-1 {
        position: relative;
        padding: 130px 0 82px 0 !important;
    }

    .tf-section.tf-program-details {
        padding: 130px 0 70px !important;
    }

    .tf-section.tf-program {
        padding: 110px 0 !important;
    }
    .testimonial .sc-quote .owl-carousel .owl-dots,
    .top-bar-2 .header-contact {
        display: none;
    }
    .tf-about .wrap-image .image.p-l70,
    .item-slider-1 .box-content {
        padding-left: 0;
    }

    .item-fb {
        margin: 0 auto;
    }
    .fl-services .box-content,
    .classe-details.p-t17,
    .tf-section.tf-program-details2,
    .tf-faq-2 .flat-tabs .content-tab,
    .title-heading,
    .sc-service {
        padding: 0 0 !important;
    }
    .infor-teacher-detail {
        margin-top: 70px;
    }
    .item-quote {
        padding: 30px 15px;
    }

    .themesflat-pagination {
        padding-top: 0 !important;
    }
    .infor-product-details,
    .fl-services .box-feature,
    .subcribe-form form {
        padding-left: 0;
    }
    .sc-about-content,
    .item-slider-1 .box-feature {
        padding-left: 15px;
    }

    .footer-bottom {
        padding: 15px;
    }

    .teacher-infor {
        padding-right: 0;
    }

    .tf-section.tf-discovery-2 {
        padding-top: 250px !important;
        padding-bottom: 130px !important;
    }

    .about .tf-subcribe {
        padding-bottom: 0 !important;
    }

    .about .tf-feedback {
        padding: 160px 0 100px !important;
    }

    .tf-section.tf-gallery {
        padding: 350px 0 0!important;
    }

    .gallery .tf-section.tf-gallery {
        padding: 0 0 70px !important;
    }

    .tf-section.tf-register {
        padding: 70px 0 300px 0 !important;
    }

    .tf-section {
        padding: 70px 0 !important;
    }
    .tf-section.tf-courses {
        padding: 130px 0 !important;
    }
    .tf-subcribe {
        padding: 50px 0;
    }

    .tf-subcribe {
        padding-bottom: 0 !important;
    }

    .feature-about2 {
        padding: 0 0;
    }
    .tf-section.tf-program-details2,
    .widget-footer,
    .tf-sc-contact {
        margin: 0 0;
    }

    .feature-about2 {
        margin-bottom: 50px;
    }
    .side-bar-shop,
    article.tf-details,
    .title-heading {
        margin-bottom: 40px !important;
    }
    .sc-service {
        margin-bottom: 20px;
    }
    .sc-discovery {
        margin-bottom: 15px;
    }
    .tab-time-table .menu-tab li,
    .tab-faq.flat-tabs .menu-tab li {
        margin-bottom: 10px;
    }
    .fl-services .box-feature,
    .infor-teacher-detail .wrap-couter .progress-couter,
    .sc-pricing,
    .subcribe-wp,
    .sc-artice,
    .sc-discovery-2 {
        margin-bottom: 30px;
    }
    .teacher-infor,
    .heading.st-1 .title-heading,
    .widget-footer .widget {
        margin-bottom: 30px !important;
    }

    .themesflat-pagination.st-2 {
        margin-bottom: 70px;
    }


    .page-title,
    .tf-slider-2 {
        padding: 200px 0 80px 0;
    }

    .widget-business .inner {
        padding: 27px 72px 27px 44px;
    }

    #footer {
        background: var(--primary-color2);
    }

    .tf-counter {
        background-color: var(--primary-color6);
    }

    .subcribe-wp .title {
        line-height: 1.2;
    }

    .item-slider-2 .title {
        font-size: 65px;
    }

    .tf-counter,
    .tf-service {
        background-image: none;
    }

    .sc-contact .wrap .title {
        font-size: 35px;
    }

    .inner-sc-contact .inner-contact ul li:last-child,
    .sc-gallery .title {
        font-size: 18px;
    }

    .heading-btn.st-1 {
        justify-content: start;
    }

    .top-bar-2 .inner-contact {
        margin-left: 50px;
    }

    .item-courses .box-content {
        padding: 15px;
    }

    .fl-register {
        margin: 0 15px;
        padding: 50px 20px;
    }
    .fl-btn.st-14 {
        padding: 0px 60px 0 33px;
    }

    .fl-subcribe {
        background-size: cover;
    }

    .item-slider-1 .wrap {
        font-size: 25px;
    }
    .item-slider-1 .box-content {
        width: 60%;
    }
    .item-slider-1 .box-feature {
        width: 40%;
        padding-top: 50px;
    }
    .inner-page .bg-inner1 {
        max-width: 35%;
    }
    .sc-discovery-about .col-discovery {
        width: calc(50% - 30px);
        margin-left: 30px;
    }
    .sc-discovery-about {
        margin-left: -30px;
    }
    .fx.sc-discovery-about {
        flex-wrap: wrap;
    }
    .sc-discovery-2 {
        background-size: cover;
    }
    .inner-page .sc-employee .box-feature {
        display: -webkit-box;
        display: -moz-box;
        display: -ms-flexbox;
        display: -webkit-flex;
        display: flex;
        justify-content: center;
        margin: 0 auto;
        width: 100%;

    }

    .inner-page .sc-employee .box-content {
        top: 205px;
    }

    .inner-page.teacher .sc-employee {
        margin-bottom: 375px;
    }

    .teacher-image {
        background-size: contain;
        background-position: center left;
    }
    .infor-teacher-detail .wrap-couter {
        flex-wrap: wrap;
    }
    .shop.details .sc-product,
    .infor-teacher-detail .wrap-couter .progress-couter {
        width: calc(50% - 30px);
    }
    .infor-teacher-detail {
        padding: 50px 30px;
    }
    .tf-message .form-message {
        padding-left: 30px;
        padding-right: 30px;
    }
    .tf-teacher-details .teacher-desc,
    .tf-teacher-details .teacher-image,
    .tf-teacher-details .teacher-infor {
        width: calc(100% - 0px);
        margin-left: 0;
        padding-right: 0;
    }
    .tf-teacher-details .teacher-details {
        margin-left: 0;
    }
    .tf-section.tf-calendar2 {
        padding: 100px 0 280px !important;
    }
    .tf-section.tf-gallery {
        padding: 180px 0 0!important;
    }
    .tf-section.tf-service-2 {
        padding: 260px 0 70px !important;
    }
    .tf-section.tf-feedback {
        padding-bottom: 120px !important;
    }
    .teacher .tf-section.tf-teacher-details,
    .teacher .tf-section.tf-counter2 {
        padding-bottom: 100px !important;
    }
    .program-details .tf-section.tf-courses {
        padding: 70px 0 130px!important;
    }
    .classes-details .tf-section.tf-courses {
        padding: 70px 0 210px!important;
    }
    .testimonial .tf-quote {
        margin-top: -70px !important;
    }
    .pricing .flat-tabs .menu-tab {
        position: relative;
        top: 0;
        margin-bottom: 30px;
    }
    .feature-blog-1 {
        top: -95px;
        left: -40px;
    }
    .infor-product .img .img-top {
        max-width: 100%;
    }

    .infor-product .img .img-thumnail .image {
        width: calc(33.333% - 30px);
    }
    .inner-page .tf-subcribe {
        padding: 70px 0;
    }
}
@media (max-width: 991px) and (min-width: 767px) {
    .sc-program .content h3 {
        font-size: 25px;
    }
    .meta-post ul li i {
        font-size: 13px;
        margin-right: 5px;
    }
    .meta-post ul li {
        margin-right: 10px;
        font-size: 12px;
    }
}
@media only screen and (max-width: 767px) {
    .top-bar-2 .header-contact,
    .item-slider-1 .box-feature,
    .comment-list ul.children,
    .item-slider-2 .box-feature,
    .sc-quote .list-author,
    .sc-quote .owl-carousel .owl-dots {
        display: none;
    }
    .dropdown ul {
        left: 0;
    }
    .sc-calendar {
        background-size: cover !important;
    }
    .owl-carousel .owl-dots {
     display: none !important;
    }

    .tf-slider-1 {
        padding: 70px 0 !important;
    }

    .fl-register .row-form .icon {
        right: 10px;
    }

    .classes-details .tf-section.tf-courses {
        padding: 70px 0 100px!important;
    }

    .fl-services .inner .sc-services-2,
    .testimonial .sc-quote .inner,
    .sc-program .image img,
    .mb-w100,
    .sc-about-content,
    .sc-about-feature,
    .item-slider-1 .box-content,
    .sc-quote .inner,
    .sc-contact .inner-sc-contact,
    .sc-contact .wrap,
    .widget-footer .widget,
    .box-blog-grid.st-2 .box-artice,
    .box-artice .box-feature img,
    .tag-article .box-lt, 
    .tag-article .box-rt,
    .quotes-article.st-2 .box-feature,
    .quotes-article.st-2 .box-content,
    .item-slider-2 .box-content {
        width: 100%;
    }
    .feature-about2,
    .infor-teacher-detail .wrap-couter,
    .wrap-couter {
        margin-right: 0;
    }
    .item-courses .box-content {
        margin: -60px 15px 0px;
    }

    .sc-discovery {
        width: 50%;
    }
    .inner-page .bg-inner1 {
        max-width: 44%;
    }
    .item-courses .box-content ul li {
        width: 48%;
    }
    .flat-tabs.tab-shop .content-tab,
    .widget.widget-business,
    .widget-footer .widget,
    .widget.st-3.widget-news,
    .article .wrap.st-1,
    .quotes-article.st-2 .box-content,
    .sc-quote .owl-carousel {
        padding: 0 0;
    }

    .sc-about-content {
        padding-left: 0;
    }

    .sc-contact,
    .quotes-article.st-2,
    .sc-about-1 {
        padding: 30px 15px;
    }

    .widget-logo .wrap,
    .sc-service .title {
        margin-bottom: 0;
    }
    .shop article .jus-bet .show-result,
    .calendar-text,
    .sc-contact .wrap,
    .tag-article .box-lt {
        margin-bottom: 15px;
    }
    .sc-faq.active {
        webkit-transform: translateY(0);
        -ms-transform: translateY(0);
        -o-transform: translateY(0);
        transform: translateY(0);
    }
    .sc-faq.active:hover {
        webkit-transform: translateY(-20px);
        -ms-transform: translateY(-20px);
        -o-transform: translateY(-20px);
        transform: translateY(-20px);
    }
    .classes .item-courses,
    .tf-program-details3 .sc-about-content,
    .sc-faq,
    .wrap-couter,
    .sc-about-feature,
    .sc-gallery.mg-bt,
    .sc-fun-fact,
    .quotes-article.st-1.st-3 {
        margin-bottom: 30px;
    }

    .quotes-article.st-2 .box-content  {
        text-align: center;
    }

    .quotes-article.st-2 .box-content .list-social.fx {
        justify-content: center;
    }

    .sc-contact .inner-sc-contact {
        justify-content: start;
    }

    .title-heading .title {
        font-size: 35px;
    }

    .quotes-article.st-1 .box-content .inner {
        font-size: 20px;
    }

    .quotes-article.st-1 .box-content .author,
    .sc-service .title {
        font-size: 16px;
    }

    .sc-service .sub {
        font-size: 14px;
        line-height: 1.5;
    }

    .fl-register .row-form {
        width: 100%;
        margin-bottom: 20px;
    }
    .fl-subcribe {
        padding: 70px 30px 70px;
    }
    .tf-counter.st-2 {
        background-size: cover;
    }
    .sc-discovery .inner-discovery {
        padding: 30px 15px 60px;
    }
    .form-review form fieldset,
    .form-message fieldset,
    .infor-teacher-detail .wrap-couter .progress-couter {
        width: calc(100% - 30px);
    }
    .classes-details #footer {
        margin-top: -17px;
    }
    .shop article .jus-bet,
    .wrap-calendar {
        flex-wrap: wrap;
    }
    .testimonial .item-quote {
        padding: 40px 20px;
    }

    .sort-by a {
        margin-right: 15px;
        margin-left: 0;
    }

    .home2 .box-parents {
        position: relative;
        margin-left: auto;
    }
    .tf-section.tf-testimonial {
        padding: 170px 0 150px !important;
        margin-top: -120px;
    }

    .home2 .tf-section.tf-quote {
        padding-bottom: 0 !important;
    }
}
@media only screen and (max-width: 600px) {
    .tf-section.tf-discovery-2 {
        padding-top: 380px !important;
        margin-top: -150px;
    }
    .item-courses .box-feature img {
        width: 100%;
    }
    .img-thumnail,
    .wrap-couter {
        flex-wrap: wrap;
    }

    .wrap-couter .progress-couter {
        margin-bottom: 30px;
    }
    .infor-product .img .img-thumnail .image,
    .shop.details .sc-product,
    .sc-product,
    .wrap-couter .progress-couter {
        width: calc(100% - 30px);
    }
    .classes .tf-section.tf-courses {
        margin-top: -85px;
    }

    .page-title .title {
        font-size: 40px;
    }

    .img-thumnail .image {
        margin-bottom: 15px;
    }

}

@media only screen and (max-width: 499px) {
    .testimonial .tf-quote .title-heading .sub-heading svg:first-child,
    .testimonial .tf-testimonial .title-heading .sub-heading svg:first-child,
    .sc-quote .inner .heading .right,
    .header-contact {
        display: none;
    }

    .flat-tabs.tab-shop .menu-tab {
        flex-wrap: wrap;
        justify-content: flex-start;
    }

    .flat-tabs.tab-shop .menu-tab li {
        width: 150px;
        margin-bottom: 10px;
    }

    .fl-subcribe {
        padding: 70px 15px;
    }

    #footer .fl-btn.st-7 {
        padding: 0 30px 0 10px;
    }

    #footer .fl-btn.st-7 span {
        font-size: 15px;
    }

    .image.m-r30 {
        margin: 0;
    }

    #footer .fl-btn.st-7 .inner::before, 
    #footer .fl-btn.st-7 .inner::after {
        right: -20px;
        font-size: 15px;
    }
    .fl-subcribe .subcribe-form form input {
        padding: 26px;
    }
    .sc-contact .inner-sc-contact {
        display: block;
    }
    .tf-program-details2 .sc-about-2 .inner ul li,
    .sc-about-2 .inner ul li,
    .fl-services .inner .sc-services-2,
    .sc-discovery {
        width: 100%;
    }

    #sidebar .widget {
        padding: 30px 15px !important;
    }

    .sc-contact .inner-sc-contact .box-btn,
    .widget-logo .wrap {
        margin-bottom: 15px;
    } 

    .btn-menu {
        right: 15px;
    }

    .top-bar p {
        font-size: 14px;
    }

    .fl-btn.st-6 {
        margin: 0 auto;
        padding:0 80px 0 60px;
    }

    .item-fb {
        width: 360px;
        background-size: cover;
        height: auto;
    }
    .tf-section.tf-register {
        padding: 30px 0 200px 0 !important;
    }
    .item-slider-1 .box-btn {
        flex-wrap: wrap;
    }
    .item-slider-1 .box-btn .fl-btn {
        margin-bottom: 10px;
    }
    .sc-about-2 .inner ul {
        flex-wrap: wrap;
    }
    .inner-page .bg-inner1 {
        max-width: 55%;
    }
    .sc-event-box.style2,
    .tf-about .wrap-image > .fx {
        display: block;
    }
    .tf-about .wrap-image .image {
        margin-bottom: 30px;
    }

    .tf-about .wrap-image .image,
    .sc-event-box.style2 .image {
        padding: 0;
    }
    .sc-event-box.style2 .content,
    .sc-event-box.style2 .image{
        width: 100%;
    }
    .tf-about .wrap-image .fx .image,
    .sc-discovery-about .col-discovery {
        width: calc(100% - 30px);
        margin-left: 30px;
    }
    .about .tf-counter.st-2 {
        margin-top: -100px;
    }
    .sc-program .content h3 {
        font-size: 26px;
    }
    #sidebar.classe-details .widget .inner-infor {
        padding: 0 15px 20px;
    }
    .sc-event-box.style2 .content {
        padding:30px;
    }
    .calendar-text {
        padding: 13px 30px 19px 30px !important;
    }
    .form-message .heading {
        margin-bottom: 25px;
        font-size: 37px;
    }
    .form-message form button {
        margin-left: 30px !important;

    }
    .inner-page .box-parents {
        position: relative;
        margin-left: auto;
        margin-bottom: 30px;
        right: 0;
    }
    .pricing .flat-tabs .menu-tab {
        flex-wrap: wrap;
        justify-content: flex-start;
    }
}
    @media only screen and (max-width: 435px) {
        .teacher-image {
            background: none;
            padding: 0;
            margin-right: 0;
        }
        .teacher-image img {
            width: 100%;
        }

        .tf-testimonial .item-fb {
            width: 330px;
            height: auto;
            padding: 38px 20px 36px 20px;
        }
        .teacher-image img {
            -webkit-mask-image: none;
        }
        .contact .title-heading .sub-heading svg:first-child {
            display: none;
        }
    }
