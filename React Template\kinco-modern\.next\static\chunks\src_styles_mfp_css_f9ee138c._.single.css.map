{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/mfp.css"], "sourcesContent": ["/* Magnific Popup CSS */\r\nimg.mfp-img {\r\n  box-shadow: 0 0 8px rgb(0 0 0 / 60%);\r\n  position: absolute;\r\n  max-height: 392px;\r\n  padding: 0 !important;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n}\r\n.mfp-img-container .mfp-content {\r\n  max-width: 400px !important;\r\n}\r\n.mfp-img-container .mfp-close {\r\n  top: -110px;\r\n  right: -24px;\r\n}\r\n.main-menu li.active > a,\r\n.header-navigation .main-menu ul > li.has-children.active > a:after {\r\n  color: #ff344f !important;\r\n}\r\n\r\nspan.current {\r\n  color: unset !important;\r\n}\r\n.owl-carousel {\r\n  display: unset !important;\r\n}\r\n\r\n.py-50 {\r\n  padding: 50px 0;\r\n}\r\n\r\n.progress-block .graph-outer .count-box {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  position: absolute;\r\n  left: 0;\r\n  top: 50% !important;\r\n  width: 111%;\r\n  margin-top: 0px !important;\r\n}\r\n\r\n.block-fourty-six .ul {\r\n  margin-bottom: 5px;\r\n}\r\n.block-fourty-six .ul i {\r\n  color: #be9a78;\r\n}\r\n\r\n.map-section iframe {\r\n  height: 600px;\r\n  width: 100%;\r\n}\r\n.mfp-bg {\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  z-index: 1042;\r\n  overflow: hidden;\r\n  position: fixed;\r\n  background: #0b0b0b;\r\n  opacity: 0.8;\r\n}\r\n\r\n.mfp-wrap {\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  z-index: 1043;\r\n  position: fixed;\r\n  outline: none !important;\r\n  -webkit-backface-visibility: hidden;\r\n}\r\n\r\n.mfp-container {\r\n  text-align: center;\r\n  position: absolute;\r\n  width: 100%;\r\n  height: 100%;\r\n  left: 0;\r\n  top: 0;\r\n  padding: 0 8px;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.mfp-container:before {\r\n  content: \"\";\r\n  display: inline-block;\r\n  height: 100%;\r\n  vertical-align: middle;\r\n}\r\n\r\n.mfp-align-top .mfp-container:before {\r\n  display: none;\r\n}\r\n\r\n.mfp-content {\r\n  position: relative;\r\n  display: inline-block;\r\n  vertical-align: middle;\r\n  margin: 0 auto;\r\n  text-align: left;\r\n  z-index: 1045;\r\n}\r\n\r\n.mfp-inline-holder .mfp-content,\r\n.mfp-ajax-holder .mfp-content {\r\n  width: 100%;\r\n  cursor: auto;\r\n}\r\n\r\n.mfp-ajax-cur {\r\n  cursor: progress;\r\n}\r\n\r\n.mfp-zoom-out-cur,\r\n.mfp-zoom-out-cur .mfp-image-holder .mfp-close {\r\n  cursor: -moz-zoom-out;\r\n  cursor: -webkit-zoom-out;\r\n  cursor: zoom-out;\r\n}\r\n\r\n.mfp-zoom {\r\n  cursor: pointer;\r\n  cursor: -webkit-zoom-in;\r\n  cursor: -moz-zoom-in;\r\n  cursor: zoom-in;\r\n}\r\n\r\n.mfp-auto-cursor .mfp-content {\r\n  cursor: auto;\r\n}\r\n\r\n.mfp-close,\r\n.mfp-arrow,\r\n.mfp-preloader,\r\n.mfp-counter {\r\n  -webkit-user-select: none;\r\n  -moz-user-select: none;\r\n  user-select: none;\r\n}\r\n\r\n.mfp-loading.mfp-figure {\r\n  display: none;\r\n}\r\n\r\n.mfp-hide {\r\n  display: none !important;\r\n}\r\n\r\n.mfp-preloader {\r\n  color: #ccc;\r\n  position: absolute;\r\n  top: 50%;\r\n  width: auto;\r\n  text-align: center;\r\n  margin-top: -0.8em;\r\n  left: 8px;\r\n  right: 8px;\r\n  z-index: 1044;\r\n}\r\n.mfp-preloader a {\r\n  color: #ccc;\r\n}\r\n.mfp-preloader a:hover {\r\n  color: #fff;\r\n}\r\n\r\n.mfp-s-ready .mfp-preloader {\r\n  display: none;\r\n}\r\n\r\n.mfp-s-error .mfp-content {\r\n  display: none;\r\n}\r\n\r\nbutton.mfp-close,\r\nbutton.mfp-arrow {\r\n  overflow: visible;\r\n  cursor: pointer;\r\n  background: transparent;\r\n  border: 0;\r\n  -webkit-appearance: none;\r\n  display: block;\r\n  outline: none;\r\n  padding: 0;\r\n  z-index: 1046;\r\n  box-shadow: none;\r\n  touch-action: manipulation;\r\n}\r\n\r\nbutton::-moz-focus-inner {\r\n  padding: 0;\r\n  border: 0;\r\n}\r\n\r\n.mfp-close {\r\n  width: 44px;\r\n  height: 44px;\r\n  line-height: 44px;\r\n  position: absolute;\r\n  right: 0;\r\n  top: 0;\r\n  text-decoration: none;\r\n  text-align: center;\r\n  opacity: 0.65;\r\n  padding: 0 0 18px 10px;\r\n  color: #fff;\r\n  font-style: normal;\r\n  font-size: 28px;\r\n  font-family: Arial, Baskerville, monospace;\r\n}\r\n.mfp-close:hover,\r\n.mfp-close:focus {\r\n  opacity: 1;\r\n}\r\n.mfp-close:active {\r\n  top: 1px;\r\n}\r\n\r\n.mfp-close-btn-in .mfp-close {\r\n  color: #333;\r\n}\r\n\r\n.mfp-image-holder .mfp-close,\r\n.mfp-iframe-holder .mfp-close {\r\n  color: #fff;\r\n  right: -6px;\r\n  text-align: right;\r\n  padding-right: 6px;\r\n  width: 100%;\r\n}\r\n\r\n.mfp-counter {\r\n  position: absolute;\r\n  top: 0;\r\n  right: 0;\r\n  color: #ccc;\r\n  font-size: 12px;\r\n  line-height: 18px;\r\n  white-space: nowrap;\r\n}\r\n\r\n.mfp-arrow {\r\n  position: absolute;\r\n  opacity: 0.65;\r\n  margin: 0;\r\n  top: 50%;\r\n  margin-top: -55px;\r\n  padding: 0;\r\n  width: 90px;\r\n  height: 110px;\r\n  -webkit-tap-highlight-color: transparent;\r\n}\r\n.mfp-arrow:active {\r\n  margin-top: -54px;\r\n}\r\n.mfp-arrow:hover,\r\n.mfp-arrow:focus {\r\n  opacity: 1;\r\n}\r\n.mfp-arrow:before,\r\n.mfp-arrow:after {\r\n  content: \"\";\r\n  display: block;\r\n  width: 0;\r\n  height: 0;\r\n  position: absolute;\r\n  left: 0;\r\n  top: 0;\r\n  margin-top: 35px;\r\n  margin-left: 35px;\r\n  border: medium inset transparent;\r\n}\r\n.mfp-arrow:after {\r\n  border-top-width: 13px;\r\n  border-bottom-width: 13px;\r\n  top: 8px;\r\n}\r\n.mfp-arrow:before {\r\n  border-top-width: 21px;\r\n  border-bottom-width: 21px;\r\n  opacity: 0.7;\r\n}\r\n\r\n.mfp-arrow-left {\r\n  left: 0;\r\n}\r\n.mfp-arrow-left:after {\r\n  border-right: 17px solid #fff;\r\n  margin-left: 31px;\r\n}\r\n.mfp-arrow-left:before {\r\n  margin-left: 25px;\r\n  border-right: 27px solid #3f3f3f;\r\n}\r\n\r\n.mfp-arrow-right {\r\n  right: 0;\r\n}\r\n.mfp-arrow-right:after {\r\n  border-left: 17px solid #fff;\r\n  margin-left: 39px;\r\n}\r\n.mfp-arrow-right:before {\r\n  border-left: 27px solid #3f3f3f;\r\n}\r\n\r\n.mfp-iframe-holder {\r\n  padding-top: 40px;\r\n  padding-bottom: 40px;\r\n}\r\n.mfp-iframe-holder .mfp-content {\r\n  line-height: 0;\r\n  width: 100%;\r\n  max-width: 900px;\r\n}\r\n.mfp-iframe-holder .mfp-close {\r\n  top: -40px;\r\n}\r\n\r\n.mfp-iframe-scaler {\r\n  width: 100%;\r\n  height: 0;\r\n  overflow: hidden;\r\n  padding-top: 56.25%;\r\n}\r\n.mfp-iframe-scaler iframe {\r\n  position: absolute;\r\n  display: block;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  box-shadow: 0 0 8px rgba(0, 0, 0, 0.6);\r\n  background: #000;\r\n}\r\n\r\n/* Main image in popup */\r\nimg.mfp-img {\r\n  width: auto;\r\n  max-width: 100%;\r\n  height: auto;\r\n  display: block;\r\n  line-height: 0;\r\n  box-sizing: border-box;\r\n  padding: 40px 0 40px;\r\n  margin: 0 auto;\r\n}\r\n\r\n/* The shadow behind the image */\r\n.mfp-figure {\r\n  line-height: 0;\r\n}\r\n.mfp-figure:after {\r\n  content: \"\";\r\n  position: absolute;\r\n  left: 0;\r\n  top: 40px;\r\n  bottom: 40px;\r\n  display: block;\r\n  right: 0;\r\n  width: auto;\r\n  height: auto;\r\n  z-index: -1;\r\n  box-shadow: 0 0 8px rgba(0, 0, 0, 0.6);\r\n  background: #444;\r\n}\r\n.mfp-figure small {\r\n  color: #bdbdbd;\r\n  display: block;\r\n  font-size: 12px;\r\n  line-height: 14px;\r\n}\r\n.mfp-figure figure {\r\n  margin: 0;\r\n}\r\n\r\n.mfp-bottom-bar {\r\n  margin-top: -36px;\r\n  position: absolute;\r\n  top: 100%;\r\n  left: 0;\r\n  width: 100%;\r\n  cursor: auto;\r\n}\r\n\r\n.mfp-title {\r\n  text-align: left;\r\n  line-height: 18px;\r\n  color: #f3f3f3;\r\n  word-wrap: break-word;\r\n  padding-right: 36px;\r\n}\r\n\r\n.mfp-image-holder .mfp-content {\r\n  max-width: 100%;\r\n}\r\n\r\n.mfp-gallery .mfp-image-holder .mfp-figure {\r\n  cursor: pointer;\r\n}\r\n\r\n@media screen and (max-width: 800px) and (orientation: landscape),\r\n  screen and (max-height: 300px) {\r\n  /**\r\n             * Remove all paddings around the image on small screen\r\n             */\r\n  .mfp-img-mobile .mfp-image-holder {\r\n    padding-left: 0;\r\n    padding-right: 0;\r\n  }\r\n  .mfp-img-mobile img.mfp-img {\r\n    padding: 0;\r\n  }\r\n  .mfp-img-mobile .mfp-figure:after {\r\n    top: 0;\r\n    bottom: 0;\r\n  }\r\n  .mfp-img-mobile .mfp-figure small {\r\n    display: inline;\r\n    margin-left: 5px;\r\n  }\r\n  .mfp-img-mobile .mfp-bottom-bar {\r\n    background: rgba(0, 0, 0, 0.6);\r\n    bottom: 0;\r\n    margin: 0;\r\n    top: auto;\r\n    padding: 3px 5px;\r\n    position: fixed;\r\n    box-sizing: border-box;\r\n  }\r\n  .mfp-img-mobile .mfp-bottom-bar:empty {\r\n    padding: 0;\r\n  }\r\n  .mfp-img-mobile .mfp-counter {\r\n    right: 5px;\r\n    top: 3px;\r\n  }\r\n  .mfp-img-mobile .mfp-close {\r\n    top: 0;\r\n    right: 0;\r\n    width: 35px;\r\n    height: 35px;\r\n    line-height: 35px;\r\n    background: rgba(0, 0, 0, 0.6);\r\n    position: fixed;\r\n    text-align: center;\r\n    padding: 0;\r\n  }\r\n}\r\n\r\n@media all and (max-width: 900px) {\r\n  .mfp-arrow {\r\n    -webkit-transform: scale(0.75);\r\n    transform: scale(0.75);\r\n  }\r\n  .mfp-arrow-left {\r\n    -webkit-transform-origin: 0;\r\n    transform-origin: 0;\r\n  }\r\n  .mfp-arrow-right {\r\n    -webkit-transform-origin: 100%;\r\n    transform-origin: 100%;\r\n  }\r\n  .mfp-container {\r\n    padding-left: 6px;\r\n    padding-right: 6px;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AACA;;;;;;;;;;AASA;;;;AAGA;;;;;AAIA;;;;AAKA;;;;AAGA;;;;AAIA;;;;AAIA;;;;;;;;;;;AAWA;;;;AAGA;;;;AAIA;;;;;AAIA;;;;;;;;;;;;AAYA;;;;;;;;;;;AAWA;;;;;;;;;;;AAWA;;;;;;;AAOA;;;;AAIA;;;;;;;;;AASA;;;;;AAMA;;;;AAIA;;;;;;AAOA;;;;;;;AAOA;;;;AAIA;;;;;;AASA;;;;AAIA;;;;AAIA;;;;;;;;;;;;AAWA;;;;AAGA;;;;AAIA;;;;AAQA;;;;;;;;;;;;;;AAeA;;;;;AAKA;;;;;;;;;;;;;;;;;AAgBA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;;;AASA;;;;;;;;;;AAUA;;;;;;;;;;;AAWA;;;;AAGA;;;;AAIA;;;;;;;;;;;;;AAaA;;;;;;AAKA;;;;;;AAMA;;;;AAGA;;;;;AAIA;;;;;AAKA;;;;AAGA;;;;;AAIA;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;AAIA;;;;;;;AAMA;;;;;;;;;;;AAYA;;;;;;;;;;;AAYA;;;;AAGA;;;;;;;;;;;;AAcA;;;;;;;AAMA;;;;AAIA;;;;;;;;;AASA;;;;;;;;AAQA;;;;AAIA;;;;AAIA;EAKE;;;;;EAIA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;;;;;;EASA;;;;EAGA;;;;;EAIA;;;;;;;;;;;;;AAaF;EACE;;;;;EAIA;;;;;EAIA;;;;;EAIA"}}]}