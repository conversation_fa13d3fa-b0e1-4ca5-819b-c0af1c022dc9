'use client';

import Link from "next/link";

export default function Blog() {
  const blogPosts = [
    {
      id: 1,
      title: "5 Tips for Preparing Your Child for Kindergarten",
      excerpt: "Starting kindergarten is a big milestone. Here are some practical tips to help your child transition smoothly.",
      date: "December 15, 2024",
      author: "<PERSON>",
      image: "/assets/images/common/sc-blog1.jpg",
      category: "Education"
    },
    {
      id: 2,
      title: "The Importance of Play-Based Learning",
      excerpt: "Discover how play-based learning helps children develop essential skills while having fun.",
      date: "December 10, 2024", 
      author: "<PERSON>",
      image: "/assets/images/common/sc-blog2.jpg",
      category: "Learning"
    },
    {
      id: 3,
      title: "Building Social Skills in Early Childhood",
      excerpt: "Learn effective strategies to help your child develop strong social and emotional skills.",
      date: "December 5, 2024",
      author: "<PERSON>", 
      image: "/assets/images/common/sc-blog3.jpg",
      category: "Development"
    },
    {
      id: 4,
      title: "Healthy Eating Habits for Growing Minds",
      excerpt: "Nutrition plays a crucial role in child development. Here's how to establish healthy eating habits.",
      date: "November 30, 2024",
      author: "<PERSON>",
      image: "/assets/images/common/sc-program1.jpg",
      category: "Health"
    },
    {
      id: 5,
      title: "Creating a Learning Environment at Home",
      excerpt: "Simple ways to create an engaging and educational environment that supports your child's learning.",
      date: "November 25, 2024",
      author: "David Martinez",
      image: "/assets/images/common/sc-program2.jpg", 
      category: "Home Learning"
    },
    {
      id: 6,
      title: "The Benefits of Outdoor Education",
      excerpt: "Explore how outdoor activities and nature-based learning contribute to child development.",
      date: "November 20, 2024",
      author: "Jennifer Lee",
      image: "/assets/images/common/sc-program3.jpg",
      category: "Outdoor Learning"
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex justify-between items-center">
            <Link href="/" className="flex items-center">
              <img src="/assets/images/logo/logodark-2.png" alt="Kinco School" className="h-12" />
            </Link>
            <nav className="hidden md:flex space-x-8">
              <Link href="/" className="text-gray-600 hover:text-blue-600 transition-colors">Home</Link>
              <Link href="/about" className="text-gray-600 hover:text-blue-600 transition-colors">About</Link>
              <Link href="/classes" className="text-gray-600 hover:text-blue-600 transition-colors">Classes</Link>
              <Link href="/program" className="text-gray-600 hover:text-blue-600 transition-colors">Programs</Link>
              <Link href="/teacher" className="text-gray-600 hover:text-blue-600 transition-colors">Teachers</Link>
              <Link href="/blog" className="text-blue-600 font-semibold">Blog</Link>
              <Link href="/contact" className="text-gray-600 hover:text-blue-600 transition-colors">Contact</Link>
            </nav>
          </div>
        </div>
      </header>

      {/* Page Banner */}
      <section className="bg-gradient-to-r from-blue-500 to-purple-600 text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl font-bold mb-4">Our Blog</h1>
          <nav className="text-blue-200">
            <Link href="/" className="hover:text-white">Home</Link>
            <span className="mx-2">/</span>
            <span>Blog</span>
          </nav>
        </div>
      </section>

      {/* Blog Posts */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold mb-4 text-gray-800">Latest Articles</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Stay updated with the latest insights, tips, and news about early childhood education
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {blogPosts.map((post) => (
              <article key={post.id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                <img 
                  src={post.image} 
                  alt={post.title}
                  className="w-full h-48 object-cover"
                />
                <div className="p-6">
                  <div className="flex items-center justify-between mb-3">
                    <span className="bg-blue-100 text-blue-800 text-xs font-semibold px-2 py-1 rounded">
                      {post.category}
                    </span>
                    <span className="text-gray-500 text-sm">{post.date}</span>
                  </div>
                  <h3 className="text-xl font-bold mb-3 text-gray-800 hover:text-blue-600 transition-colors">
                    <Link href={`/blog/${post.id}`}>
                      {post.title}
                    </Link>
                  </h3>
                  <p className="text-gray-600 mb-4">{post.excerpt}</p>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">By {post.author}</span>
                    <Link 
                      href={`/blog/${post.id}`}
                      className="text-blue-600 hover:text-blue-800 font-semibold text-sm"
                    >
                      Read More →
                    </Link>
                  </div>
                </div>
              </article>
            ))}
          </div>

          {/* Pagination */}
          <div className="flex justify-center mt-12">
            <nav className="flex space-x-2">
              <button className="px-3 py-2 text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                Previous
              </button>
              <button className="px-3 py-2 text-white bg-blue-600 border border-blue-600 rounded-md">
                1
              </button>
              <button className="px-3 py-2 text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                2
              </button>
              <button className="px-3 py-2 text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                3
              </button>
              <button className="px-3 py-2 text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                Next
              </button>
            </nav>
          </div>
        </div>
      </section>

      {/* Newsletter Signup */}
      <section className="bg-blue-600 text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">Stay Updated</h2>
          <p className="text-xl mb-8 text-blue-100">
            Subscribe to our newsletter for the latest educational tips and school updates
          </p>
          <div className="max-w-md mx-auto flex">
            <input 
              type="email" 
              placeholder="Enter your email"
              className="flex-1 px-4 py-3 rounded-l-lg text-gray-900"
            />
            <button className="bg-yellow-400 text-blue-900 px-6 py-3 rounded-r-lg font-semibold hover:bg-yellow-300 transition-colors">
              Subscribe
            </button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-800 text-white py-8">
        <div className="container mx-auto px-4 text-center">
          <div className="mb-4">
            <Link href="/" className="text-2xl font-bold text-blue-400">
              Kinco School
            </Link>
          </div>
          <p className="text-gray-400 mb-4">
            Providing quality education and care for your children
          </p>
          <div className="flex justify-center space-x-6">
            <Link href="/about" className="text-gray-400 hover:text-white">About</Link>
            <Link href="/classes" className="text-gray-400 hover:text-white">Classes</Link>
            <Link href="/blog" className="text-white">Blog</Link>
            <Link href="/contact" className="text-gray-400 hover:text-white">Contact</Link>
          </div>
          <div className="mt-6 pt-6 border-t border-gray-700">
            <p className="text-gray-400">© 2024 Kinco School. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
