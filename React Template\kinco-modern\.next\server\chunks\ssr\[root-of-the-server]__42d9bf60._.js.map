{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kinco-day-care-kindergarten-react-template-30-06-2025/Kinco/React%20Template/kinco-modern/src/components/Header.js"], "sourcesContent": ["'use client';\n\nimport Link from \"next/link\";\nimport { usePathname } from \"next/navigation\";\n\nexport default function Header() {\n  const pathname = usePathname();\n\n  const isActive = (path) => {\n    return pathname === path;\n  };\n\n  return (\n    <header className=\"header-style-1 bg-white shadow-lg fixed-top\">\n      <div className=\"container-fluid\">\n        <div className=\"row\">\n          <div className=\"col-12\">\n            <div className=\"header-inner d-flex justify-content-between align-items-center py-3\">\n              <div className=\"header-logo\">\n                <Link href=\"/\" className=\"d-flex align-items-center text-decoration-none\">\n                  <div className=\"logo-placeholder d-flex align-items-center\">\n                    <div className=\"logo-icon bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2\"\n                         style={{width: '40px', height: '40px'}}>\n                      <i className=\"fa fa-graduation-cap\"></i>\n                    </div>\n                    <div>\n                      <h4 className=\"mb-0 text-primary fw-bold\">Smart</h4>\n                      <small className=\"text-muted\">Kinco</small>\n                    </div>\n                  </div>\n                </Link>\n              </div>\n              <nav className=\"main-menu d-none d-lg-block\">\n                <ul className=\"d-flex list-unstyled mb-0 align-items-center\">\n                  <li className=\"menu-item\">\n                    <Link href=\"/\" className={`menu-link text-decoration-none px-3 py-2 rounded-pill transition-all ${isActive('/') ? 'fw-semibold text-white bg-primary' : 'text-dark hover-primary'}`}>\n                      Home\n                    </Link>\n                  </li>\n                  <li className=\"menu-item\">\n                    <Link href=\"/about\" className={`menu-link text-decoration-none px-3 py-2 rounded-pill transition-all ${isActive('/about') ? 'fw-semibold text-white bg-primary' : 'text-dark hover-primary'}`}>\n                      About\n                    </Link>\n                  </li>\n                  <li className=\"menu-item\">\n                    <Link href=\"/classes\" className={`menu-link text-decoration-none px-3 py-2 rounded-pill transition-all ${isActive('/classes') ? 'fw-semibold text-white bg-primary' : 'text-dark hover-primary'}`}>\n                      Classes\n                    </Link>\n                  </li>\n                  <li className=\"menu-item\">\n                    <Link href=\"/program\" className={`menu-link text-decoration-none px-3 py-2 rounded-pill transition-all ${isActive('/program') ? 'fw-semibold text-white bg-primary' : 'text-dark hover-primary'}`}>\n                      Programs\n                    </Link>\n                  </li>\n                  <li className=\"menu-item\">\n                    <Link href=\"/teacher\" className={`menu-link text-decoration-none px-3 py-2 rounded-pill transition-all ${isActive('/teacher') ? 'fw-semibold text-white bg-primary' : 'text-dark hover-primary'}`}>\n                      Teachers\n                    </Link>\n                  </li>\n                  <li className=\"menu-item\">\n                    <Link href=\"/gallery\" className={`menu-link text-decoration-none px-3 py-2 rounded-pill transition-all ${isActive('/gallery') ? 'fw-semibold text-white bg-primary' : 'text-dark hover-primary'}`}>\n                      Gallery\n                    </Link>\n                  </li>\n                  <li className=\"menu-item\">\n                    <Link href=\"/blog\" className={`menu-link text-decoration-none px-3 py-2 rounded-pill transition-all ${isActive('/blog') ? 'fw-semibold text-white bg-primary' : 'text-dark hover-primary'}`}>\n                      Blog\n                    </Link>\n                  </li>\n                  <li className=\"menu-item\">\n                    <Link href=\"/events\" className={`menu-link text-decoration-none px-3 py-2 rounded-pill transition-all ${isActive('/events') ? 'fw-semibold text-white bg-primary' : 'text-dark hover-primary'}`}>\n                      Events\n                    </Link>\n                  </li>\n                  <li className=\"menu-item\">\n                    <Link href=\"/contact\" className={`menu-link text-decoration-none px-3 py-2 rounded-pill transition-all ${isActive('/contact') ? 'fw-semibold text-white bg-primary' : 'text-dark hover-primary'}`}>\n                      Contact\n                    </Link>\n                  </li>\n                </ul>\n              </nav>\n              <div className=\"header-mobile d-lg-none\">\n                <button className=\"mobile-menu-toggle btn btn-primary\">\n                  <i className=\"fa fa-bars\"></i>\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,WAAW,CAAC;QAChB,OAAO,aAAa;IACtB;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CACvB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;gDACV,OAAO;oDAAC,OAAO;oDAAQ,QAAQ;gDAAM;0DACxC,cAAA,8OAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA4B;;;;;;kEAC1C,8OAAC;wDAAM,WAAU;kEAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAKtC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAI,WAAW,CAAC,qEAAqE,EAAE,SAAS,OAAO,sCAAsC,2BAA2B;0DAAE;;;;;;;;;;;sDAIvL,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAW,CAAC,qEAAqE,EAAE,SAAS,YAAY,sCAAsC,2BAA2B;0DAAE;;;;;;;;;;;sDAIjM,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAW,CAAC,qEAAqE,EAAE,SAAS,cAAc,sCAAsC,2BAA2B;0DAAE;;;;;;;;;;;sDAIrM,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAW,CAAC,qEAAqE,EAAE,SAAS,cAAc,sCAAsC,2BAA2B;0DAAE;;;;;;;;;;;sDAIrM,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAW,CAAC,qEAAqE,EAAE,SAAS,cAAc,sCAAsC,2BAA2B;0DAAE;;;;;;;;;;;sDAIrM,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAW,CAAC,qEAAqE,EAAE,SAAS,cAAc,sCAAsC,2BAA2B;0DAAE;;;;;;;;;;;sDAIrM,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAW,CAAC,qEAAqE,EAAE,SAAS,WAAW,sCAAsC,2BAA2B;0DAAE;;;;;;;;;;;sDAI/L,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAU,WAAW,CAAC,qEAAqE,EAAE,SAAS,aAAa,sCAAsC,2BAA2B;0DAAE;;;;;;;;;;;sDAInM,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAW,CAAC,qEAAqE,EAAE,SAAS,cAAc,sCAAsC,2BAA2B;0DAAE;;;;;;;;;;;;;;;;;;;;;;0CAMzM,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAO,WAAU;8CAChB,cAAA,8OAAC;wCAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS/B", "debugId": null}}, {"offset": {"line": 345, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kinco-day-care-kindergarten-react-template-30-06-2025/Kinco/React%20Template/kinco-modern/src/components/Footer.js"], "sourcesContent": ["'use client';\n\nimport Link from \"next/link\";\n\nexport default function Footer() {\n  return (\n    <footer className=\"footer-style-2 bg-dark text-white\">\n      <div className=\"container\">\n        <div className=\"footer-content py-5\">\n          <div className=\"row g-4\">\n            <div className=\"col-lg-3 col-md-6\">\n              <div className=\"footer-widget\">\n                <div className=\"footer-logo mb-4\">\n                  <Link href=\"/\">\n                    <img src=\"/assets/images/logo/logofootert.png\" alt=\"Kinco School\" className=\"img-fluid\" style={{maxHeight: '50px'}} />\n                  </Link>\n                </div>\n                <p className=\"text-white-50 mb-4\">\n                  Providing quality education and care for your children in a safe, nurturing environment.\n                </p>\n                <div className=\"footer-social d-flex gap-3\">\n                  <a href=\"#\" className=\"social-link text-white-50 hover-primary\">\n                    <i className=\"fa fa-facebook\"></i>\n                  </a>\n                  <a href=\"#\" className=\"social-link text-white-50 hover-primary\">\n                    <i className=\"fa fa-twitter\"></i>\n                  </a>\n                  <a href=\"#\" className=\"social-link text-white-50 hover-primary\">\n                    <i className=\"fa fa-instagram\"></i>\n                  </a>\n                  <a href=\"#\" className=\"social-link text-white-50 hover-primary\">\n                    <i className=\"fa fa-linkedin\"></i>\n                  </a>\n                </div>\n              </div>\n            </div>\n            <div className=\"col-lg-3 col-md-6\">\n              <div className=\"footer-widget\">\n                <h3 className=\"footer-title h5 fw-bold mb-4\">Quick Links</h3>\n                <ul className=\"footer-menu list-unstyled\">\n                  <li className=\"mb-2\"><Link href=\"/about\" className=\"text-white-50 text-decoration-none hover-primary\">About Us</Link></li>\n                  <li className=\"mb-2\"><Link href=\"/classes\" className=\"text-white-50 text-decoration-none hover-primary\">Classes</Link></li>\n                  <li className=\"mb-2\"><Link href=\"/program\" className=\"text-white-50 text-decoration-none hover-primary\">Programs</Link></li>\n                  <li className=\"mb-2\"><Link href=\"/teacher\" className=\"text-white-50 text-decoration-none hover-primary\">Teachers</Link></li>\n                  <li className=\"mb-2\"><Link href=\"/gallery\" className=\"text-white-50 text-decoration-none hover-primary\">Gallery</Link></li>\n                </ul>\n              </div>\n            </div>\n            <div className=\"col-lg-3 col-md-6\">\n              <div className=\"footer-widget\">\n                <h3 className=\"footer-title h5 fw-bold mb-4\">Programs</h3>\n                <ul className=\"footer-menu list-unstyled\">\n                  <li className=\"mb-2\"><a href=\"#\" className=\"text-white-50 text-decoration-none hover-primary\">Drawing & Painting</a></li>\n                  <li className=\"mb-2\"><a href=\"#\" className=\"text-white-50 text-decoration-none hover-primary\">Computer Learning</a></li>\n                  <li className=\"mb-2\"><a href=\"#\" className=\"text-white-50 text-decoration-none hover-primary\">Basic English</a></li>\n                  <li className=\"mb-2\"><a href=\"#\" className=\"text-white-50 text-decoration-none hover-primary\">Music & Dance</a></li>\n                  <li className=\"mb-2\"><a href=\"#\" className=\"text-white-50 text-decoration-none hover-primary\">Sports Activities</a></li>\n                </ul>\n              </div>\n            </div>\n            <div className=\"col-lg-3 col-md-6\">\n              <div className=\"footer-widget\">\n                <h3 className=\"footer-title h5 fw-bold mb-4\">Contact Info</h3>\n                <div className=\"footer-contact\">\n                  <div className=\"contact-item d-flex align-items-start mb-3\">\n                    <i className=\"fa fa-map-marker text-primary me-3 mt-1\"></i>\n                    <span className=\"text-white-50\">123 Education Street, Learning City, LC 12345</span>\n                  </div>\n                  <div className=\"contact-item d-flex align-items-center mb-3\">\n                    <i className=\"fa fa-phone text-primary me-3\"></i>\n                    <span className=\"text-white-50\">(*************</span>\n                  </div>\n                  <div className=\"contact-item d-flex align-items-center mb-3\">\n                    <i className=\"fa fa-envelope text-primary me-3\"></i>\n                    <span className=\"text-white-50\"><EMAIL></span>\n                  </div>\n                  <div className=\"contact-item d-flex align-items-center\">\n                    <i className=\"fa fa-clock-o text-primary me-3\"></i>\n                    <span className=\"text-white-50\">Mon-Fri: 7AM-6PM</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n        <div className=\"footer-bottom border-top border-secondary pt-4\">\n          <div className=\"row align-items-center\">\n            <div className=\"col-12 text-center\">\n              <p className=\"text-white-50 mb-0\">© 2024 Kinco School. All rights reserved.</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;0DACT,cAAA,8OAAC;oDAAI,KAAI;oDAAsC,KAAI;oDAAe,WAAU;oDAAY,OAAO;wDAAC,WAAW;oDAAM;;;;;;;;;;;;;;;;sDAGrH,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAGlC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,MAAK;oDAAI,WAAU;8DACpB,cAAA,8OAAC;wDAAE,WAAU;;;;;;;;;;;8DAEf,8OAAC;oDAAE,MAAK;oDAAI,WAAU;8DACpB,cAAA,8OAAC;wDAAE,WAAU;;;;;;;;;;;8DAEf,8OAAC;oDAAE,MAAK;oDAAI,WAAU;8DACpB,cAAA,8OAAC;wDAAE,WAAU;;;;;;;;;;;8DAEf,8OAAC;oDAAE,MAAK;oDAAI,WAAU;8DACpB,cAAA,8OAAC;wDAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAKrB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA+B;;;;;;sDAC7C,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAG,WAAU;8DAAO,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAS,WAAU;kEAAmD;;;;;;;;;;;8DACtG,8OAAC;oDAAG,WAAU;8DAAO,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;kEAAmD;;;;;;;;;;;8DACxG,8OAAC;oDAAG,WAAU;8DAAO,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;kEAAmD;;;;;;;;;;;8DACxG,8OAAC;oDAAG,WAAU;8DAAO,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;kEAAmD;;;;;;;;;;;8DACxG,8OAAC;oDAAG,WAAU;8DAAO,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;kEAAmD;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAI9G,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA+B;;;;;;sDAC7C,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAG,WAAU;8DAAO,cAAA,8OAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAmD;;;;;;;;;;;8DAC9F,8OAAC;oDAAG,WAAU;8DAAO,cAAA,8OAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAmD;;;;;;;;;;;8DAC9F,8OAAC;oDAAG,WAAU;8DAAO,cAAA,8OAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAmD;;;;;;;;;;;8DAC9F,8OAAC;oDAAG,WAAU;8DAAO,cAAA,8OAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAmD;;;;;;;;;;;8DAC9F,8OAAC;oDAAG,WAAU;8DAAO,cAAA,8OAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAmD;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAIpG,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA+B;;;;;;sDAC7C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;;;;;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;;8DAElC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;;;;;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;;8DAElC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;;;;;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;;8DAElC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;;;;;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAO5C,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOhD", "debugId": null}}, {"offset": {"line": 896, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kinco-day-care-kindergarten-react-template-30-06-2025/Kinco/React%20Template/kinco-modern/src/components/PageBanner.js"], "sourcesContent": ["'use client';\n\nimport Link from \"next/link\";\n\nexport default function PageBanner({ title, breadcrumb = [] }) {\n  return (\n    <section className=\"tf-page-banner bg-primary text-white py-5\">\n      <div className=\"container\">\n        <div className=\"row\">\n          <div className=\"col-12 text-center\">\n            <h1 className=\"page-title h1 fw-bold mb-3\">{title}</h1>\n            {breadcrumb.length > 0 && (\n              <nav aria-label=\"breadcrumb\">\n                <ol className=\"breadcrumb justify-content-center mb-0\">\n                  <li className=\"breadcrumb-item\">\n                    <Link href=\"/\" className=\"text-white-50 text-decoration-none\">Home</Link>\n                  </li>\n                  {breadcrumb.map((item, index) => (\n                    <li \n                      key={index} \n                      className={`breadcrumb-item ${index === breadcrumb.length - 1 ? 'active text-white' : ''}`}\n                      {...(index === breadcrumb.length - 1 ? { 'aria-current': 'page' } : {})}\n                    >\n                      {index === breadcrumb.length - 1 ? (\n                        item.name\n                      ) : (\n                        <Link href={item.href} className=\"text-white-50 text-decoration-none\">\n                          {item.name}\n                        </Link>\n                      )}\n                    </li>\n                  ))}\n                </ol>\n              </nav>\n            )}\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS,WAAW,EAAE,KAAK,EAAE,aAAa,EAAE,EAAE;IAC3D,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA8B;;;;;;wBAC3C,WAAW,MAAM,GAAG,mBACnB,8OAAC;4BAAI,cAAW;sCACd,cAAA,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAG,WAAU;kDACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;sDAAqC;;;;;;;;;;;oCAE/D,WAAW,GAAG,CAAC,CAAC,MAAM,sBACrB,8OAAC;4CAEC,WAAW,CAAC,gBAAgB,EAAE,UAAU,WAAW,MAAM,GAAG,IAAI,sBAAsB,IAAI;4CACzF,GAAI,UAAU,WAAW,MAAM,GAAG,IAAI;gDAAE,gBAAgB;4CAAO,IAAI,CAAC,CAAC;sDAErE,UAAU,WAAW,MAAM,GAAG,IAC7B,KAAK,IAAI,iBAET,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAM,KAAK,IAAI;gDAAE,WAAU;0DAC9B,KAAK,IAAI;;;;;;2CART;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqB3B", "debugId": null}}, {"offset": {"line": 1001, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kinco-day-care-kindergarten-react-template-30-06-2025/Kinco/React%20Template/kinco-modern/src/app/about/page.js"], "sourcesContent": ["'use client';\n\nimport Link from \"next/link\";\nimport Header from \"../../components/Header\";\nimport Footer from \"../../components/Footer\";\nimport PageBanner from \"../../components/PageBanner\";\n\nexport default function About() {\n  return (\n    <div className=\"min-h-screen\">\n      {/* Header */}\n      <Header />\n\n      {/* Page Banner */}\n      <PageBanner\n        title=\"About Kinco School\"\n        breadcrumb={[{ name: 'About', href: '/about' }]}\n      />\n\n      {/* About Content */}\n      <section className=\"tf-section tf-about-us-1\">\n        <div className=\"container\">\n          <div className=\"row\">\n            <div className=\"col-12\">\n              <div className=\"about-us-1\">\n                <div className=\"row align-items-center mb-5\">\n                  <div className=\"col-lg-6\">\n                    <div className=\"box-content\">\n                      <div className=\"box-sub-tag d-flex align-items-center mb-3\">\n                        <div className=\"sub-tag-icon me-3\">\n                          <i className=\"flaticon-kindergarten text-primary fs-2\"></i>\n                        </div>\n                        <div className=\"sub-tag-title\">\n                          <p className=\"text-primary fw-semibold mb-0\">About Us</p>\n                        </div>\n                      </div>\n                      <div className=\"title h2 fw-bold mb-4\">\n                        We Provide The Best Education For Your Children\n                      </div>\n                      <div className=\"des text-muted mb-4\">\n                        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut elit tellus,\n                        luctus nec ullamcorper mattis, pulvinar dapibus leo. Sed do eiusmod tempor\n                        incididunt ut labore et dolore magna aliqua.\n                      </div>\n                      <div className=\"des text-muted mb-4\">\n                        Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut\n                        aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in\n                        voluptate velit esse cillum dolore eu fugiat nulla pariatur.\n                      </div>\n                      <div className=\"row g-3 mb-4\">\n                        <div className=\"col-6\">\n                          <div className=\"counter-box text-center p-3 bg-light rounded\">\n                            <div className=\"h3 fw-bold text-primary mb-1\">15+</div>\n                            <div className=\"text-muted small\">Years Experience</div>\n                          </div>\n                        </div>\n                        <div className=\"col-6\">\n                          <div className=\"counter-box text-center p-3 bg-light rounded\">\n                            <div className=\"h3 fw-bold text-primary mb-1\">500+</div>\n                            <div className=\"text-muted small\">Happy Students</div>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"col-lg-6\">\n                    <div className=\"box-feature\">\n                      <img\n                        src=\"/assets/images/common/sc-about1.jpg\"\n                        alt=\"About Kinco\"\n                        className=\"img-fluid rounded shadow\"\n                      />\n                    </div>\n                  </div>\n                </div>\n\n                {/* Mission & Vision */}\n                <div className=\"row g-4\">\n                  <div className=\"col-lg-6\">\n                    <div className=\"mission-box bg-white p-4 rounded shadow-sm h-100\">\n                      <div className=\"mission-icon mb-3\">\n                        <i className=\"flaticon-target text-primary fs-1\"></i>\n                      </div>\n                      <h3 className=\"h4 fw-bold mb-3\">Our Mission</h3>\n                      <p className=\"text-muted mb-0\">\n                        To provide a nurturing and stimulating environment where children can develop their full potential through quality education, creative expression, and character building.\n                      </p>\n                    </div>\n                  </div>\n                  <div className=\"col-lg-6\">\n                    <div className=\"vision-box bg-white p-4 rounded shadow-sm h-100\">\n                      <div className=\"vision-icon mb-3\">\n                        <i className=\"flaticon-eye text-primary fs-1\"></i>\n                      </div>\n                      <h3 className=\"h4 fw-bold mb-3\">Our Vision</h3>\n                      <p className=\"text-muted mb-0\">\n                        To be the leading educational institution that shapes confident, creative, and compassionate individuals who will contribute positively to society.\n                      </p>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,2HAAA,CAAA,UAAM;;;;;0BAGP,8OAAC,+HAAA,CAAA,UAAU;gBACT,OAAM;gBACN,YAAY;oBAAC;wBAAE,MAAM;wBAAS,MAAM;oBAAS;iBAAE;;;;;;0BAIjD,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAE,WAAU;;;;;;;;;;;8EAEf,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAE,WAAU;kFAAgC;;;;;;;;;;;;;;;;;sEAGjD,8OAAC;4DAAI,WAAU;sEAAwB;;;;;;sEAGvC,8OAAC;4DAAI,WAAU;sEAAsB;;;;;;sEAKrC,8OAAC;4DAAI,WAAU;sEAAsB;;;;;;sEAKrC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;0FAA+B;;;;;;0FAC9C,8OAAC;gFAAI,WAAU;0FAAmB;;;;;;;;;;;;;;;;;8EAGtC,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;0FAA+B;;;;;;0FAC9C,8OAAC;gFAAI,WAAU;0FAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAM5C,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDACC,KAAI;wDACJ,KAAI;wDACJ,WAAU;;;;;;;;;;;;;;;;;;;;;;kDAOlB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAE,WAAU;;;;;;;;;;;sEAEf,8OAAC;4DAAG,WAAU;sEAAkB;;;;;;sEAChC,8OAAC;4DAAE,WAAU;sEAAkB;;;;;;;;;;;;;;;;;0DAKnC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAE,WAAU;;;;;;;;;;;sEAEf,8OAAC;4DAAG,WAAU;sEAAkB;;;;;;sEAChC,8OAAC;4DAAE,WAAU;sEAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAa/C,8OAAC,2HAAA,CAAA,UAAM;;;;;;;;;;;AAGb", "debugId": null}}]}