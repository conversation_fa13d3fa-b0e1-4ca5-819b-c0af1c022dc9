{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kinco-day-care-kindergarten-react-template-30-06-2025/Kinco/React%20Template/kinco-modern/src/app/events/page.js"], "sourcesContent": ["'use client';\n\nimport Link from \"next/link\";\n\nexport default function Events() {\n  const upcomingEvents = [\n    {\n      id: 1,\n      title: \"Annual Science Fair\",\n      date: \"January 15, 2025\",\n      time: \"9:00 AM - 3:00 PM\",\n      location: \"Main Auditorium\",\n      description: \"Students will showcase their science projects and experiments. Parents and families are welcome to attend.\",\n      image: \"/assets/images/common/sc-event1.jpg\",\n      category: \"Academic\"\n    },\n    {\n      id: 2,\n      title: \"Winter Art Exhibition\",\n      date: \"January 22, 2025\", \n      time: \"10:00 AM - 2:00 PM\",\n      location: \"Art Gallery\",\n      description: \"Display of student artwork created during the winter semester. Refreshments will be provided.\",\n      image: \"/assets/images/common/sc-event2.jpg\",\n      category: \"Arts\"\n    },\n    {\n      id: 3,\n      title: \"Parent-Teacher Conference\",\n      date: \"February 5, 2025\",\n      time: \"1:00 PM - 6:00 PM\", \n      location: \"Individual Classrooms\",\n      description: \"Meet with your child's teacher to discuss progress and development. Please schedule your appointment.\",\n      image: \"/assets/images/common/sc-event3.jpg\",\n      category: \"Academic\"\n    }\n  ];\n\n  const pastEvents = [\n    {\n      id: 4,\n      title: \"Holiday Concert\",\n      date: \"December 20, 2024\",\n      time: \"7:00 PM\",\n      location: \"Main Auditorium\", \n      description: \"Students performed holiday songs and showcased their musical talents.\",\n      image: \"/assets/images/common/sc-program1.jpg\",\n      category: \"Music\"\n    },\n    {\n      id: 5,\n      title: \"Thanksgiving Feast\",\n      date: \"November 25, 2024\",\n      time: \"11:30 AM - 1:00 PM\",\n      location: \"Cafeteria\",\n      description: \"Students and families came together to celebrate Thanksgiving with a traditional feast.\",\n      image: \"/assets/images/common/sc-program2.jpg\", \n      category: \"Community\"\n    },\n    {\n      id: 6,\n      title: \"Fall Sports Day\",\n      date: \"October 15, 2024\",\n      time: \"9:00 AM - 3:00 PM\",\n      location: \"Outdoor Field\",\n      description: \"Students participated in various sports activities and team games.\",\n      image: \"/assets/images/common/sc-program3.jpg\",\n      category: \"Sports\"\n    }\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm\">\n        <div className=\"container mx-auto px-4 py-4\">\n          <div className=\"flex justify-between items-center\">\n            <Link href=\"/\" className=\"flex items-center\">\n              <img src=\"/assets/images/logo/logodark-2.png\" alt=\"Kinco School\" className=\"h-12\" />\n            </Link>\n            <nav className=\"hidden md:flex space-x-8\">\n              <Link href=\"/\" className=\"text-gray-600 hover:text-blue-600 transition-colors\">Home</Link>\n              <Link href=\"/about\" className=\"text-gray-600 hover:text-blue-600 transition-colors\">About</Link>\n              <Link href=\"/classes\" className=\"text-gray-600 hover:text-blue-600 transition-colors\">Classes</Link>\n              <Link href=\"/program\" className=\"text-gray-600 hover:text-blue-600 transition-colors\">Programs</Link>\n              <Link href=\"/teacher\" className=\"text-gray-600 hover:text-blue-600 transition-colors\">Teachers</Link>\n              <Link href=\"/events\" className=\"text-blue-600 font-semibold\">Events</Link>\n              <Link href=\"/contact\" className=\"text-gray-600 hover:text-blue-600 transition-colors\">Contact</Link>\n            </nav>\n          </div>\n        </div>\n      </header>\n\n      {/* Page Banner */}\n      <section className=\"bg-gradient-to-r from-blue-500 to-purple-600 text-white py-16\">\n        <div className=\"container mx-auto px-4 text-center\">\n          <h1 className=\"text-4xl font-bold mb-4\">School Events</h1>\n          <nav className=\"text-blue-200\">\n            <Link href=\"/\" className=\"hover:text-white\">Home</Link>\n            <span className=\"mx-2\">/</span>\n            <span>Events</span>\n          </nav>\n        </div>\n      </section>\n\n      {/* Upcoming Events */}\n      <section className=\"py-16\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-4xl font-bold mb-4 text-gray-800\">Upcoming Events</h2>\n            <p className=\"text-gray-600 max-w-2xl mx-auto\">\n              Join us for these exciting upcoming events and activities\n            </p>\n          </div>\n\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16\">\n            {upcomingEvents.map((event) => (\n              <div key={event.id} className=\"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow\">\n                <img \n                  src={event.image} \n                  alt={event.title}\n                  className=\"w-full h-48 object-cover\"\n                />\n                <div className=\"p-6\">\n                  <div className=\"flex items-center justify-between mb-3\">\n                    <span className=\"bg-green-100 text-green-800 text-xs font-semibold px-2 py-1 rounded\">\n                      {event.category}\n                    </span>\n                    <span className=\"bg-blue-100 text-blue-800 text-xs font-semibold px-2 py-1 rounded\">\n                      Upcoming\n                    </span>\n                  </div>\n                  <h3 className=\"text-xl font-bold mb-3 text-gray-800\">{event.title}</h3>\n                  <div className=\"space-y-2 mb-4 text-sm text-gray-600\">\n                    <div className=\"flex items-center\">\n                      <span className=\"mr-2\">📅</span>\n                      {event.date}\n                    </div>\n                    <div className=\"flex items-center\">\n                      <span className=\"mr-2\">🕒</span>\n                      {event.time}\n                    </div>\n                    <div className=\"flex items-center\">\n                      <span className=\"mr-2\">📍</span>\n                      {event.location}\n                    </div>\n                  </div>\n                  <p className=\"text-gray-600 mb-4\">{event.description}</p>\n                  <button className=\"w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 transition-colors\">\n                    Register Now\n                  </button>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Past Events */}\n      <section className=\"py-16 bg-gray-100\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-4xl font-bold mb-4 text-gray-800\">Past Events</h2>\n            <p className=\"text-gray-600 max-w-2xl mx-auto\">\n              Take a look at some of our recent successful events and activities\n            </p>\n          </div>\n\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {pastEvents.map((event) => (\n              <div key={event.id} className=\"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow\">\n                <img \n                  src={event.image} \n                  alt={event.title}\n                  className=\"w-full h-48 object-cover\"\n                />\n                <div className=\"p-6\">\n                  <div className=\"flex items-center justify-between mb-3\">\n                    <span className=\"bg-purple-100 text-purple-800 text-xs font-semibold px-2 py-1 rounded\">\n                      {event.category}\n                    </span>\n                    <span className=\"bg-gray-100 text-gray-800 text-xs font-semibold px-2 py-1 rounded\">\n                      Completed\n                    </span>\n                  </div>\n                  <h3 className=\"text-xl font-bold mb-3 text-gray-800\">{event.title}</h3>\n                  <div className=\"space-y-2 mb-4 text-sm text-gray-600\">\n                    <div className=\"flex items-center\">\n                      <span className=\"mr-2\">📅</span>\n                      {event.date}\n                    </div>\n                    <div className=\"flex items-center\">\n                      <span className=\"mr-2\">🕒</span>\n                      {event.time}\n                    </div>\n                    <div className=\"flex items-center\">\n                      <span className=\"mr-2\">📍</span>\n                      {event.location}\n                    </div>\n                  </div>\n                  <p className=\"text-gray-600\">{event.description}</p>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Event Calendar CTA */}\n      <section className=\"py-16 bg-blue-600 text-white\">\n        <div className=\"container mx-auto px-4 text-center\">\n          <h2 className=\"text-3xl font-bold mb-4\">Stay Updated with Our Events</h2>\n          <p className=\"text-xl mb-8 text-blue-100\">\n            Subscribe to our calendar to never miss an important school event\n          </p>\n          <div className=\"space-x-4\">\n            <Link \n              href=\"/contact\" \n              className=\"bg-yellow-400 text-blue-900 px-8 py-3 rounded-lg font-semibold hover:bg-yellow-300 transition-colors\"\n            >\n              Subscribe to Calendar\n            </Link>\n            <Link \n              href=\"/contact\" \n              className=\"border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors\"\n            >\n              Contact Us\n            </Link>\n          </div>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"bg-gray-800 text-white py-8\">\n        <div className=\"container mx-auto px-4 text-center\">\n          <div className=\"mb-4\">\n            <Link href=\"/\" className=\"text-2xl font-bold text-blue-400\">\n              Kinco School\n            </Link>\n          </div>\n          <p className=\"text-gray-400 mb-4\">\n            Providing quality education and care for your children\n          </p>\n          <div className=\"flex justify-center space-x-6\">\n            <Link href=\"/about\" className=\"text-gray-400 hover:text-white\">About</Link>\n            <Link href=\"/classes\" className=\"text-gray-400 hover:text-white\">Classes</Link>\n            <Link href=\"/events\" className=\"text-white\">Events</Link>\n            <Link href=\"/contact\" className=\"text-gray-400 hover:text-white\">Contact</Link>\n          </div>\n          <div className=\"mt-6 pt-6 border-t border-gray-700\">\n            <p className=\"text-gray-400\">© 2024 Kinco School. All rights reserved.</p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,MAAM,iBAAiB;QACrB;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,MAAM;YACN,UAAU;YACV,aAAa;YACb,OAAO;YACP,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,MAAM;YACN,UAAU;YACV,aAAa;YACb,OAAO;YACP,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,MAAM;YACN,UAAU;YACV,aAAa;YACb,OAAO;YACP,UAAU;QACZ;KACD;IAED,MAAM,aAAa;QACjB;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,MAAM;YACN,UAAU;YACV,aAAa;YACb,OAAO;YACP,UAAU;QACZ;QACA;YACE,IAAI;YAC<PERSON>,OAAO;YAC<PERSON>,MAAM;YACN,MAAM;YACN,UAAU;YACV,aAAa;YACb,OAAO;YACP,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,MAAM;YACN,UAAU;YACV,aAAa;YACb,OAAO;YACP,UAAU;QACZ;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CACvB,cAAA,8OAAC;oCAAI,KAAI;oCAAqC,KAAI;oCAAe,WAAU;;;;;;;;;;;0CAE7E,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDAAsD;;;;;;kDAC/E,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAAsD;;;;;;kDACpF,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAsD;;;;;;kDACtF,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAsD;;;;;;kDACtF,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAsD;;;;;;kDACtF,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAU,WAAU;kDAA8B;;;;;;kDAC7D,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAsD;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO9F,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA0B;;;;;;sCACxC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAmB;;;;;;8CAC5C,8OAAC;oCAAK,WAAU;8CAAO;;;;;;8CACvB,8OAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;0BAMZ,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAE,WAAU;8CAAkC;;;;;;;;;;;;sCAKjD,8OAAC;4BAAI,WAAU;sCACZ,eAAe,GAAG,CAAC,CAAC,sBACnB,8OAAC;oCAAmB,WAAU;;sDAC5B,8OAAC;4CACC,KAAK,MAAM,KAAK;4CAChB,KAAK,MAAM,KAAK;4CAChB,WAAU;;;;;;sDAEZ,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEACb,MAAM,QAAQ;;;;;;sEAEjB,8OAAC;4DAAK,WAAU;sEAAoE;;;;;;;;;;;;8DAItF,8OAAC;oDAAG,WAAU;8DAAwC,MAAM,KAAK;;;;;;8DACjE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAO;;;;;;gEACtB,MAAM,IAAI;;;;;;;sEAEb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAO;;;;;;gEACtB,MAAM,IAAI;;;;;;;sEAEb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAO;;;;;;gEACtB,MAAM,QAAQ;;;;;;;;;;;;;8DAGnB,8OAAC;oDAAE,WAAU;8DAAsB,MAAM,WAAW;;;;;;8DACpD,8OAAC;oDAAO,WAAU;8DAAsF;;;;;;;;;;;;;mCA/BlG,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;0BA0C1B,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAE,WAAU;8CAAkC;;;;;;;;;;;;sCAKjD,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,sBACf,8OAAC;oCAAmB,WAAU;;sDAC5B,8OAAC;4CACC,KAAK,MAAM,KAAK;4CAChB,KAAK,MAAM,KAAK;4CAChB,WAAU;;;;;;sDAEZ,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEACb,MAAM,QAAQ;;;;;;sEAEjB,8OAAC;4DAAK,WAAU;sEAAoE;;;;;;;;;;;;8DAItF,8OAAC;oDAAG,WAAU;8DAAwC,MAAM,KAAK;;;;;;8DACjE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAO;;;;;;gEACtB,MAAM,IAAI;;;;;;;sEAEb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAO;;;;;;gEACtB,MAAM,IAAI;;;;;;;sEAEb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAO;;;;;;gEACtB,MAAM,QAAQ;;;;;;;;;;;;;8DAGnB,8OAAC;oDAAE,WAAU;8DAAiB,MAAM,WAAW;;;;;;;;;;;;;mCA9BzC,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;0BAuC1B,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA0B;;;;;;sCACxC,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAG1C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;0BAQP,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CAAmC;;;;;;;;;;;sCAI9D,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAGlC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAAiC;;;;;;8CAC/D,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAiC;;;;;;8CACjE,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAU,WAAU;8CAAa;;;;;;8CAC5C,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAiC;;;;;;;;;;;;sCAEnE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMzC", "debugId": null}}]}