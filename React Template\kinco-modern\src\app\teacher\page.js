'use client';

import Link from "next/link";
import Header from "../../components/Header";
import Footer from "../../components/Footer";

export default function Teachers() {
  const teachers = [
    {
      id: 1,
      name: "<PERSON>",
      position: "Lead Teacher",
      experience: "8 years",
      specialization: "Early Childhood Development",
      image: "/assets/images/common/sc-employee-1.jpg",
      bio: "<PERSON> has a passion for nurturing young minds and creating engaging learning environments."
    },
    {
      id: 2,
      name: "<PERSON>",
      position: "Art Teacher",
      experience: "5 years",
      specialization: "Creative Arts & Crafts",
      image: "/assets/images/common/sc-employee-2.jpg",
      bio: "<PERSON> brings creativity and imagination to every art class, inspiring children to express themselves."
    },
    {
      id: 3,
      name: "<PERSON>",
      position: "Music Teacher",
      experience: "6 years",
      specialization: "Music & Movement",
      image: "/assets/images/common/sc-employee-3.jpg",
      bio: "Michael helps children discover the joy of music through interactive and fun learning experiences."
    },
    {
      id: 4,
      name: "<PERSON>",
      position: "Language Teacher",
      experience: "7 years",
      specialization: "English & Communication",
      image: "/assets/images/common/sc-employee-4.jpg",
      bio: "<PERSON> focuses on building strong communication skills and language development in young learners."
    },
    {
      id: 5,
      name: "<PERSON> <PERSON>",
      position: "Physical Education",
      experience: "4 years",
      specialization: "Sports & Physical Development",
      image: "/assets/images/common/sc-employee-5.jpg",
      bio: "David promotes healthy living and physical development through fun sports and activities."
    },
    {
      id: 6,
      name: "Jennifer Lee",
      position: "Science Teacher",
      experience: "6 years",
      specialization: "STEM Education",
      image: "/assets/images/common/sc-employee-6.jpg",
      bio: "Jennifer makes science fun and accessible, encouraging curiosity and exploration in young minds."
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Header */}
      <Header />

      {/* Page Banner */}
      <section className="bg-gradient-to-r from-blue-500 to-purple-600 text-white page-banner">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl font-bold mb-4">Our Teachers</h1>
          <nav className="text-blue-200">
            <Link href="/" className="hover:text-white">Home</Link>
            <span className="mx-2">/</span>
            <span>Teachers</span>
          </nav>
        </div>
      </section>

      {/* Teachers Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold mb-4 text-gray-800">Meet Our Dedicated Team</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Our experienced and passionate teachers are committed to providing the best educational experience for your children
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {teachers.map((teacher) => (
              <div key={teacher.id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                <img 
                  src={teacher.image} 
                  alt={teacher.name}
                  className="w-full h-64 object-cover"
                />
                <div className="p-6">
                  <h3 className="text-xl font-bold mb-1 text-gray-800">{teacher.name}</h3>
                  <p className="text-blue-600 font-semibold mb-2">{teacher.position}</p>
                  <div className="text-sm text-gray-500 mb-3">
                    <p>Experience: {teacher.experience}</p>
                    <p>Specialization: {teacher.specialization}</p>
                  </div>
                  <p className="text-gray-600 text-sm mb-4">{teacher.bio}</p>
                  <Link 
                    href={`/teacher/${teacher.id}`}
                    className="text-blue-600 hover:text-blue-800 font-semibold text-sm"
                  >
                    View Profile →
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Join Our Team Section */}
      <section className="bg-blue-600 text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">Join Our Teaching Team</h2>
          <p className="text-xl mb-8 text-blue-100">
            Are you passionate about early childhood education? We're always looking for dedicated teachers to join our team.
          </p>
          <Link 
            href="/contact" 
            className="bg-yellow-400 text-blue-900 px-8 py-3 rounded-lg font-semibold hover:bg-yellow-300 transition-colors"
          >
            Apply Now
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-800 text-white py-8">
        <div className="container mx-auto px-4 text-center">
          <div className="mb-4">
            <Link href="/" className="text-2xl font-bold text-blue-400">
              Kinco School
            </Link>
          </div>
          <p className="text-gray-400 mb-4">
            Providing quality education and care for your children
          </p>
          <div className="flex justify-center space-x-6">
            <Link href="/about" className="text-gray-400 hover:text-white">About</Link>
            <Link href="/classes" className="text-gray-400 hover:text-white">Classes</Link>
            <Link href="/teacher" className="text-white">Teachers</Link>
            <Link href="/contact" className="text-gray-400 hover:text-white">Contact</Link>
          </div>
          <div className="mt-6 pt-6 border-t border-gray-700">
            <p className="text-gray-400">© 2024 Kinco School. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
