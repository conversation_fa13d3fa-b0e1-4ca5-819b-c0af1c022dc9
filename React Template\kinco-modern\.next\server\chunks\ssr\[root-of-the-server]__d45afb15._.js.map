{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kinco-day-care-kindergarten-react-template-30-06-2025/Kinco/React%20Template/kinco-modern/src/components/Header.js"], "sourcesContent": ["'use client';\n\nimport Link from \"next/link\";\nimport { usePathname } from \"next/navigation\";\n\nexport default function Header() {\n  const pathname = usePathname();\n\n  const isActive = (path) => {\n    return pathname === path;\n  };\n\n  return (\n    <header className=\"header-style-1 bg-white shadow-lg fixed-top\">\n      <div className=\"container-fluid\">\n        <div className=\"row\">\n          <div className=\"col-12\">\n            <div className=\"header-inner d-flex justify-content-between align-items-center py-3\">\n              <div className=\"header-logo\">\n                <Link href=\"/\" className=\"d-flex align-items-center text-decoration-none\">\n                  <div className=\"logo-placeholder d-flex align-items-center\">\n                    <div className=\"logo-icon bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2\"\n                         style={{width: '40px', height: '40px'}}>\n                      <i className=\"fa fa-graduation-cap\"></i>\n                    </div>\n                    <div>\n                      <h4 className=\"mb-0 text-primary fw-bold\">Smart</h4>\n                      <small className=\"text-muted\">Kinco</small>\n                    </div>\n                  </div>\n                </Link>\n              </div>\n              <nav className=\"main-menu d-none d-lg-block\">\n                <ul className=\"d-flex list-unstyled mb-0 align-items-center\">\n                  <li className=\"menu-item\">\n                    <Link href=\"/\" className={`menu-link text-decoration-none px-3 py-2 rounded-pill transition-all ${isActive('/') ? 'fw-semibold text-white bg-primary' : 'text-dark hover-primary'}`}>\n                      Home\n                    </Link>\n                  </li>\n                  <li className=\"menu-item\">\n                    <Link href=\"/about\" className={`menu-link text-decoration-none px-3 py-2 rounded-pill transition-all ${isActive('/about') ? 'fw-semibold text-white bg-primary' : 'text-dark hover-primary'}`}>\n                      About\n                    </Link>\n                  </li>\n                  <li className=\"menu-item\">\n                    <Link href=\"/classes\" className={`menu-link text-decoration-none px-3 py-2 rounded-pill transition-all ${isActive('/classes') ? 'fw-semibold text-white bg-primary' : 'text-dark hover-primary'}`}>\n                      Classes\n                    </Link>\n                  </li>\n                  <li className=\"menu-item\">\n                    <Link href=\"/program\" className={`menu-link text-decoration-none px-3 py-2 rounded-pill transition-all ${isActive('/program') ? 'fw-semibold text-white bg-primary' : 'text-dark hover-primary'}`}>\n                      Programs\n                    </Link>\n                  </li>\n                  <li className=\"menu-item\">\n                    <Link href=\"/teacher\" className={`menu-link text-decoration-none px-3 py-2 rounded-pill transition-all ${isActive('/teacher') ? 'fw-semibold text-white bg-primary' : 'text-dark hover-primary'}`}>\n                      Teachers\n                    </Link>\n                  </li>\n                  <li className=\"menu-item\">\n                    <Link href=\"/gallery\" className={`menu-link text-decoration-none px-3 py-2 rounded-pill transition-all ${isActive('/gallery') ? 'fw-semibold text-white bg-primary' : 'text-dark hover-primary'}`}>\n                      Gallery\n                    </Link>\n                  </li>\n                  <li className=\"menu-item\">\n                    <Link href=\"/blog\" className={`menu-link text-decoration-none px-3 py-2 rounded-pill transition-all ${isActive('/blog') ? 'fw-semibold text-white bg-primary' : 'text-dark hover-primary'}`}>\n                      Blog\n                    </Link>\n                  </li>\n                  <li className=\"menu-item\">\n                    <Link href=\"/events\" className={`menu-link text-decoration-none px-3 py-2 rounded-pill transition-all ${isActive('/events') ? 'fw-semibold text-white bg-primary' : 'text-dark hover-primary'}`}>\n                      Events\n                    </Link>\n                  </li>\n                  <li className=\"menu-item\">\n                    <Link href=\"/contact\" className={`menu-link text-decoration-none px-3 py-2 rounded-pill transition-all ${isActive('/contact') ? 'fw-semibold text-white bg-primary' : 'text-dark hover-primary'}`}>\n                      Contact\n                    </Link>\n                  </li>\n                </ul>\n              </nav>\n              <div className=\"header-mobile d-lg-none\">\n                <button className=\"mobile-menu-toggle btn btn-primary\">\n                  <i className=\"fa fa-bars\"></i>\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,WAAW,CAAC;QAChB,OAAO,aAAa;IACtB;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CACvB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;gDACV,OAAO;oDAAC,OAAO;oDAAQ,QAAQ;gDAAM;0DACxC,cAAA,8OAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA4B;;;;;;kEAC1C,8OAAC;wDAAM,WAAU;kEAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAKtC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAI,WAAW,CAAC,qEAAqE,EAAE,SAAS,OAAO,sCAAsC,2BAA2B;0DAAE;;;;;;;;;;;sDAIvL,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAW,CAAC,qEAAqE,EAAE,SAAS,YAAY,sCAAsC,2BAA2B;0DAAE;;;;;;;;;;;sDAIjM,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAW,CAAC,qEAAqE,EAAE,SAAS,cAAc,sCAAsC,2BAA2B;0DAAE;;;;;;;;;;;sDAIrM,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAW,CAAC,qEAAqE,EAAE,SAAS,cAAc,sCAAsC,2BAA2B;0DAAE;;;;;;;;;;;sDAIrM,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAW,CAAC,qEAAqE,EAAE,SAAS,cAAc,sCAAsC,2BAA2B;0DAAE;;;;;;;;;;;sDAIrM,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAW,CAAC,qEAAqE,EAAE,SAAS,cAAc,sCAAsC,2BAA2B;0DAAE;;;;;;;;;;;sDAIrM,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAW,CAAC,qEAAqE,EAAE,SAAS,WAAW,sCAAsC,2BAA2B;0DAAE;;;;;;;;;;;sDAI/L,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAU,WAAW,CAAC,qEAAqE,EAAE,SAAS,aAAa,sCAAsC,2BAA2B;0DAAE;;;;;;;;;;;sDAInM,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAW,CAAC,qEAAqE,EAAE,SAAS,cAAc,sCAAsC,2BAA2B;0DAAE;;;;;;;;;;;;;;;;;;;;;;0CAMzM,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAO,WAAU;8CAChB,cAAA,8OAAC;wCAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS/B", "debugId": null}}, {"offset": {"line": 345, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kinco-day-care-kindergarten-react-template-30-06-2025/Kinco/React%20Template/kinco-modern/src/components/Footer.js"], "sourcesContent": ["'use client';\n\nimport Link from \"next/link\";\n\nexport default function Footer() {\n  return (\n    <footer className=\"footer-style-2 bg-dark text-white\">\n      <div className=\"container\">\n        <div className=\"footer-content py-5\">\n          <div className=\"row g-4\">\n            <div className=\"col-lg-3 col-md-6\">\n              <div className=\"footer-widget\">\n                <div className=\"footer-logo mb-4\">\n                  <Link href=\"/\">\n                    <img src=\"/assets/images/logo/logofootert.png\" alt=\"Kinco School\" className=\"img-fluid\" style={{maxHeight: '50px'}} />\n                  </Link>\n                </div>\n                <p className=\"text-white-50 mb-4\">\n                  Providing quality education and care for your children in a safe, nurturing environment.\n                </p>\n                <div className=\"footer-social d-flex gap-3\">\n                  <a href=\"#\" className=\"social-link text-white-50 hover-primary\">\n                    <i className=\"fa fa-facebook\"></i>\n                  </a>\n                  <a href=\"#\" className=\"social-link text-white-50 hover-primary\">\n                    <i className=\"fa fa-twitter\"></i>\n                  </a>\n                  <a href=\"#\" className=\"social-link text-white-50 hover-primary\">\n                    <i className=\"fa fa-instagram\"></i>\n                  </a>\n                  <a href=\"#\" className=\"social-link text-white-50 hover-primary\">\n                    <i className=\"fa fa-linkedin\"></i>\n                  </a>\n                </div>\n              </div>\n            </div>\n            <div className=\"col-lg-3 col-md-6\">\n              <div className=\"footer-widget\">\n                <h3 className=\"footer-title h5 fw-bold mb-4\">Quick Links</h3>\n                <ul className=\"footer-menu list-unstyled\">\n                  <li className=\"mb-2\"><Link href=\"/about\" className=\"text-white-50 text-decoration-none hover-primary\">About Us</Link></li>\n                  <li className=\"mb-2\"><Link href=\"/classes\" className=\"text-white-50 text-decoration-none hover-primary\">Classes</Link></li>\n                  <li className=\"mb-2\"><Link href=\"/program\" className=\"text-white-50 text-decoration-none hover-primary\">Programs</Link></li>\n                  <li className=\"mb-2\"><Link href=\"/teacher\" className=\"text-white-50 text-decoration-none hover-primary\">Teachers</Link></li>\n                  <li className=\"mb-2\"><Link href=\"/gallery\" className=\"text-white-50 text-decoration-none hover-primary\">Gallery</Link></li>\n                </ul>\n              </div>\n            </div>\n            <div className=\"col-lg-3 col-md-6\">\n              <div className=\"footer-widget\">\n                <h3 className=\"footer-title h5 fw-bold mb-4\">Programs</h3>\n                <ul className=\"footer-menu list-unstyled\">\n                  <li className=\"mb-2\"><a href=\"#\" className=\"text-white-50 text-decoration-none hover-primary\">Drawing & Painting</a></li>\n                  <li className=\"mb-2\"><a href=\"#\" className=\"text-white-50 text-decoration-none hover-primary\">Computer Learning</a></li>\n                  <li className=\"mb-2\"><a href=\"#\" className=\"text-white-50 text-decoration-none hover-primary\">Basic English</a></li>\n                  <li className=\"mb-2\"><a href=\"#\" className=\"text-white-50 text-decoration-none hover-primary\">Music & Dance</a></li>\n                  <li className=\"mb-2\"><a href=\"#\" className=\"text-white-50 text-decoration-none hover-primary\">Sports Activities</a></li>\n                </ul>\n              </div>\n            </div>\n            <div className=\"col-lg-3 col-md-6\">\n              <div className=\"footer-widget\">\n                <h3 className=\"footer-title h5 fw-bold mb-4\">Contact Info</h3>\n                <div className=\"footer-contact\">\n                  <div className=\"contact-item d-flex align-items-start mb-3\">\n                    <i className=\"fa fa-map-marker text-primary me-3 mt-1\"></i>\n                    <span className=\"text-white-50\">123 Education Street, Learning City, LC 12345</span>\n                  </div>\n                  <div className=\"contact-item d-flex align-items-center mb-3\">\n                    <i className=\"fa fa-phone text-primary me-3\"></i>\n                    <span className=\"text-white-50\">(*************</span>\n                  </div>\n                  <div className=\"contact-item d-flex align-items-center mb-3\">\n                    <i className=\"fa fa-envelope text-primary me-3\"></i>\n                    <span className=\"text-white-50\"><EMAIL></span>\n                  </div>\n                  <div className=\"contact-item d-flex align-items-center\">\n                    <i className=\"fa fa-clock-o text-primary me-3\"></i>\n                    <span className=\"text-white-50\">Mon-Fri: 7AM-6PM</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n        <div className=\"footer-bottom border-top border-secondary pt-4\">\n          <div className=\"row align-items-center\">\n            <div className=\"col-12 text-center\">\n              <p className=\"text-white-50 mb-0\">© 2024 Kinco School. All rights reserved.</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;0DACT,cAAA,8OAAC;oDAAI,KAAI;oDAAsC,KAAI;oDAAe,WAAU;oDAAY,OAAO;wDAAC,WAAW;oDAAM;;;;;;;;;;;;;;;;sDAGrH,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAGlC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,MAAK;oDAAI,WAAU;8DACpB,cAAA,8OAAC;wDAAE,WAAU;;;;;;;;;;;8DAEf,8OAAC;oDAAE,MAAK;oDAAI,WAAU;8DACpB,cAAA,8OAAC;wDAAE,WAAU;;;;;;;;;;;8DAEf,8OAAC;oDAAE,MAAK;oDAAI,WAAU;8DACpB,cAAA,8OAAC;wDAAE,WAAU;;;;;;;;;;;8DAEf,8OAAC;oDAAE,MAAK;oDAAI,WAAU;8DACpB,cAAA,8OAAC;wDAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAKrB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA+B;;;;;;sDAC7C,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAG,WAAU;8DAAO,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAS,WAAU;kEAAmD;;;;;;;;;;;8DACtG,8OAAC;oDAAG,WAAU;8DAAO,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;kEAAmD;;;;;;;;;;;8DACxG,8OAAC;oDAAG,WAAU;8DAAO,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;kEAAmD;;;;;;;;;;;8DACxG,8OAAC;oDAAG,WAAU;8DAAO,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;kEAAmD;;;;;;;;;;;8DACxG,8OAAC;oDAAG,WAAU;8DAAO,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;kEAAmD;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAI9G,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA+B;;;;;;sDAC7C,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAG,WAAU;8DAAO,cAAA,8OAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAmD;;;;;;;;;;;8DAC9F,8OAAC;oDAAG,WAAU;8DAAO,cAAA,8OAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAmD;;;;;;;;;;;8DAC9F,8OAAC;oDAAG,WAAU;8DAAO,cAAA,8OAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAmD;;;;;;;;;;;8DAC9F,8OAAC;oDAAG,WAAU;8DAAO,cAAA,8OAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAmD;;;;;;;;;;;8DAC9F,8OAAC;oDAAG,WAAU;8DAAO,cAAA,8OAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAmD;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAIpG,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA+B;;;;;;sDAC7C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;;;;;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;;8DAElC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;;;;;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;;8DAElC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;;;;;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;;8DAElC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;;;;;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAO5C,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOhD", "debugId": null}}, {"offset": {"line": 896, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kinco-day-care-kindergarten-react-template-30-06-2025/Kinco/React%20Template/kinco-modern/src/app/teacher/page.js"], "sourcesContent": ["'use client';\n\nimport Link from \"next/link\";\nimport Header from \"../../components/Header\";\nimport Footer from \"../../components/Footer\";\n\nexport default function Teachers() {\n  const teachers = [\n    {\n      id: 1,\n      name: \"<PERSON>\",\n      position: \"Lead Teacher\",\n      experience: \"8 years\",\n      specialization: \"Early Childhood Development\",\n      image: \"/assets/images/common/sc-employee-1.jpg\",\n      bio: \"<PERSON> has a passion for nurturing young minds and creating engaging learning environments.\"\n    },\n    {\n      id: 2,\n      name: \"<PERSON>\",\n      position: \"Art Teacher\",\n      experience: \"5 years\",\n      specialization: \"Creative Arts & Crafts\",\n      image: \"/assets/images/common/sc-employee-2.jpg\",\n      bio: \"<PERSON> brings creativity and imagination to every art class, inspiring children to express themselves.\"\n    },\n    {\n      id: 3,\n      name: \"<PERSON>\",\n      position: \"Music Teacher\",\n      experience: \"6 years\",\n      specialization: \"Music & Movement\",\n      image: \"/assets/images/common/sc-employee-3.jpg\",\n      bio: \"Michael helps children discover the joy of music through interactive and fun learning experiences.\"\n    },\n    {\n      id: 4,\n      name: \"<PERSON>\",\n      position: \"Language Teacher\",\n      experience: \"7 years\",\n      specialization: \"English & Communication\",\n      image: \"/assets/images/common/sc-employee-4.jpg\",\n      bio: \"<PERSON> focuses on building strong communication skills and language development in young learners.\"\n    },\n    {\n      id: 5,\n      name: \"<PERSON> <PERSON>\",\n      position: \"Physical Education\",\n      experience: \"4 years\",\n      specialization: \"Sports & Physical Development\",\n      image: \"/assets/images/common/sc-employee-5.jpg\",\n      bio: \"David promotes healthy living and physical development through fun sports and activities.\"\n    },\n    {\n      id: 6,\n      name: \"Jennifer Lee\",\n      position: \"Science Teacher\",\n      experience: \"6 years\",\n      specialization: \"STEM Education\",\n      image: \"/assets/images/common/sc-employee-6.jpg\",\n      bio: \"Jennifer makes science fun and accessible, encouraging curiosity and exploration in young minds.\"\n    }\n  ];\n\n  return (\n    <div className=\"min-h-screen\">\n      {/* Header */}\n      <Header />\n\n      {/* Page Banner */}\n      <section className=\"bg-gradient-to-r from-blue-500 to-purple-600 text-white page-banner\">\n        <div className=\"container mx-auto px-4 text-center\">\n          <h1 className=\"text-4xl font-bold mb-4\">Our Teachers</h1>\n          <nav className=\"text-blue-200\">\n            <Link href=\"/\" className=\"hover:text-white\">Home</Link>\n            <span className=\"mx-2\">/</span>\n            <span>Teachers</span>\n          </nav>\n        </div>\n      </section>\n\n      {/* Teachers Section */}\n      <section className=\"py-16\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-4xl font-bold mb-4 text-gray-800\">Meet Our Dedicated Team</h2>\n            <p className=\"text-gray-600 max-w-2xl mx-auto\">\n              Our experienced and passionate teachers are committed to providing the best educational experience for your children\n            </p>\n          </div>\n\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {teachers.map((teacher) => (\n              <div key={teacher.id} className=\"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow\">\n                <img \n                  src={teacher.image} \n                  alt={teacher.name}\n                  className=\"w-full h-64 object-cover\"\n                />\n                <div className=\"p-6\">\n                  <h3 className=\"text-xl font-bold mb-1 text-gray-800\">{teacher.name}</h3>\n                  <p className=\"text-blue-600 font-semibold mb-2\">{teacher.position}</p>\n                  <div className=\"text-sm text-gray-500 mb-3\">\n                    <p>Experience: {teacher.experience}</p>\n                    <p>Specialization: {teacher.specialization}</p>\n                  </div>\n                  <p className=\"text-gray-600 text-sm mb-4\">{teacher.bio}</p>\n                  <Link \n                    href={`/teacher/${teacher.id}`}\n                    className=\"text-blue-600 hover:text-blue-800 font-semibold text-sm\"\n                  >\n                    View Profile →\n                  </Link>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Join Our Team Section */}\n      <section className=\"bg-blue-600 text-white py-16\">\n        <div className=\"container mx-auto px-4 text-center\">\n          <h2 className=\"text-3xl font-bold mb-4\">Join Our Teaching Team</h2>\n          <p className=\"text-xl mb-8 text-blue-100\">\n            Are you passionate about early childhood education? We're always looking for dedicated teachers to join our team.\n          </p>\n          <Link \n            href=\"/contact\" \n            className=\"bg-yellow-400 text-blue-900 px-8 py-3 rounded-lg font-semibold hover:bg-yellow-300 transition-colors\"\n          >\n            Apply Now\n          </Link>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"bg-gray-800 text-white py-8\">\n        <div className=\"container mx-auto px-4 text-center\">\n          <div className=\"mb-4\">\n            <Link href=\"/\" className=\"text-2xl font-bold text-blue-400\">\n              Kinco School\n            </Link>\n          </div>\n          <p className=\"text-gray-400 mb-4\">\n            Providing quality education and care for your children\n          </p>\n          <div className=\"flex justify-center space-x-6\">\n            <Link href=\"/about\" className=\"text-gray-400 hover:text-white\">About</Link>\n            <Link href=\"/classes\" className=\"text-gray-400 hover:text-white\">Classes</Link>\n            <Link href=\"/teacher\" className=\"text-white\">Teachers</Link>\n            <Link href=\"/contact\" className=\"text-gray-400 hover:text-white\">Contact</Link>\n          </div>\n          <div className=\"mt-6 pt-6 border-t border-gray-700\">\n            <p className=\"text-gray-400\">© 2024 Kinco School. All rights reserved.</p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,WAAW;QACf;YACE,IAAI;YACJ,MAAM;YACN,UAAU;YACV,YAAY;YACZ,gBAAgB;YAChB,OAAO;YACP,KAAK;QACP;QACA;YACE,IAAI;YACJ,MAAM;YACN,UAAU;YACV,YAAY;YACZ,gBAAgB;YAChB,OAAO;YACP,KAAK;QACP;QACA;YACE,IAAI;YACJ,MAAM;YACN,UAAU;YACV,YAAY;YACZ,gBAAgB;YAChB,OAAO;YACP,KAAK;QACP;QACA;YACE,IAAI;YACJ,MAAM;YACN,UAAU;YACV,YAAY;YACZ,gBAAgB;YAChB,OAAO;YACP,KAAK;QACP;QACA;YACE,IAAI;YAC<PERSON>,MAAM;YACN,UAAU;YACV,YAAY;YACZ,gBAAgB;YAChB,OAAO;YACP,KAAK;QACP;QACA;YACE,IAAI;YACJ,MAAM;YACN,UAAU;YACV,YAAY;YACZ,gBAAgB;YAChB,OAAO;YACP,KAAK;QACP;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,2HAAA,CAAA,UAAM;;;;;0BAGP,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA0B;;;;;;sCACxC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAmB;;;;;;8CAC5C,8OAAC;oCAAK,WAAU;8CAAO;;;;;;8CACvB,8OAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;0BAMZ,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAE,WAAU;8CAAkC;;;;;;;;;;;;sCAKjD,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;oCAAqB,WAAU;;sDAC9B,8OAAC;4CACC,KAAK,QAAQ,KAAK;4CAClB,KAAK,QAAQ,IAAI;4CACjB,WAAU;;;;;;sDAEZ,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAwC,QAAQ,IAAI;;;;;;8DAClE,8OAAC;oDAAE,WAAU;8DAAoC,QAAQ,QAAQ;;;;;;8DACjE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;gEAAE;gEAAa,QAAQ,UAAU;;;;;;;sEAClC,8OAAC;;gEAAE;gEAAiB,QAAQ,cAAc;;;;;;;;;;;;;8DAE5C,8OAAC;oDAAE,WAAU;8DAA8B,QAAQ,GAAG;;;;;;8DACtD,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,CAAC,SAAS,EAAE,QAAQ,EAAE,EAAE;oDAC9B,WAAU;8DACX;;;;;;;;;;;;;mCAjBK,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;0BA4B5B,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA0B;;;;;;sCACxC,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAG1C,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;;;;;;0BAOL,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CAAmC;;;;;;;;;;;sCAI9D,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAGlC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAAiC;;;;;;8CAC/D,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAiC;;;;;;8CACjE,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAa;;;;;;8CAC7C,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAiC;;;;;;;;;;;;sCAEnE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMzC", "debugId": null}}]}