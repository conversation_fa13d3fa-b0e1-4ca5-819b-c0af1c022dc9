import{a as getWindow}from"../shared/ssr-window.esm.min.mjs";import{n as nextTick,f as now}from"../shared/utils.min.mjs";function Mousewheel(e){let{swiper:t,extendParams:a,on:s,emit:n}=e;const l=getWindow();let i;a({mousewheel:{enabled:!1,releaseOnEdges:!1,invert:!1,forceToAxis:!1,sensitivity:1,eventsTarget:"container",thresholdDelta:null,thresholdTime:null,noMousewheelClass:"swiper-no-mousewheel"}}),t.mousewheel={enabled:!1};let r,o=now();const d=[];function m(){t.enabled&&(t.mouseEntered=!0)}function p(){t.enabled&&(t.mouseEntered=!1)}function u(e){return!(t.params.mousewheel.thresholdDelta&&e.delta<t.params.mousewheel.thresholdDelta)&&(!(t.params.mousewheel.thresholdTime&&now()-o<t.params.mousewheel.thresholdTime)&&(e.delta>=6&&now()-o<60||(e.direction<0?t.isEnd&&!t.params.loop||t.animating||(t.slideNext(),n("scroll",e.raw)):t.isBeginning&&!t.params.loop||t.animating||(t.slidePrev(),n("scroll",e.raw)),o=(new l.Date).getTime(),!1)))}function h(e){let a=e,s=!0;if(!t.enabled)return;if(e.target.closest(`.${t.params.mousewheel.noMousewheelClass}`))return;const l=t.params.mousewheel;t.params.cssMode&&a.preventDefault();let o=t.el;"container"!==t.params.mousewheel.eventsTarget&&(o=document.querySelector(t.params.mousewheel.eventsTarget));const m=o&&o.contains(a.target);if(!t.mouseEntered&&!m&&!l.releaseOnEdges)return!0;a.originalEvent&&(a=a.originalEvent);let p=0;const h=t.rtlTranslate?-1:1,c=function(e){let t=0,a=0,s=0,n=0;return"detail"in e&&(a=e.detail),"wheelDelta"in e&&(a=-e.wheelDelta/120),"wheelDeltaY"in e&&(a=-e.wheelDeltaY/120),"wheelDeltaX"in e&&(t=-e.wheelDeltaX/120),"axis"in e&&e.axis===e.HORIZONTAL_AXIS&&(t=a,a=0),s=10*t,n=10*a,"deltaY"in e&&(n=e.deltaY),"deltaX"in e&&(s=e.deltaX),e.shiftKey&&!s&&(s=n,n=0),(s||n)&&e.deltaMode&&(1===e.deltaMode?(s*=40,n*=40):(s*=800,n*=800)),s&&!t&&(t=s<1?-1:1),n&&!a&&(a=n<1?-1:1),{spinX:t,spinY:a,pixelX:s,pixelY:n}}(a);if(l.forceToAxis)if(t.isHorizontal()){if(!(Math.abs(c.pixelX)>Math.abs(c.pixelY)))return!0;p=-c.pixelX*h}else{if(!(Math.abs(c.pixelY)>Math.abs(c.pixelX)))return!0;p=-c.pixelY}else p=Math.abs(c.pixelX)>Math.abs(c.pixelY)?-c.pixelX*h:-c.pixelY;if(0===p)return!0;l.invert&&(p=-p);let w=t.getTranslate()+p*l.sensitivity;if(w>=t.minTranslate()&&(w=t.minTranslate()),w<=t.maxTranslate()&&(w=t.maxTranslate()),s=!!t.params.loop||!(w===t.minTranslate()||w===t.maxTranslate()),s&&t.params.nested&&a.stopPropagation(),t.params.freeMode&&t.params.freeMode.enabled){const e={time:now(),delta:Math.abs(p),direction:Math.sign(p)},s=r&&e.time<r.time+500&&e.delta<=r.delta&&e.direction===r.direction;if(!s){r=void 0;let o=t.getTranslate()+p*l.sensitivity;const m=t.isBeginning,u=t.isEnd;if(o>=t.minTranslate()&&(o=t.minTranslate()),o<=t.maxTranslate()&&(o=t.maxTranslate()),t.setTransition(0),t.setTranslate(o),t.updateProgress(),t.updateActiveIndex(),t.updateSlidesClasses(),(!m&&t.isBeginning||!u&&t.isEnd)&&t.updateSlidesClasses(),t.params.loop&&t.loopFix({direction:e.direction<0?"next":"prev",byMousewheel:!0}),t.params.freeMode.sticky){clearTimeout(i),i=void 0,d.length>=15&&d.shift();const a=d.length?d[d.length-1]:void 0,s=d[0];if(d.push(e),a&&(e.delta>a.delta||e.direction!==a.direction))d.splice(0);else if(d.length>=15&&e.time-s.time<500&&s.delta-e.delta>=1&&e.delta<=6){const a=p>0?.8:.2;r=e,d.splice(0),i=nextTick((()=>{!t.destroyed&&t.params&&t.slideToClosest(t.params.speed,!0,void 0,a)}),0)}i||(i=nextTick((()=>{if(t.destroyed||!t.params)return;r=e,d.splice(0),t.slideToClosest(t.params.speed,!0,void 0,.5)}),500))}if(s||n("scroll",a),t.params.autoplay&&t.params.autoplay.disableOnInteraction&&t.autoplay.stop(),l.releaseOnEdges&&(o===t.minTranslate()||o===t.maxTranslate()))return!0}}else{const a={time:now(),delta:Math.abs(p),direction:Math.sign(p),raw:e};d.length>=2&&d.shift();const s=d.length?d[d.length-1]:void 0;if(d.push(a),s?(a.direction!==s.direction||a.delta>s.delta||a.time>s.time+150)&&u(a):u(a),function(e){const a=t.params.mousewheel;if(e.direction<0){if(t.isEnd&&!t.params.loop&&a.releaseOnEdges)return!0}else if(t.isBeginning&&!t.params.loop&&a.releaseOnEdges)return!0;return!1}(a))return!0}return a.preventDefault?a.preventDefault():a.returnValue=!1,!1}function c(e){let a=t.el;"container"!==t.params.mousewheel.eventsTarget&&(a=document.querySelector(t.params.mousewheel.eventsTarget)),a[e]("mouseenter",m),a[e]("mouseleave",p),a[e]("wheel",h)}function w(){return t.params.cssMode?(t.wrapperEl.removeEventListener("wheel",h),!0):!t.mousewheel.enabled&&(c("addEventListener"),t.mousewheel.enabled=!0,!0)}function f(){return t.params.cssMode?(t.wrapperEl.addEventListener(event,h),!0):!!t.mousewheel.enabled&&(c("removeEventListener"),t.mousewheel.enabled=!1,!0)}s("init",(()=>{!t.params.mousewheel.enabled&&t.params.cssMode&&f(),t.params.mousewheel.enabled&&w()})),s("destroy",(()=>{t.params.cssMode&&w(),t.mousewheel.enabled&&f()})),Object.assign(t.mousewheel,{enable:w,disable:f})}export{Mousewheel as default};
//# sourceMappingURL=mousewheel.min.mjs.map