'use client';

import Link from "next/link";
import { usePathname } from "next/navigation";

export default function Header() {
  const pathname = usePathname();

  const isActive = (path) => {
    return pathname === path;
  };

  return (
    <header className="header-style-1 bg-white shadow-lg fixed-top">
      <div className="container-fluid">
        <div className="row">
          <div className="col-12">
            <div className="header-inner d-flex justify-content-between align-items-center py-3">
              <div className="header-logo">
                <Link href="/" className="d-flex align-items-center text-decoration-none">
                  <div className="logo-placeholder d-flex align-items-center">
                    <div className="logo-icon bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2"
                         style={{width: '40px', height: '40px'}}>
                      <i className="fa fa-graduation-cap"></i>
                    </div>
                    <div>
                      <h4 className="mb-0 text-primary fw-bold">Smart</h4>
                      <small className="text-muted">Kinco</small>
                    </div>
                  </div>
                </Link>
              </div>
              <nav className="main-menu d-none d-lg-block">
                <ul className="d-flex list-unstyled mb-0 align-items-center">
                  <li className="menu-item">
                    <Link href="/" className={`menu-link text-decoration-none px-3 py-2 rounded-pill transition-all ${isActive('/') ? 'fw-semibold text-white bg-primary' : 'text-dark hover-primary'}`}>
                      Home
                    </Link>
                  </li>
                  <li className="menu-item">
                    <Link href="/about" className={`menu-link text-decoration-none px-3 py-2 rounded-pill transition-all ${isActive('/about') ? 'fw-semibold text-white bg-primary' : 'text-dark hover-primary'}`}>
                      About
                    </Link>
                  </li>
                  <li className="menu-item">
                    <Link href="/classes" className={`menu-link text-decoration-none px-3 py-2 rounded-pill transition-all ${isActive('/classes') ? 'fw-semibold text-white bg-primary' : 'text-dark hover-primary'}`}>
                      Classes
                    </Link>
                  </li>
                  <li className="menu-item">
                    <Link href="/program" className={`menu-link text-decoration-none px-3 py-2 rounded-pill transition-all ${isActive('/program') ? 'fw-semibold text-white bg-primary' : 'text-dark hover-primary'}`}>
                      Programs
                    </Link>
                  </li>
                  <li className="menu-item">
                    <Link href="/teacher" className={`menu-link text-decoration-none px-3 py-2 rounded-pill transition-all ${isActive('/teacher') ? 'fw-semibold text-white bg-primary' : 'text-dark hover-primary'}`}>
                      Teachers
                    </Link>
                  </li>
                  <li className="menu-item">
                    <Link href="/gallery" className={`menu-link text-decoration-none px-3 py-2 rounded-pill transition-all ${isActive('/gallery') ? 'fw-semibold text-white bg-primary' : 'text-dark hover-primary'}`}>
                      Gallery
                    </Link>
                  </li>
                  <li className="menu-item">
                    <Link href="/blog" className={`menu-link text-decoration-none px-3 py-2 rounded-pill transition-all ${isActive('/blog') ? 'fw-semibold text-white bg-primary' : 'text-dark hover-primary'}`}>
                      Blog
                    </Link>
                  </li>
                  <li className="menu-item">
                    <Link href="/events" className={`menu-link text-decoration-none px-3 py-2 rounded-pill transition-all ${isActive('/events') ? 'fw-semibold text-white bg-primary' : 'text-dark hover-primary'}`}>
                      Events
                    </Link>
                  </li>
                  <li className="menu-item">
                    <Link href="/contact" className={`menu-link text-decoration-none px-3 py-2 rounded-pill transition-all ${isActive('/contact') ? 'fw-semibold text-white bg-primary' : 'text-dark hover-primary'}`}>
                      Contact
                    </Link>
                  </li>
                </ul>
              </nav>
              <div className="header-mobile d-lg-none">
                <button className="mobile-menu-toggle btn btn-primary">
                  <i className="fa fa-bars"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
}
