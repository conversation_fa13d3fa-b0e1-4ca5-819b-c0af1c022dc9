{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kinco-day-care-kindergarten-react-template-30-06-2025/Kinco/React%20Template/kinco-modern/src/app/about/page.js"], "sourcesContent": ["'use client';\n\nimport Link from \"next/link\";\n\nexport default function About() {\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm\">\n        <div className=\"container mx-auto px-4 py-4\">\n          <div className=\"flex justify-between items-center\">\n            <Link href=\"/\" className=\"text-2xl font-bold text-blue-600\">\n              Kinco School\n            </Link>\n            <nav className=\"hidden md:flex space-x-6\">\n              <Link href=\"/about\" className=\"text-blue-600 font-semibold\">About</Link>\n              <Link href=\"/classes\" className=\"text-gray-600 hover:text-blue-600\">Classes</Link>\n              <Link href=\"/contact\" className=\"text-gray-600 hover:text-blue-600\">Contact</Link>\n            </nav>\n          </div>\n        </div>\n      </header>\n\n      {/* Page Banner */}\n      <section className=\"bg-gradient-to-r from-blue-500 to-purple-600 text-white py-16\">\n        <div className=\"container mx-auto px-4 text-center\">\n          <h1 className=\"text-4xl font-bold mb-4\">About Kinco School</h1>\n          <nav className=\"text-blue-200\">\n            <Link href=\"/\" className=\"hover:text-white\">Home</Link>\n            <span className=\"mx-2\">/</span>\n            <span>About</span>\n          </nav>\n        </div>\n      </section>\n\n      {/* About Content */}\n      <section className=\"py-16\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"grid md:grid-cols-2 gap-12 items-center mb-16\">\n            <div>\n              <div className=\"text-blue-600 font-semibold mb-2\">About Us</div>\n              <h2 className=\"text-4xl font-bold mb-6 text-gray-800\">\n                We Provide The Best Education For Your Children\n              </h2>\n              <p className=\"text-gray-600 mb-6 leading-relaxed\">\n                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut elit tellus, luctus nec ullamcorper mattis, pulvinar dapibus leo. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.\n              </p>\n              <p className=\"text-gray-600 mb-8 leading-relaxed\">\n                Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.\n              </p>\n              <div className=\"grid grid-cols-2 gap-4 mb-8\">\n                <div className=\"text-center p-4 bg-blue-50 rounded-lg\">\n                  <div className=\"text-3xl font-bold text-blue-600 mb-2\">15+</div>\n                  <div className=\"text-gray-600\">Years Experience</div>\n                </div>\n                <div className=\"text-center p-4 bg-blue-50 rounded-lg\">\n                  <div className=\"text-3xl font-bold text-blue-600 mb-2\">500+</div>\n                  <div className=\"text-gray-600\">Happy Students</div>\n                </div>\n              </div>\n            </div>\n            <div>\n              <img\n                src=\"/assets/images/common/sc-about1.jpg\"\n                alt=\"About Kinco\"\n                className=\"rounded-lg shadow-lg w-full\"\n              />\n            </div>\n          </div>\n\n          {/* Mission & Vision */}\n          <div className=\"grid md:grid-cols-2 gap-12\">\n            <div className=\"bg-white p-8 rounded-lg shadow-md\">\n              <h3 className=\"text-2xl font-bold mb-4 text-gray-800\">Our Mission</h3>\n              <p className=\"text-gray-600 leading-relaxed\">\n                To provide a nurturing and stimulating environment where children can develop their full potential through quality education, creative expression, and character building.\n              </p>\n            </div>\n            <div className=\"bg-white p-8 rounded-lg shadow-md\">\n              <h3 className=\"text-2xl font-bold mb-4 text-gray-800\">Our Vision</h3>\n              <p className=\"text-gray-600 leading-relaxed\">\n                To be the leading educational institution that shapes confident, creative, and compassionate individuals who will contribute positively to society.\n              </p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"bg-gray-800 text-white py-8\">\n        <div className=\"container mx-auto px-4 text-center\">\n          <div className=\"mb-4\">\n            <Link href=\"/\" className=\"text-2xl font-bold text-blue-400\">\n              Kinco School\n            </Link>\n          </div>\n          <p className=\"text-gray-400 mb-4\">\n            Providing quality education and care for your children\n          </p>\n          <div className=\"flex justify-center space-x-6\">\n            <Link href=\"/about\" className=\"text-white\">About</Link>\n            <Link href=\"/classes\" className=\"text-gray-400 hover:text-white\">Classes</Link>\n            <Link href=\"/contact\" className=\"text-gray-400 hover:text-white\">Contact</Link>\n          </div>\n          <div className=\"mt-6 pt-6 border-t border-gray-700\">\n            <p className=\"text-gray-400\">© 2024 Kinco School. All rights reserved.</p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CAAmC;;;;;;0CAG5D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAA8B;;;;;;kDAC5D,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAoC;;;;;;kDACpE,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO5E,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA0B;;;;;;sCACxC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAmB;;;;;;8CAC5C,8OAAC;oCAAK,WAAU;8CAAO;;;;;;8CACvB,8OAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;0BAMZ,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDAAmC;;;;;;sDAClD,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;sDAGtD,8OAAC;4CAAE,WAAU;sDAAqC;;;;;;sDAGlD,8OAAC;4CAAE,WAAU;sDAAqC;;;;;;sDAGlD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAwC;;;;;;sEACvD,8OAAC;4DAAI,WAAU;sEAAgB;;;;;;;;;;;;8DAEjC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAwC;;;;;;sEACvD,8OAAC;4DAAI,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;;;;;;;8CAIrC,8OAAC;8CACC,cAAA,8OAAC;wCACC,KAAI;wCACJ,KAAI;wCACJ,WAAU;;;;;;;;;;;;;;;;;sCAMhB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;8CAI/C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASrD,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CAAmC;;;;;;;;;;;sCAI9D,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAGlC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAAa;;;;;;8CAC3C,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAiC;;;;;;8CACjE,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAiC;;;;;;;;;;;;sCAEnE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMzC", "debugId": null}}]}