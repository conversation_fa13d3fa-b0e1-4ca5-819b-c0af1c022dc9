'use client';

import Link from "next/link";
import { useState } from "react";

export default function Gallery() {
  const [selectedCategory, setSelectedCategory] = useState('all');

  const categories = [
    { id: 'all', name: 'All Photos' },
    { id: 'classroom', name: 'Classroom' },
    { id: 'activities', name: 'Activities' },
    { id: 'events', name: 'Events' },
    { id: 'outdoor', name: 'Outdoor Play' }
  ];

  const galleryItems = [
    { id: 1, category: 'classroom', image: '/assets/images/common/sc-gallery-1.jpg', title: 'Reading Time' },
    { id: 2, category: 'activities', image: '/assets/images/common/sc-gallery-2.jpg', title: 'Art Class' },
    { id: 3, category: 'outdoor', image: '/assets/images/common/sc-gallery-3.jpg', title: 'Playground Fun' },
    { id: 4, category: 'events', image: '/assets/images/common/sc-gallery-4.jpg', title: 'School Event' },
    { id: 5, category: 'classroom', image: '/assets/images/common/sc-gallery-5.jpg', title: 'Learning Together' },
    { id: 6, category: 'activities', image: '/assets/images/common/sc-gallery-6.jpg', title: 'Music Class' },
    { id: 7, category: 'outdoor', image: '/assets/images/common/sc-gallery-7.jpg', title: 'Garden Time' },
    { id: 8, category: 'events', image: '/assets/images/common/sc-gallery-8.jpg', title: 'Graduation Day' },
    { id: 9, category: 'classroom', image: '/assets/images/common/sc-gallery-9.jpg', title: 'Science Exploration' },
    { id: 10, category: 'activities', image: '/assets/images/common/sc-program1.jpg', title: 'Creative Play' },
    { id: 11, category: 'outdoor', image: '/assets/images/common/sc-program2.jpg', title: 'Sports Day' },
    { id: 12, category: 'events', image: '/assets/images/common/sc-program3.jpg', title: 'Parent Day' }
  ];

  const filteredItems = selectedCategory === 'all' 
    ? galleryItems 
    : galleryItems.filter(item => item.category === selectedCategory);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex justify-between items-center">
            <Link href="/" className="text-2xl font-bold text-blue-600">
              Kinco School
            </Link>
            <nav className="hidden md:flex space-x-6">
              <Link href="/about" className="text-gray-600 hover:text-blue-600">About</Link>
              <Link href="/classes" className="text-gray-600 hover:text-blue-600">Classes</Link>
              <Link href="/gallery" className="text-blue-600 font-semibold">Gallery</Link>
              <Link href="/contact" className="text-gray-600 hover:text-blue-600">Contact</Link>
            </nav>
          </div>
        </div>
      </header>

      {/* Page Banner */}
      <section className="bg-gradient-to-r from-blue-500 to-purple-600 text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl font-bold mb-4">Photo Gallery</h1>
          <nav className="text-blue-200">
            <Link href="/" className="hover:text-white">Home</Link>
            <span className="mx-2">/</span>
            <span>Gallery</span>
          </nav>
        </div>
      </section>

      {/* Gallery Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold mb-4 text-gray-800">Moments at Kinco School</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Take a look at the wonderful moments and activities that happen every day at our school
            </p>
          </div>

          {/* Filter Buttons */}
          <div className="flex flex-wrap justify-center gap-4 mb-12">
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`px-6 py-2 rounded-full font-semibold transition-colors ${
                  selectedCategory === category.id
                    ? 'bg-blue-600 text-white'
                    : 'bg-white text-gray-600 hover:bg-blue-50 hover:text-blue-600'
                }`}
              >
                {category.name}
              </button>
            ))}
          </div>

          {/* Gallery Grid */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredItems.map((item) => (
              <div key={item.id} className="group relative overflow-hidden rounded-lg shadow-md hover:shadow-lg transition-shadow">
                <img 
                  src={item.image} 
                  alt={item.title}
                  className="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-300 flex items-center justify-center">
                  <div className="text-white text-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <h3 className="text-lg font-semibold mb-2">{item.title}</h3>
                    <button className="bg-white text-blue-600 px-4 py-2 rounded-lg font-semibold hover:bg-blue-50 transition-colors">
                      View
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {filteredItems.length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-500 text-lg">No photos found in this category.</p>
            </div>
          )}
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-blue-600 text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">Want to See More?</h2>
          <p className="text-xl mb-8 text-blue-100">
            Visit our school to experience the joy and learning firsthand
          </p>
          <Link 
            href="/contact" 
            className="bg-yellow-400 text-blue-900 px-8 py-3 rounded-lg font-semibold hover:bg-yellow-300 transition-colors"
          >
            Schedule a Visit
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-800 text-white py-8">
        <div className="container mx-auto px-4 text-center">
          <div className="mb-4">
            <Link href="/" className="text-2xl font-bold text-blue-400">
              Kinco School
            </Link>
          </div>
          <p className="text-gray-400 mb-4">
            Providing quality education and care for your children
          </p>
          <div className="flex justify-center space-x-6">
            <Link href="/about" className="text-gray-400 hover:text-white">About</Link>
            <Link href="/classes" className="text-gray-400 hover:text-white">Classes</Link>
            <Link href="/gallery" className="text-white">Gallery</Link>
            <Link href="/contact" className="text-gray-400 hover:text-white">Contact</Link>
          </div>
          <div className="mt-6 pt-6 border-t border-gray-700">
            <p className="text-gray-400">© 2024 Kinco School. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
