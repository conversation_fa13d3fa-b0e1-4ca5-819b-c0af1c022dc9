{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kinco-day-care-kindergarten-react-template-30-06-2025/Kinco/React%20Template/kinco-modern/src/lib/components/VideoPopup.js"], "sourcesContent": ["import React, { Fragment, useEffect, useRef, useState } from \"react\";\r\n\r\nlet useClickOutside = (handler) => {\r\n  let domNode = useRef();\r\n\r\n  useEffect(() => {\r\n    let maybeHandler = (event) => {\r\n      if (!domNode.current.contains(event.target)) {\r\n        handler();\r\n      }\r\n    };\r\n\r\n    document.addEventListener(\"mousedown\", maybeHandler);\r\n\r\n    return () => {\r\n      document.removeEventListener(\"mousedown\", maybeHandler);\r\n    };\r\n  });\r\n\r\n  return domNode;\r\n};\r\n\r\nconst VideoPopup_ = ({ close, videoID }) => {\r\n  let domNode = useClickOutside(() => {\r\n    close(false);\r\n  });\r\n  return (\r\n    <Fragment>\r\n      <div className=\"mfp-bg mfp-ready\" onClick={() => close(false)}></div>\r\n      <div\r\n        className=\"mfp-wrap mfp-close-btn-in mfp-auto-cursor mfp-ready\"\r\n        tabIndex={-1}\r\n        style={{ overflow: \"hidden auto\" }}\r\n      >\r\n        <div className=\"mfp-container mfp-s-ready mfp-iframe-holder\">\r\n          <div className=\"mfp-content\" ref={domNode}>\r\n            <div className=\"mfp-iframe-scaler\">\r\n              <button\r\n                title=\"Close (Esc)\"\r\n                type=\"button\"\r\n                className=\"mfp-close\"\r\n                onClick={() => close()}\r\n              >\r\n                ×\r\n              </button>\r\n              <iframe\r\n                src=\"https://www.youtube.com/embed/nfP5N9Yc72A?autoplay=1\"\r\n                title=\"YouTube video player\"\r\n                frameBorder=\"0\"\r\n                allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\r\n                allowFullScreen\r\n              ></iframe>\r\n            </div>\r\n          </div>\r\n          <div className=\"mfp-preloader\">Loading...</div>\r\n        </div>\r\n      </div>\r\n    </Fragment>\r\n  );\r\n};\r\n\r\nconst VideoPopup = () => {\r\n  const [video, setVideo] = useState(false);\r\n  const [videoValue, setVideoValue] = useState(null);\r\n  useEffect(() => {\r\n    setTimeout(() => {\r\n      const a = document.querySelectorAll(\"a\");\r\n      a.forEach((a) => {\r\n        if (a.href.includes(\"https://www.youtube.com\")) {\r\n          a.addEventListener(\"click\", (e) => {\r\n            e.preventDefault();\r\n            setVideoValue(a.href);\r\n            setVideo(true);\r\n          });\r\n        }\r\n      });\r\n    }, 1000);\r\n  }, []);\r\n  return (\r\n    <Fragment>\r\n      {video && (\r\n        <VideoPopup_ close={() => setVideo(false)} videoID={videoValue} />\r\n      )}\r\n    </Fragment>\r\n  );\r\n};\r\n\r\nexport default VideoPopup;\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,IAAI,kBAAkB,CAAC;IACrB,IAAI,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IAEnB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,eAAe,CAAC;YAClB,IAAI,CAAC,QAAQ,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAG;gBAC3C;YACF;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QAEvC,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF;IAEA,OAAO;AACT;AAEA,MAAM,cAAc,CAAC,EAAE,KAAK,EAAE,OAAO,EAAE;IACrC,IAAI,UAAU,gBAAgB;QAC5B,MAAM;IACR;IACA,qBACE,8OAAC,qMAAA,CAAA,WAAQ;;0BACP,8OAAC;gBAA<PERSON>,WAAU;gBAAmB,SAAS,IAAM,MAAM;;;;;;0BACvD,8OAAC;gBACC,WAAU;gBACV,UAAU,CAAC;gBACX,OAAO;oBAAE,UAAU;gBAAc;0BAEjC,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;4BAAc,KAAK;sCAChC,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,OAAM;wCACN,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM;kDAChB;;;;;;kDAGD,8OAAC;wCACC,KAAI;wCACJ,OAAM;wCACN,aAAY;wCACZ,OAAM;wCACN,eAAe;;;;;;;;;;;;;;;;;sCAIrB,8OAAC;4BAAI,WAAU;sCAAgB;;;;;;;;;;;;;;;;;;;;;;;AAKzC;AAEA,MAAM,aAAa;IACjB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;YACT,MAAM,IAAI,SAAS,gBAAgB,CAAC;YACpC,EAAE,OAAO,CAAC,CAAC;gBACT,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,4BAA4B;oBAC9C,EAAE,gBAAgB,CAAC,SAAS,CAAC;wBAC3B,EAAE,cAAc;wBAChB,cAAc,EAAE,IAAI;wBACpB,SAAS;oBACX;gBACF;YACF;QACF,GAAG;IACL,GAAG,EAAE;IACL,qBACE,8OAAC,qMAAA,CAAA,WAAQ;kBACN,uBACC,8OAAC;YAAY,OAAO,IAAM,SAAS;YAAQ,SAAS;;;;;;;;;;;AAI5D;uCAEe", "debugId": null}}, {"offset": {"line": 188, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kinco-day-care-kindergarten-react-template-30-06-2025/Kinco/React%20Template/kinco-modern/src/lib/utils.js"], "sourcesContent": ["export const activeNavMenu = (path) => {\r\n  const navItem = document.querySelectorAll(\"#mainnav li a\");\r\n\r\n  navItem.forEach((nav) => {\r\n    if (nav.pathname === window.location.pathname) {\r\n      if (!nav.href.includes(\"#\")) {\r\n        if (nav.pathname === \"/contact\" || nav.pathname === \"/about\") {\r\n          nav.parentElement.className = \"current-menu-item\";\r\n        } else {\r\n          nav.parentElement.className = \"inner-current-item\";\r\n          !document.querySelector(\"body\").className.includes(\"style\") &&\r\n            nav.parentElement.parentElement.parentElement.classList.add(\r\n              \"current-item\"\r\n            );\r\n          nav.parentElement.parentElement.parentElement.classList.add(\r\n            \"current-menu-item\"\r\n          );\r\n          nav.parentElement.parentElement.parentElement.parentElement.parentElement.classList.add(\r\n            \"current-menu-item\"\r\n          );\r\n        }\r\n      }\r\n    }\r\n  });\r\n};\r\n\r\nexport const animation = () => {\r\n  if (typeof window !== \"undefined\") {\r\n    window.WOW = require(\"wowjs\");\r\n  }\r\n  new WOW.WOW().init();\r\n};\r\nconst stickyNav_ = () => {\r\n  let offset = window.scrollY;\r\n  const stickys = document.querySelectorAll(\"#site-header\");\r\n  for (let i = 0; i < stickys.length; i++) {\r\n    const sticky = stickys[i];\r\n    if (sticky) {\r\n      if (offset > 10) {\r\n        sticky.classList.add(\"is-fixed\");\r\n      } else {\r\n        sticky.classList.remove(\"is-fixed\");\r\n      }\r\n      if (offset > 100) {\r\n        sticky.classList.add(\"is-small\");\r\n      } else {\r\n        sticky.classList.remove(\"is-small\");\r\n      }\r\n    }\r\n  }\r\n};\r\n\r\nexport const stickyNav = (stickyClass) =>\r\n  window.addEventListener(\"scroll\", stickyNav_);\r\n\r\nexport const pagination = (listClass, sort, active) => {\r\n  let list = document.querySelectorAll(listClass);\r\n  for (let i = 0; i < list.length; i++) {\r\n    const element = list[i];\r\n    if (active === 1) {\r\n      if (i < sort) {\r\n        element.classList.remove(\"d-none\");\r\n      } else {\r\n        element.classList.add(\"d-none\");\r\n      }\r\n    } else {\r\n      if (i >= (active - 1) * sort && i < active * sort) {\r\n        element.classList.remove(\"d-none\");\r\n      } else {\r\n        element.classList.add(\"d-none\");\r\n      }\r\n    }\r\n  }\r\n};\r\n\r\nexport const getPagination = (totalNumber, sort) => {\r\n  let arr = new Array(Math.ceil(totalNumber / sort))\r\n    .fill()\r\n    .map((_, idx) => idx + 1);\r\n  return arr;\r\n};\r\n\r\nexport const scrollTopFun = () => {\r\n  let scrollUp = document.getElementById(\"scroll-top\"),\r\n    lastScrollTop = 0;\r\n\r\n  window.addEventListener(\"scroll\", () => {\r\n    let st = window.scrollY;\r\n    if (st > 110) {\r\n      scrollUp.classList.add(\"show\");\r\n    } else {\r\n      scrollUp.classList.remove(\"show\");\r\n    }\r\n  });\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;AAAO,MAAM,gBAAgB,CAAC;IAC5B,MAAM,UAAU,SAAS,gBAAgB,CAAC;IAE1C,QAAQ,OAAO,CAAC,CAAC;QACf,IAAI,IAAI,QAAQ,KAAK,OAAO,QAAQ,CAAC,QAAQ,EAAE;YAC7C,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM;gBAC3B,IAAI,IAAI,QAAQ,KAAK,cAAc,IAAI,QAAQ,KAAK,UAAU;oBAC5D,IAAI,aAAa,CAAC,SAAS,GAAG;gBAChC,OAAO;oBACL,IAAI,aAAa,CAAC,SAAS,GAAG;oBAC9B,CAAC,SAAS,aAAa,CAAC,QAAQ,SAAS,CAAC,QAAQ,CAAC,YACjD,IAAI,aAAa,CAAC,aAAa,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,CACzD;oBAEJ,IAAI,aAAa,CAAC,aAAa,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,CACzD;oBAEF,IAAI,aAAa,CAAC,aAAa,CAAC,aAAa,CAAC,aAAa,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,CACrF;gBAEJ;YACF;QACF;IACF;AACF;AAEO,MAAM,YAAY;IACvB,uCAAmC;;IAEnC;IACA,IAAI,IAAI,GAAG,GAAG,IAAI;AACpB;AACA,MAAM,aAAa;IACjB,IAAI,SAAS,OAAO,OAAO;IAC3B,MAAM,UAAU,SAAS,gBAAgB,CAAC;IAC1C,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;QACvC,MAAM,SAAS,OAAO,CAAC,EAAE;QACzB,IAAI,QAAQ;YACV,IAAI,SAAS,IAAI;gBACf,OAAO,SAAS,CAAC,GAAG,CAAC;YACvB,OAAO;gBACL,OAAO,SAAS,CAAC,MAAM,CAAC;YAC1B;YACA,IAAI,SAAS,KAAK;gBAChB,OAAO,SAAS,CAAC,GAAG,CAAC;YACvB,OAAO;gBACL,OAAO,SAAS,CAAC,MAAM,CAAC;YAC1B;QACF;IACF;AACF;AAEO,MAAM,YAAY,CAAC,cACxB,OAAO,gBAAgB,CAAC,UAAU;AAE7B,MAAM,aAAa,CAAC,WAAW,MAAM;IAC1C,IAAI,OAAO,SAAS,gBAAgB,CAAC;IACrC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;QACpC,MAAM,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,WAAW,GAAG;YAChB,IAAI,IAAI,MAAM;gBACZ,QAAQ,SAAS,CAAC,MAAM,CAAC;YAC3B,OAAO;gBACL,QAAQ,SAAS,CAAC,GAAG,CAAC;YACxB;QACF,OAAO;YACL,IAAI,KAAK,CAAC,SAAS,CAAC,IAAI,QAAQ,IAAI,SAAS,MAAM;gBACjD,QAAQ,SAAS,CAAC,MAAM,CAAC;YAC3B,OAAO;gBACL,QAAQ,SAAS,CAAC,GAAG,CAAC;YACxB;QACF;IACF;AACF;AAEO,MAAM,gBAAgB,CAAC,aAAa;IACzC,IAAI,MAAM,IAAI,MAAM,KAAK,IAAI,CAAC,cAAc,OACzC,IAAI,GACJ,GAAG,CAAC,CAAC,GAAG,MAAQ,MAAM;IACzB,OAAO;AACT;AAEO,MAAM,eAAe;IAC1B,IAAI,WAAW,SAAS,cAAc,CAAC,eACrC,gBAAgB;IAElB,OAAO,gBAAgB,CAAC,UAAU;QAChC,IAAI,KAAK,OAAO,OAAO;QACvB,IAAI,KAAK,KAAK;YACZ,SAAS,SAAS,CAAC,GAAG,CAAC;QACzB,OAAO;YACL,SAAS,SAAS,CAAC,MAAM,CAAC;QAC5B;IACF;AACF", "debugId": null}}, {"offset": {"line": 279, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kinco-day-care-kindergarten-react-template-30-06-2025/Kinco/React%20Template/kinco-modern/src/lib/layouts/Footer.js"], "sourcesContent": ["import Link from \"next/link\";\r\nconst Footer = () => {\r\n  return (\r\n    <footer id=\"footer\">\r\n      <section className=\"tf-subcribe\">\r\n        <div className=\"container\">\r\n          <div className=\"row\">\r\n            <div className=\"col-lg-6 col-md-12 col-12\">\r\n              <div className=\"subcribe-wp\">\r\n                <h2 className=\"title\">Subscribe Our Newsletter</h2>\r\n                <p className=\"sub f-mulish\">\r\n                  Beet consectetur adipiscing elit, sed do eiusmod tempor\r\n                  incididunt ut labore\r\n                </p>\r\n              </div>\r\n            </div>\r\n            <div className=\"col-lg-6 col-md-12 col-12\">\r\n              <div className=\"subcribe-form fx\" id=\"subscribe-form\">\r\n                <form action=\"#\" onSubmit={(e) => e.preventDefault()}>\r\n                  <input\r\n                    type=\"email\"\r\n                    id=\"subscribe-email\"\r\n                    placeholder=\"Email Address\"\r\n                  />\r\n                  <button className=\"fl-btn st-7\" id=\"subscribe-button\">\r\n                    <span className=\"inner\">Subscribe</span>\r\n                  </button>\r\n                </form>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n      <div className=\"footer-inner\">\r\n        <div className=\"container\">\r\n          <div className=\"row\">\r\n            <div className=\"col-12\">\r\n              <div className=\"widget-footer\">\r\n                <div className=\"widget widget-logo\">\r\n                  <div className=\"logo-bottom\" id=\"logo-footer\">\r\n                    <Link href=\"/\">\r\n                      <a>\r\n                        <img\r\n                          src=\"assets/images/logo/logofootert.png\"\r\n                          alt=\"kinco\"\r\n                        />\r\n                      </a>\r\n                    </Link>\r\n                  </div>\r\n                  <p className=\"wrap f-mulish\">\r\n                    Sit amet consectetur adipiscing elit sed do eiusmod teminci\r\n                    idunt ut labore et dolore magna\r\n                  </p>\r\n                  <div className=\"list-contact\">\r\n                    <ul>\r\n                      <li className=\"fx\">\r\n                        <span>\r\n                          <i className=\"far fa-map-marker-alt\" /> 55 Main\r\n                          Street, New York\r\n                        </span>\r\n                      </li>\r\n                      <li className=\"fx\">\r\n                        <a href=\"mailto:<EMAIL>\">\r\n                          <i className=\"far fa-envelope\" /> <EMAIL>\r\n                        </a>\r\n                      </li>\r\n                      <li className=\"fx\">\r\n                        <a href=\"tel:012345678\">\r\n                          <i className=\"fal fa-phone\" /> +012 (345) 678\r\n                        </a>\r\n                      </li>\r\n                    </ul>\r\n                  </div>\r\n                </div>\r\n                <div className=\"widget widget-business\">\r\n                  <div className=\"inner\">\r\n                    <div className=\"op-time\">\r\n                      <h4 className=\"title-widget\">opening hours</h4>\r\n                      <ul>\r\n                        <li>\r\n                          <span className=\"f-mulish\">Sunday - Friday</span>\r\n                        </li>\r\n                        <li>\r\n                          <span className=\"f-mulish\">08 am - 05 pm</span>\r\n                        </li>\r\n                      </ul>\r\n                    </div>\r\n                    <div className=\"cls-time\">\r\n                      <p>Every Satarday and Govt Holiday</p>\r\n                      <h4 className=\"title-widget\">closed</h4>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <div className=\"widget widget-link\">\r\n                  <h4 className=\"title-widget\">Our Program</h4>\r\n                  <ul className=\"list-link\">\r\n                    <li className=\"fx\">\r\n                      <Link href=\"/program\">\r\n                        <a className=\"wd-ctm f-mulish\">Arts &amp; Drawing</a>\r\n                      </Link>\r\n                    </li>\r\n                    <li className=\"fx\">\r\n                      <Link href=\"/program\">\r\n                        <a className=\"wd-ctm f-mulish\">Computer Engineering </a>\r\n                      </Link>\r\n                    </li>\r\n                    <li className=\"fx\">\r\n                      <Link href=\"/program\">\r\n                        <a className=\"wd-ctm f-mulish\">Digital Mathematics</a>\r\n                      </Link>\r\n                    </li>\r\n                    <li className=\"fx\">\r\n                      <Link href=\"/program\">\r\n                        <a className=\"wd-ctm f-mulish\">Physical Exercise</a>\r\n                      </Link>\r\n                    </li>\r\n                    <li className=\"fx\">\r\n                      <Link href=\"/program\">\r\n                        <a className=\"wd-ctm f-mulish\">General Science</a>\r\n                      </Link>\r\n                    </li>\r\n                    <li className=\"fx\">\r\n                      <Link href=\"/program\">\r\n                        <a className=\"wd-ctm f-mulish\">English Basic</a>\r\n                      </Link>\r\n                    </li>\r\n                    <li className=\"fx\">\r\n                      <Link href=\"/program\">\r\n                        <a className=\"wd-ctm f-mulish\">Social Science</a>\r\n                      </Link>\r\n                    </li>\r\n                  </ul>\r\n                </div>\r\n                <div className=\"widget widget-news st-3\">\r\n                  <h4 className=\"title-widget\">recent news</h4>\r\n                  <ul className=\"list-news\">\r\n                    <li className=\"fx\">\r\n                      <img\r\n                        src=\"assets/images/thumbnails/widget9.jpg\"\r\n                        alt=\"Image\"\r\n                        className=\"feature\"\r\n                      />\r\n                      <ul className=\"box-content\">\r\n                        <li>\r\n                          <h6 className=\"title\">\r\n                            <Link href=\"/blog-grid\">\r\n                              <a>Useful Code Extened End Developers</a>\r\n                            </Link>\r\n                          </h6>\r\n                        </li>\r\n                        <li>\r\n                          <Link href=\"/blog-grid\">\r\n                            <a className=\"fx meta-news clr-pri-6\">\r\n                              <i className=\"far fa-calendar-alt\" />\r\n                              25 dec 2021\r\n                            </a>\r\n                          </Link>\r\n                        </li>\r\n                      </ul>\r\n                    </li>\r\n                    <li className=\"fx\">\r\n                      <img\r\n                        src=\"assets/images/thumbnails/widget10.jpg\"\r\n                        alt=\"Image\"\r\n                        className=\"feature\"\r\n                      />\r\n                      <ul className=\"box-content\">\r\n                        <li>\r\n                          <h6 className=\"title\">\r\n                            <Link href=\"/blog-grid\">\r\n                              <a>Useful Code Extened End Developers</a>\r\n                            </Link>\r\n                          </h6>\r\n                        </li>\r\n                        <li>\r\n                          <Link href=\"/blog-grid\">\r\n                            <a className=\"fx meta-news clr-pri-6\">\r\n                              <i className=\"far fa-calendar-alt\" />\r\n                              25 dec 2021\r\n                            </a>\r\n                          </Link>\r\n                        </li>\r\n                      </ul>\r\n                    </li>\r\n                  </ul>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"col-12\">\r\n              <div className=\"footer-bottom jus-ct\">\r\n                <p className=\"copy-right\">\r\n                  Copyright © {new Date().getFullYear()}, Kinco - Kindergarten\r\n                  HTML Template. Designed by{\" \"}\r\n                  <a href=\"https://themeforest.net/user/webtend/portfolio\">\r\n                    Webtend\r\n                  </a>\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </footer>\r\n  );\r\n};\r\nexport default Footer;\r\n"], "names": [], "mappings": ";;;;AAAA;;;AACA,MAAM,SAAS;IACb,qBACE,8OAAC;QAAO,IAAG;;0BACT,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAQ;;;;;;sDACtB,8OAAC;4CAAE,WAAU;sDAAe;;;;;;;;;;;;;;;;;0CAMhC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;oCAAmB,IAAG;8CACnC,cAAA,8OAAC;wCAAK,QAAO;wCAAI,UAAU,CAAC,IAAM,EAAE,cAAc;;0DAChD,8OAAC;gDACC,MAAK;gDACL,IAAG;gDACH,aAAY;;;;;;0DAEd,8OAAC;gDAAO,WAAU;gDAAc,IAAG;0DACjC,cAAA,8OAAC;oDAAK,WAAU;8DAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQtC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;oDAAc,IAAG;8DAC9B,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;kEACT,cAAA,8OAAC;sEACC,cAAA,8OAAC;gEACC,KAAI;gEACJ,KAAI;;;;;;;;;;;;;;;;;;;;;8DAKZ,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;8DAI7B,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EACZ,cAAA,8OAAC;;sFACC,8OAAC;4EAAE,WAAU;;;;;;wEAA0B;;;;;;;;;;;;0EAI3C,8OAAC;gEAAG,WAAU;0EACZ,cAAA,8OAAC;oEAAE,MAAK;;sFACN,8OAAC;4EAAE,WAAU;;;;;;wEAAoB;;;;;;;;;;;;0EAGrC,8OAAC;gEAAG,WAAU;0EACZ,cAAA,8OAAC;oEAAE,MAAK;;sFACN,8OAAC;4EAAE,WAAU;;;;;;wEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAMxC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAe;;;;;;0EAC7B,8OAAC;;kFACC,8OAAC;kFACC,cAAA,8OAAC;4EAAK,WAAU;sFAAW;;;;;;;;;;;kFAE7B,8OAAC;kFACC,cAAA,8OAAC;4EAAK,WAAU;sFAAW;;;;;;;;;;;;;;;;;;;;;;;kEAIjC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAE;;;;;;0EACH,8OAAC;gEAAG,WAAU;0EAAe;;;;;;;;;;;;;;;;;;;;;;;sDAInC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAe;;;;;;8DAC7B,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;gEAAC,MAAK;0EACT,cAAA,8OAAC;oEAAE,WAAU;8EAAkB;;;;;;;;;;;;;;;;sEAGnC,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;gEAAC,MAAK;0EACT,cAAA,8OAAC;oEAAE,WAAU;8EAAkB;;;;;;;;;;;;;;;;sEAGnC,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;gEAAC,MAAK;0EACT,cAAA,8OAAC;oEAAE,WAAU;8EAAkB;;;;;;;;;;;;;;;;sEAGnC,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;gEAAC,MAAK;0EACT,cAAA,8OAAC;oEAAE,WAAU;8EAAkB;;;;;;;;;;;;;;;;sEAGnC,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;gEAAC,MAAK;0EACT,cAAA,8OAAC;oEAAE,WAAU;8EAAkB;;;;;;;;;;;;;;;;sEAGnC,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;gEAAC,MAAK;0EACT,cAAA,8OAAC;oEAAE,WAAU;8EAAkB;;;;;;;;;;;;;;;;sEAGnC,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;gEAAC,MAAK;0EACT,cAAA,8OAAC;oEAAE,WAAU;8EAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAKvC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAe;;;;;;8DAC7B,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEACC,KAAI;oEACJ,KAAI;oEACJ,WAAU;;;;;;8EAEZ,8OAAC;oEAAG,WAAU;;sFACZ,8OAAC;sFACC,cAAA,8OAAC;gFAAG,WAAU;0FACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;oFAAC,MAAK;8FACT,cAAA,8OAAC;kGAAE;;;;;;;;;;;;;;;;;;;;;sFAIT,8OAAC;sFACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gFAAC,MAAK;0FACT,cAAA,8OAAC;oFAAE,WAAU;;sGACX,8OAAC;4FAAE,WAAU;;;;;;wFAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sEAO/C,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEACC,KAAI;oEACJ,KAAI;oEACJ,WAAU;;;;;;8EAEZ,8OAAC;oEAAG,WAAU;;sFACZ,8OAAC;sFACC,cAAA,8OAAC;gFAAG,WAAU;0FACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;oFAAC,MAAK;8FACT,cAAA,8OAAC;kGAAE;;;;;;;;;;;;;;;;;;;;;sFAIT,8OAAC;sFACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gFAAC,MAAK;0FACT,cAAA,8OAAC;oFAAE,WAAU;;sGACX,8OAAC;4FAAE,WAAU;;;;;;wFAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAWvD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;;4CAAa;4CACX,IAAI,OAAO,WAAW;4CAAG;4CACX;0DAC3B,8OAAC;gDAAE,MAAK;0DAAiD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW3E;uCACe", "debugId": null}}, {"offset": {"line": 1082, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kinco-day-care-kindergarten-react-template-30-06-2025/Kinco/React%20Template/kinco-modern/src/lib/components/useClickOutside.js"], "sourcesContent": ["import { useEffect, useRef } from \"react\";\r\n\r\nlet useClickOutside = (handler) => {\r\n  let domNode = useRef();\r\n\r\n  useEffect(() => {\r\n    let maybeHandler = (event) => {\r\n      if (!domNode.current.contains(event.target)) {\r\n        handler();\r\n      }\r\n    };\r\n\r\n    document.addEventListener(\"mousedown\", maybeHandler);\r\n\r\n    return () => {\r\n      document.removeEventListener(\"mousedown\", maybeHandler);\r\n    };\r\n  });\r\n\r\n  return domNode;\r\n};\r\nexport default useClickOutside;\r\n"], "names": [], "mappings": ";;;AAAA;;AAEA,IAAI,kBAAkB,CAAC;IACrB,IAAI,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IAEnB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,eAAe,CAAC;YAClB,IAAI,CAAC,QAAQ,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAG;gBAC3C;YACF;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QAEvC,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF;IAEA,OAAO;AACT;uCACe", "debugId": null}}, {"offset": {"line": 1109, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kinco-day-care-kindergarten-react-template-30-06-2025/Kinco/React%20Template/kinco-modern/src/lib/components/HeaderSearchForm.js"], "sourcesContent": ["import useClickOutside from \"./useClickOutside\";\r\n\r\nconst HeaderSearchForm = ({ show, handleClose }) => {\r\n  let domNode = useClickOutside(() => {\r\n    handleClose(false);\r\n  });\r\n  return (\r\n    <form\r\n      role=\"search\"\r\n      method=\"get\"\r\n      className={`header-search-form ${show ? \"show\" : \"\"}`}\r\n      action=\"#\"\r\n      ref={domNode}\r\n      onSubmit={(e) => {\r\n        e.preventDefault();\r\n        handleClose();\r\n      }}\r\n    >\r\n      <input\r\n        type=\"text\"\r\n        defaultValue=\"\"\r\n        name=\"s\"\r\n        className=\"header-search-field\"\r\n        placeholder=\"Search...\"\r\n      />\r\n      <button type=\"submit\" className=\"header-search-submit\" title=\"Search\">\r\n        <i className=\"fa fa-search\" />\r\n      </button>\r\n    </form>\r\n  );\r\n};\r\nexport default HeaderSearchForm;\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,MAAM,mBAAmB,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE;IAC7C,IAAI,UAAU,CAAA,GAAA,2IAAA,CAAA,UAAe,AAAD,EAAE;QAC5B,YAAY;IACd;IACA,qBACE,8OAAC;QACC,MAAK;QACL,QAAO;QACP,WAAW,CAAC,mBAAmB,EAAE,OAAO,SAAS,IAAI;QACrD,QAAO;QACP,KAAK;QACL,UAAU,CAAC;YACT,EAAE,cAAc;YAChB;QACF;;0BAEA,8OAAC;gBACC,MAAK;gBACL,cAAa;gBACb,MAAK;gBACL,WAAU;gBACV,aAAY;;;;;;0BAEd,8OAAC;gBAAO,MAAK;gBAAS,WAAU;gBAAuB,OAAM;0BAC3D,cAAA,8OAAC;oBAAE,WAAU;;;;;;;;;;;;;;;;;AAIrB;uCACe", "debugId": null}}, {"offset": {"line": 1172, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kinco-day-care-kindergarten-react-template-30-06-2025/Kinco/React%20Template/kinco-modern/src/lib/components/Sidebar.js"], "sourcesContent": ["import Link from \"next/link\";\r\nconst Sidebar = ({ show, close }) => {\r\n  return (\r\n    <div id=\"sidebar2\" className={`side-menu__block ${show ? \"active\" : \"\"}`}>\r\n      <div\r\n        className=\"side-menu__block-overlay custom-cursor__overlay\"\r\n        onClick={close}\r\n      />\r\n      <div className=\"inner-sidebar side-menu__block-inner fl-st-1\">\r\n        <div className=\"side-menu__top justify-content-end\">\r\n          <a\r\n            href=\"#\"\r\n            className=\"side-menu__toggler side-menu__close-btn\"\r\n            onClick={close}\r\n          >\r\n            <img src=\"assets/images/common/close.png\" alt=\"images\" />\r\n          </a>\r\n        </div>\r\n        <div className=\"wrap\">\r\n          <div className=\"widget widget-quote\">\r\n            <div className=\"box-feature\">\r\n              <div className=\"inner\">\r\n                <img src=\"assets/images/post/post-quotes2.jpg\" alt=\"Image\" />\r\n                <div className=\"box-icon jus-ali-ct\">\r\n                  <i className=\"far fa-quote-right\" />\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"box-content\">\r\n              <h5 className=\"author clr-pri-2\">Patrick D. Smith</h5>\r\n              <p className=\"wrap f-rubik\">\r\n                Sit amet consectetur adipiscing elit sed do eiusmod tempor\r\n                didunt ut labore et dolore magna\r\n              </p>\r\n            </div>\r\n          </div>\r\n          <div className=\"widget widget-search st-2\">\r\n            <h4 className=\"title-widget fl-ctm-1\">\r\n              Search\r\n              <span className=\"ctm-inner\" />\r\n            </h4>\r\n            <div className=\"form-search-widget\">\r\n              <form action=\"#\" onSubmit={(e) => e.preventDefault()}>\r\n                <input type=\"text\" placeholder=\"Search Here\" />\r\n                <button>\r\n                  <i className=\"fas fa-search\" />\r\n                </button>\r\n              </form>\r\n            </div>\r\n          </div>\r\n          <div className=\"widget widget-category st-2\">\r\n            <h4 className=\"title-widget fl-ctm-1\">\r\n              category\r\n              <span className=\"ctm-inner\" />\r\n            </h4>\r\n            <div className=\"list-category\">\r\n              <ul>\r\n                <li className=\"fx\">\r\n                  <span className=\"st wd-ctm\">Arts &amp; Drawing</span>\r\n                  <span className=\"st\">05</span>\r\n                </li>\r\n                <li className=\"fx\">\r\n                  <span className=\"st wd-ctm\">Basic Language</span>\r\n                  <span className=\"st\">02</span>\r\n                </li>\r\n                <li className=\"fx\">\r\n                  <span className=\"st wd-ctm\">Graphics Design</span>\r\n                  <span className=\"st\">07</span>\r\n                </li>\r\n                <li className=\"fx\">\r\n                  <span className=\"st wd-ctm\">Web Development</span>\r\n                  <span className=\"st\">04</span>\r\n                </li>\r\n                <li className=\"fx\">\r\n                  <span className=\"st wd-ctm\">Lifestyle</span>\r\n                  <span className=\"st\">06</span>\r\n                </li>\r\n                <li className=\"fx\">\r\n                  <span className=\"st wd-ctm\">GYM &amp; Gaming</span>\r\n                  <span className=\"st\">05</span>\r\n                </li>\r\n                <li className=\"fx\">\r\n                  <span className=\"st wd-ctm\">Events &amp; Party</span>\r\n                  <span className=\"st\">05</span>\r\n                </li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n          <div className=\"widget widget-news st-2\">\r\n            <h4 className=\"title-widget fl-ctm-1\">\r\n              recent news\r\n              <span className=\"ctm-inner\" />\r\n            </h4>\r\n            <ul className=\"list-news\">\r\n              <li className=\"fx\">\r\n                <img\r\n                  src=\"assets/images/thumbnails/widget1.jpg\"\r\n                  alt=\"Image\"\r\n                  className=\"feature\"\r\n                />\r\n                <ul className=\"box-content\">\r\n                  <li>\r\n                    <h6 className=\"title\">\r\n                      <Link href=\"/blog-single\">\r\n                        <a>Bake Layers Accesilit Testing Supporte</a>\r\n                      </Link>\r\n                    </h6>\r\n                  </li>\r\n                  <li>\r\n                    <Link href=\"/blog-single\">\r\n                      <a className=\"fx meta-news clr-pri-4\">\r\n                        <i className=\"far fa-calendar-alt\" />\r\n                        <span className=\"f-rubik\">25 nov 2021</span>\r\n                      </a>\r\n                    </Link>\r\n                  </li>\r\n                </ul>\r\n              </li>\r\n              <li className=\"fx\">\r\n                <img\r\n                  src=\"assets/images/thumbnails/widget2.jpg\"\r\n                  alt=\"Image\"\r\n                  className=\"feature\"\r\n                />\r\n                <ul className=\"box-content\">\r\n                  <li>\r\n                    <h6 className=\"title\">\r\n                      <Link href=\"/blog-single\">\r\n                        <a>Bake Layers Accesilit Testin Supporte</a>\r\n                      </Link>\r\n                    </h6>\r\n                  </li>\r\n                  <li>\r\n                    <Link href=\"/blog-single\">\r\n                      <a className=\"fx meta-news clr-pri-4\">\r\n                        <i className=\"far fa-calendar-alt\" />\r\n                        <span className=\"f-rubik\">25 nov 2021</span>\r\n                      </a>\r\n                    </Link>\r\n                  </li>\r\n                </ul>\r\n              </li>\r\n              <li className=\"fx\">\r\n                <img\r\n                  src=\"assets/images/thumbnails/widget3.jpg\"\r\n                  alt=\"Image\"\r\n                  className=\"feature\"\r\n                />\r\n                <ul className=\"box-content\">\r\n                  <li>\r\n                    <h6 className=\"title\">\r\n                      <Link href=\"/blog-single\">\r\n                        <a>Bake Layers Accesilit Testin Supporte</a>\r\n                      </Link>\r\n                    </h6>\r\n                  </li>\r\n                  <li>\r\n                    <Link href=\"/blog-single\">\r\n                      <a className=\"fx meta-news clr-pri-4\">\r\n                        <i className=\"far fa-calendar-alt\" />\r\n                        <span className=\"f-rubik\">25 nov 2021</span>\r\n                      </a>\r\n                    </Link>\r\n                  </li>\r\n                </ul>\r\n              </li>\r\n              <li className=\"fx\">\r\n                <img\r\n                  src=\"assets/images/thumbnails/widget4.jpg\"\r\n                  alt=\"Image\"\r\n                  className=\"feature\"\r\n                />\r\n                <ul className=\"box-content\">\r\n                  <li>\r\n                    <h6 className=\"title\">\r\n                      <Link href=\"/blog-single\">\r\n                        <a>Bake Layers Accesilit Testin Supporte</a>\r\n                      </Link>\r\n                    </h6>\r\n                  </li>\r\n                  <li>\r\n                    <Link href=\"/blog-single\">\r\n                      <a className=\"fx meta-news clr-pri-4\">\r\n                        <i className=\"far fa-calendar-alt\" />\r\n                        <span className=\"f-rubik\">25 nov 2021</span>\r\n                      </a>\r\n                    </Link>\r\n                  </li>\r\n                </ul>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n          <div className=\"widget widget-tag st-2\">\r\n            <h4 className=\"title-widget fl-ctm-1\">\r\n              best tags\r\n              <span className=\"ctm-inner\" />\r\n            </h4>\r\n            <ul className=\"list-tag\">\r\n              <li>\r\n                <Link href=\"/shop-details\">\r\n                  <a className=\"f-rubik active\">Technology</a>\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link href=\"/shop-details\">\r\n                  <a className=\"f-rubik active\">service</a>\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link href=\"/shop-details\">\r\n                  <a className=\"f-rubik active\">team</a>\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link href=\"/shop-details\">\r\n                  <a className=\"f-rubik active\">solutions</a>\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link href=\"/shop-details\">\r\n                  <a className=\"f-rubik active\">consultancy</a>\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link href=\"/shop-details\">\r\n                  <a className=\"f-rubik active\">It Company</a>\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link href=\"/shop-details\">\r\n                  <a className=\"f-rubik active\">agency</a>\r\n                </Link>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n          <div className=\"widget widget-gallery st-2\">\r\n            <h4 className=\"title-widget fl-ctm-1\">\r\n              photo gallery\r\n              <span className=\"ctm-inner\" />\r\n            </h4>\r\n            <div className=\"list-gallery fx\">\r\n              <div className=\"box-photo\">\r\n                <div className=\"overlay fx\">\r\n                  <i className=\"fal fa-plus\" />\r\n                </div>\r\n                <img src=\"assets/images/thumbnails/widget5.jpg\" alt=\"Image\" />\r\n              </div>\r\n              <div className=\"box-photo active\">\r\n                <div className=\"overlay fx\">\r\n                  <i className=\"fal fa-plus\" />\r\n                </div>\r\n                <img src=\"assets/images/thumbnails/widget1.jpg\" alt=\"Image\" />\r\n              </div>\r\n              <div className=\"box-photo\">\r\n                <div className=\"overlay fx\">\r\n                  <i className=\"fal fa-plus\" />\r\n                </div>\r\n                <img src=\"assets/images/thumbnails/widget6.jpg\" alt=\"Image\" />\r\n              </div>\r\n              <div className=\"box-photo\">\r\n                <div className=\"overlay fx\">\r\n                  <i className=\"fal fa-plus\" />\r\n                </div>\r\n                <img src=\"assets/images/thumbnails/widget7.jpg\" alt=\"Image\" />\r\n              </div>\r\n              <div className=\"box-photo\">\r\n                <div className=\"overlay fx\">\r\n                  <i className=\"fal fa-plus\" />\r\n                </div>\r\n                <img src=\"assets/images/thumbnails/widget3.jpg\" alt=\"Image\" />\r\n              </div>\r\n              <div className=\"box-photo\">\r\n                <div className=\"overlay fx\">\r\n                  <i className=\"fal fa-plus\" />\r\n                </div>\r\n                <img src=\"assets/images/thumbnails/widget8.jpg\" alt=\"Image\" />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      {/*/inner-sidebar*/}\r\n    </div>\r\n  );\r\n};\r\nexport default Sidebar;\r\n"], "names": [], "mappings": ";;;;AAAA;;;AACA,MAAM,UAAU,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE;IAC9B,qBACE,8OAAC;QAAI,IAAG;QAAW,WAAW,CAAC,iBAAiB,EAAE,OAAO,WAAW,IAAI;;0BACtE,8OAAC;gBACC,WAAU;gBACV,SAAS;;;;;;0BAEX,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,MAAK;4BACL,WAAU;4BACV,SAAS;sCAET,cAAA,8OAAC;gCAAI,KAAI;gCAAiC,KAAI;;;;;;;;;;;;;;;;kCAGlD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,KAAI;oDAAsC,KAAI;;;;;;8DACnD,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;kDAInB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAmB;;;;;;0DACjC,8OAAC;gDAAE,WAAU;0DAAe;;;;;;;;;;;;;;;;;;0CAMhC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;;4CAAwB;0DAEpC,8OAAC;gDAAK,WAAU;;;;;;;;;;;;kDAElB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,QAAO;4CAAI,UAAU,CAAC,IAAM,EAAE,cAAc;;8DAChD,8OAAC;oDAAM,MAAK;oDAAO,aAAY;;;;;;8DAC/B,8OAAC;8DACC,cAAA,8OAAC;wDAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAKrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;;4CAAwB;0DAEpC,8OAAC;gDAAK,WAAU;;;;;;;;;;;;kDAElB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAK,WAAU;sEAAY;;;;;;sEAC5B,8OAAC;4DAAK,WAAU;sEAAK;;;;;;;;;;;;8DAEvB,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAK,WAAU;sEAAY;;;;;;sEAC5B,8OAAC;4DAAK,WAAU;sEAAK;;;;;;;;;;;;8DAEvB,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAK,WAAU;sEAAY;;;;;;sEAC5B,8OAAC;4DAAK,WAAU;sEAAK;;;;;;;;;;;;8DAEvB,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAK,WAAU;sEAAY;;;;;;sEAC5B,8OAAC;4DAAK,WAAU;sEAAK;;;;;;;;;;;;8DAEvB,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAK,WAAU;sEAAY;;;;;;sEAC5B,8OAAC;4DAAK,WAAU;sEAAK;;;;;;;;;;;;8DAEvB,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAK,WAAU;sEAAY;;;;;;sEAC5B,8OAAC;4DAAK,WAAU;sEAAK;;;;;;;;;;;;8DAEvB,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAK,WAAU;sEAAY;;;;;;sEAC5B,8OAAC;4DAAK,WAAU;sEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAK7B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;;4CAAwB;0DAEpC,8OAAC;gDAAK,WAAU;;;;;;;;;;;;kDAElB,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDACC,KAAI;wDACJ,KAAI;wDACJ,WAAU;;;;;;kEAEZ,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;0EACC,cAAA,8OAAC;oEAAG,WAAU;8EACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;wEAAC,MAAK;kFACT,cAAA,8OAAC;sFAAE;;;;;;;;;;;;;;;;;;;;;0EAIT,8OAAC;0EACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oEAAC,MAAK;8EACT,cAAA,8OAAC;wEAAE,WAAU;;0FACX,8OAAC;gFAAE,WAAU;;;;;;0FACb,8OAAC;gFAAK,WAAU;0FAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAMpC,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDACC,KAAI;wDACJ,KAAI;wDACJ,WAAU;;;;;;kEAEZ,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;0EACC,cAAA,8OAAC;oEAAG,WAAU;8EACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;wEAAC,MAAK;kFACT,cAAA,8OAAC;sFAAE;;;;;;;;;;;;;;;;;;;;;0EAIT,8OAAC;0EACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oEAAC,MAAK;8EACT,cAAA,8OAAC;wEAAE,WAAU;;0FACX,8OAAC;gFAAE,WAAU;;;;;;0FACb,8OAAC;gFAAK,WAAU;0FAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAMpC,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDACC,KAAI;wDACJ,KAAI;wDACJ,WAAU;;;;;;kEAEZ,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;0EACC,cAAA,8OAAC;oEAAG,WAAU;8EACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;wEAAC,MAAK;kFACT,cAAA,8OAAC;sFAAE;;;;;;;;;;;;;;;;;;;;;0EAIT,8OAAC;0EACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oEAAC,MAAK;8EACT,cAAA,8OAAC;wEAAE,WAAU;;0FACX,8OAAC;gFAAE,WAAU;;;;;;0FACb,8OAAC;gFAAK,WAAU;0FAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAMpC,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDACC,KAAI;wDACJ,KAAI;wDACJ,WAAU;;;;;;kEAEZ,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;0EACC,cAAA,8OAAC;oEAAG,WAAU;8EACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;wEAAC,MAAK;kFACT,cAAA,8OAAC;sFAAE;;;;;;;;;;;;;;;;;;;;;0EAIT,8OAAC;0EACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oEAAC,MAAK;8EACT,cAAA,8OAAC;wEAAE,WAAU;;0FACX,8OAAC;gFAAE,WAAU;;;;;;0FACb,8OAAC;gFAAK,WAAU;0FAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQxC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;;4CAAwB;0DAEpC,8OAAC;gDAAK,WAAU;;;;;;;;;;;;kDAElB,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;8DACT,cAAA,8OAAC;wDAAE,WAAU;kEAAiB;;;;;;;;;;;;;;;;0DAGlC,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;8DACT,cAAA,8OAAC;wDAAE,WAAU;kEAAiB;;;;;;;;;;;;;;;;0DAGlC,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;8DACT,cAAA,8OAAC;wDAAE,WAAU;kEAAiB;;;;;;;;;;;;;;;;0DAGlC,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;8DACT,cAAA,8OAAC;wDAAE,WAAU;kEAAiB;;;;;;;;;;;;;;;;0DAGlC,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;8DACT,cAAA,8OAAC;wDAAE,WAAU;kEAAiB;;;;;;;;;;;;;;;;0DAGlC,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;8DACT,cAAA,8OAAC;wDAAE,WAAU;kEAAiB;;;;;;;;;;;;;;;;0DAGlC,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;8DACT,cAAA,8OAAC;wDAAE,WAAU;kEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAKtC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;;4CAAwB;0DAEpC,8OAAC;gDAAK,WAAU;;;;;;;;;;;;kDAElB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAE,WAAU;;;;;;;;;;;kEAEf,8OAAC;wDAAI,KAAI;wDAAuC,KAAI;;;;;;;;;;;;0DAEtD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAE,WAAU;;;;;;;;;;;kEAEf,8OAAC;wDAAI,KAAI;wDAAuC,KAAI;;;;;;;;;;;;0DAEtD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAE,WAAU;;;;;;;;;;;kEAEf,8OAAC;wDAAI,KAAI;wDAAuC,KAAI;;;;;;;;;;;;0DAEtD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAE,WAAU;;;;;;;;;;;kEAEf,8OAAC;wDAAI,KAAI;wDAAuC,KAAI;;;;;;;;;;;;0DAEtD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAE,WAAU;;;;;;;;;;;kEAEf,8OAAC;wDAAI,KAAI;wDAAuC,KAAI;;;;;;;;;;;;0DAEtD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAE,WAAU;;;;;;;;;;;kEAEf,8OAAC;wDAAI,KAAI;wDAAuC,KAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpE;uCACe", "debugId": null}}, {"offset": {"line": 2402, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kinco-day-care-kindergarten-react-template-30-06-2025/Kinco/React%20Template/kinco-modern/src/lib/layouts/header/Menus.js"], "sourcesContent": ["import Link from \"next/link\";\r\nimport { Fragment } from \"react\";\r\n\r\nexport const Home = () => (\r\n  <Fragment>\r\n    <li>\r\n      <Link href=\"/\">Home 01</Link>\r\n    </li>\r\n    <li>\r\n      <Link href=\"/home2\">Home 02</Link>\r\n    </li>\r\n  </Fragment>\r\n);\r\nexport const Pages = () => (\r\n  <Fragment>\r\n    <li>\r\n      <Link href=\"/calendar\">Academic Calendar</Link>\r\n    </li>\r\n    <li>\r\n      <Link href=\"/testimonials\">testimonials</Link>\r\n    </li>\r\n    <li>\r\n      <Link href=\"/time-table\">Time Table</Link>\r\n    </li>\r\n    <li>\r\n      <Link href=\"/gallery\">gallery</Link>\r\n    </li>\r\n    <li>\r\n      <Link href=\"/pricing\">pricing</Link>\r\n    </li>\r\n    <li>\r\n      <Link href=\"/faq\">Faq</Link>\r\n    </li>\r\n  </Fragment>\r\n);\r\nexport const Teacher = () => (\r\n  <Fragment>\r\n    <li>\r\n      <Link href=\"/teacher\">Teacher</Link>\r\n    </li>\r\n    <li>\r\n      <Link href=\"/teacher-details\">Teachers Details</Link>\r\n    </li>\r\n  </Fragment>\r\n);\r\nexport const Classes = () => (\r\n  <Fragment>\r\n    <li>\r\n      <Link href=\"/classes\">Classes</Link>\r\n    </li>\r\n    <li>\r\n      <Link href=\"/classe-details\">Classes Details</Link>\r\n    </li>\r\n  </Fragment>\r\n);\r\nexport const Event = () => (\r\n  <Fragment>\r\n    <li>\r\n      <Link href=\"/events\">Event</Link>\r\n    </li>\r\n    <li>\r\n      <Link href=\"/event2\">Event 2</Link>\r\n    </li>\r\n    <li>\r\n      <Link href=\"/event-details\">Events Details</Link>\r\n    </li>\r\n  </Fragment>\r\n);\r\nexport const Program = () => (\r\n  <Fragment>\r\n    <li>\r\n      <Link href=\"/program\">Program</Link>\r\n    </li>\r\n    <li>\r\n      <Link href=\"/program-details\">program details</Link>\r\n    </li>\r\n  </Fragment>\r\n);\r\nexport const Blog = () => (\r\n  <Fragment>\r\n    <li>\r\n      <Link href=\"/blog-grid\">blog grid</Link>\r\n    </li>\r\n    <li>\r\n      <Link href=\"/blog-list\">blog list</Link>\r\n    </li>\r\n    <li>\r\n      <Link href=\"/blog-single\">blog single</Link>\r\n    </li>\r\n  </Fragment>\r\n);\r\nexport const Shop = () => (\r\n  <Fragment>\r\n    <li>\r\n      <Link href=\"/shop\">Shop</Link>\r\n    </li>\r\n    <li>\r\n      <Link href=\"/shop-details\">Shop Details</Link>\r\n    </li>\r\n  </Fragment>\r\n);\r\nexport const About = () => <Link href=\"/about\">About</Link>;\r\nexport const Contact = () => <Link href=\"/contact\">CONTACTS</Link>;\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AACA;;;;AAEO,MAAM,OAAO,kBAClB,8OAAC,qMAAA,CAAA,WAAQ;;0BACP,8OAAC;0BACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oBAAC,MAAK;8BAAI;;;;;;;;;;;0BAEjB,8OAAC;0BACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oBAAC,MAAK;8BAAS;;;;;;;;;;;;;;;;;AAInB,MAAM,QAAQ,kBACnB,8OAAC,qMAAA,CAAA,WAAQ;;0BACP,8OAAC;0BACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oBAAC,MAAK;8BAAY;;;;;;;;;;;0BAEzB,8OAAC;0BACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oBAAC,MAAK;8BAAgB;;;;;;;;;;;0BAE7B,8OAAC;0BACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oBAAC,MAAK;8BAAc;;;;;;;;;;;0BAE3B,8OAAC;0BACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oBAAC,MAAK;8BAAW;;;;;;;;;;;0BAExB,8OAAC;0BACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oBAAC,MAAK;8BAAW;;;;;;;;;;;0BAExB,8OAAC;0BACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oBAAC,MAAK;8BAAO;;;;;;;;;;;;;;;;;AAIjB,MAAM,UAAU,kBACrB,8OAAC,qMAAA,CAAA,WAAQ;;0BACP,8OAAC;0BACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oBAAC,MAAK;8BAAW;;;;;;;;;;;0BAExB,8OAAC;0BACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oBAAC,MAAK;8BAAmB;;;;;;;;;;;;;;;;;AAI7B,MAAM,UAAU,kBACrB,8OAAC,qMAAA,CAAA,WAAQ;;0BACP,8OAAC;0BACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oBAAC,MAAK;8BAAW;;;;;;;;;;;0BAExB,8OAAC;0BACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oBAAC,MAAK;8BAAkB;;;;;;;;;;;;;;;;;AAI5B,MAAM,QAAQ,kBACnB,8OAAC,qMAAA,CAAA,WAAQ;;0BACP,8OAAC;0BACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oBAAC,MAAK;8BAAU;;;;;;;;;;;0BAEvB,8OAAC;0BACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oBAAC,MAAK;8BAAU;;;;;;;;;;;0BAEvB,8OAAC;0BACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oBAAC,MAAK;8BAAiB;;;;;;;;;;;;;;;;;AAI3B,MAAM,UAAU,kBACrB,8OAAC,qMAAA,CAAA,WAAQ;;0BACP,8OAAC;0BACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oBAAC,MAAK;8BAAW;;;;;;;;;;;0BAExB,8OAAC;0BACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oBAAC,MAAK;8BAAmB;;;;;;;;;;;;;;;;;AAI7B,MAAM,OAAO,kBAClB,8OAAC,qMAAA,CAAA,WAAQ;;0BACP,8OAAC;0BACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oBAAC,MAAK;8BAAa;;;;;;;;;;;0BAE1B,8OAAC;0BACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oBAAC,MAAK;8BAAa;;;;;;;;;;;0BAE1B,8OAAC;0BACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oBAAC,MAAK;8BAAe;;;;;;;;;;;;;;;;;AAIzB,MAAM,OAAO,kBAClB,8OAAC,qMAAA,CAAA,WAAQ;;0BACP,8OAAC;0BACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oBAAC,MAAK;8BAAQ;;;;;;;;;;;0BAErB,8OAAC;0BACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oBAAC,MAAK;8BAAgB;;;;;;;;;;;;;;;;;AAI1B,MAAM,QAAQ,kBAAM,8OAAC,4JAAA,CAAA,UAAI;QAAC,MAAK;kBAAS;;;;;;AACxC,MAAM,UAAU,kBAAM,8OAAC,4JAAA,CAAA,UAAI;QAAC,MAAK;kBAAW", "debugId": null}}, {"offset": {"line": 2814, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kinco-day-care-kindergarten-react-template-30-06-2025/Kinco/React%20Template/kinco-modern/src/lib/layouts/header/Header.js"], "sourcesContent": ["import Link from \"next/link\";\r\nimport { useEffect, useState } from \"react\";\r\nimport HeaderSearchForm from \"../../components/HeaderSearchForm\";\r\nimport Sidebar from \"../../components/Sidebar\";\r\nimport {\r\n  About,\r\n  Blog,\r\n  Classes,\r\n  Contact,\r\n  Event,\r\n  Home,\r\n  Pages,\r\n  Program,\r\n  Shop,\r\n  Teacher,\r\n} from \"./Menus\";\r\n\r\nconst Header = () => {\r\n  useEffect(() => {\r\n    document.querySelector(\"body\").className =\r\n      \"counter-scroll header-fixed inner-page\";\r\n  }, []);\r\n  const [form, setForm] = useState(false);\r\n  const [sidebarToggle, setSidebarToggle] = useState(false);\r\n\r\n  return (\r\n    <header id=\"header\" className=\"d-none d-xl-block\">\r\n      <div className=\"top-bar\">\r\n        <div className=\"inner jus-ct\">\r\n          <p className=\"clr-pri-1\">\r\n            Working Jours : Sun - Friday, 08:00 am - 05:00 pm\r\n          </p>\r\n        </div>\r\n      </div>\r\n      <div id=\"site-header\" className=\"\">\r\n        <div className=\"container-fluid\">\r\n          <div className=\"row\">\r\n            <div className=\"col-md-12\">\r\n              <div className=\"site-header-inner fx\">\r\n                <div id=\"site-logo\" className=\"clearfix\">\r\n                  <Link href=\"/\">\r\n                    <a className=\"logo\">\r\n                      <img src=\"assets/images/logo/logodark.png\" alt=\"Kinco\" />\r\n                    </a>\r\n                  </Link>\r\n                </div>\r\n                <div className=\"btn-menu\">\r\n                  <span />\r\n                </div>\r\n                <div className=\"nav-wrap\">\r\n                  <nav id=\"mainnav\" className=\"mainnav st-2\">\r\n                    <ul className=\"menu\">\r\n                      <li className=\"menu-item-has-children\">\r\n                        <a href=\"#\">HOME</a>\r\n                        <ul className=\"sub-menu\">\r\n                          <Home />\r\n                        </ul>\r\n                      </li>\r\n                      <li className=\"menu-item\">\r\n                        <About />\r\n                      </li>\r\n                      <li className=\"menu-item-has-children\">\r\n                        <a>PAGES</a>\r\n                        <ul className=\"sub-menu\">\r\n                          <li className=\"inner-menu-item\">\r\n                            <a href=\"#\">Teachers</a>\r\n                            <ul className=\"sub-menu\">\r\n                              <Teacher />\r\n                            </ul>\r\n                          </li>\r\n                          <li className=\"inner-menu-item \">\r\n                            <a href=\"#\">Classes</a>\r\n                            <ul className=\"sub-menu\">\r\n                              <Classes />\r\n                            </ul>\r\n                          </li>\r\n                          <li className=\"inner-menu-item\">\r\n                            <a href=\"#\">Events</a>\r\n                            <ul className=\"sub-menu\">\r\n                              <Event />\r\n                            </ul>\r\n                          </li>\r\n                          <Pages />\r\n                        </ul>\r\n                      </li>\r\n                      <li className=\"menu-item-has-children\">\r\n                        <a>Programs</a>\r\n                        <ul className=\"sub-menu\">\r\n                          <Program />\r\n                        </ul>\r\n                      </li>\r\n                      <li className=\"menu-item-has-children\">\r\n                        <a>BLOG</a>\r\n                        <ul className=\"sub-menu\">\r\n                          <Blog />\r\n                        </ul>\r\n                      </li>\r\n                      <li className=\"menu-item-has-children\">\r\n                        <a>SHOP</a>\r\n                        <ul className=\"sub-menu\">\r\n                          <Shop />\r\n                        </ul>\r\n                      </li>\r\n                      <li className=\"inner\">\r\n                        <Contact />\r\n                      </li>\r\n                    </ul>\r\n                    {/* /.menu */}\r\n                  </nav>\r\n                </div>\r\n                <div className=\"header-right fx\">\r\n                  <div id=\"header-search\">\r\n                    <a\r\n                      href=\"#\"\r\n                      className=\"search-box header-search-icon\"\r\n                      onClick={() => setForm(!form)}\r\n                    >\r\n                      <i className=\"far fa-search\" />\r\n                    </a>\r\n                    <HeaderSearchForm\r\n                      show={form}\r\n                      handleClose={() => setForm(false)}\r\n                    />\r\n                  </div>\r\n                  <div className=\"header-contact fx\">\r\n                    <a\r\n                      href=\"#\"\r\n                      className=\"menu-bar-right header-menu\"\r\n                      onClick={() => setSidebarToggle(true)}\r\n                    >\r\n                      <svg\r\n                        data-name=\"Hero Area\"\r\n                        xmlns=\"http://www.w3.org/2000/svg\"\r\n                        width={58}\r\n                        height={58}\r\n                        viewBox=\"0 0 58 58\"\r\n                      >\r\n                        <defs>\r\n                          <style\r\n                            dangerouslySetInnerHTML={{\r\n                              __html:\r\n                                \"\\n                                                        .cls-1 {\\n                                                        fill: #b250fe;\\n                                                        }\\n                                                \\n                                                        .cls-1, .cls-2 {\\n                                                        fill-rule: evenodd;\\n                                                        }\\n                                                \\n                                                        .cls-2 {\\n                                                        fill: #fff;\\n                                                        }\\n                                                    \",\r\n                            }}\r\n                          />\r\n                        </defs>\r\n                        <g id=\"Menu_Area2\" data-name=\"Menu Area\">\r\n                          <g id=\"Menu_bar\" data-name=\"Menu bar\">\r\n                            <path\r\n                              id=\"Bg\"\r\n                              className=\"cls-1\"\r\n                              d=\"M7.853,2.283c14.9-3.89,29.969-1.4,43.467.819a7.923,7.923,0,0,1,5.735,5.422c3.111,14.141-.428,28.636-1.166,42.981a5.157,5.157,0,0,1-4.773,4.875c-13.49.568-23.463,3.285-41.787,0.9C5.948,56.807,2.348,54.2,1.9,51.7-0.683,37.877.2,23.508,2.194,8.757a8.71,8.71,0,0,1,5.66-6.473\"\r\n                            />\r\n                            <path\r\n                              id=\"Bar\"\r\n                              className=\"cls-2\"\r\n                              d=\"M16,17H42a2,2,0,0,1,0,4H16A2,2,0,0,1,16,17Zm0,10H42a2,2,0,0,1,0,4H16A2,2,0,0,1,16,27Zm0,10H42a2,2,0,0,1,0,4H16A2,2,0,0,1,16,37Z\"\r\n                            />\r\n                          </g>\r\n                        </g>\r\n                      </svg>\r\n                    </a>\r\n                    <div className=\"inner-contact fx\">\r\n                      <svg\r\n                        id=\"Hero_Area2\"\r\n                        data-name=\"Hero Area\"\r\n                        xmlns=\"http://www.w3.org/2000/svg\"\r\n                        xmlnsXlink=\"http://www.w3.org/1999/xlink\"\r\n                        width={49}\r\n                        height={47}\r\n                        viewBox=\"0 0 49 47\"\r\n                      >\r\n                        <g data-name=\"Menu Area\">\r\n                          <g>\r\n                            <image\r\n                              id=\"helpline2\"\r\n                              width={49}\r\n                              height={47}\r\n                              xlinkHref=\"data:img/png;base64,iVBORw0KGgoAAAANSUhEUgAAADEAAAAvCAYAAABKQCL3AAAG10lEQVRogc2aCbCVYxjHf11JN5HKNUhj39eEm20sNylFwtiSfTDIMiPb2Mk2yJR1hoQKZatJRFnGTiJE1pTRlCRyQ0nMY37fzDefc+75jnvu1X/mzrnn297n/77P9v6/02LF+SfTTGgJ9AG6Ae2AVsCawHKgRcaEX4BlwCLgVWBSQya2bAb7Y4zBQC9gdeBDYDrwBfA58KNGV0muI7AlsBWwM3AssFAyd3tfs5KoBZ4HJgLnAi+VuH6en2+kjoWN+wMHAKOAeuA4YG5yQVXT2P4POgFvATcCx+QgUAzLnYhBwMGu3AfAoc1B4n7gNuCGCj5zPnAEcBLwFNCbJnSn8OO9gJ5N9PxngO7AWGCbpiJxDvBw5th5wObAH8DlBnOCAWatP4FhmeC9xmCP6y9OHZ8CvBlxUsqdugBneXP4ZA9g1RL3VAM/AVemjj1uYIc/twZmATt67kHgDs9Fqp0cs+u5iKm+Zqc9gE8llCAmY7NiK3GpwfgN8C3wM9BGUkOA980QhVAH/AB877nL9N3q1LXh06do/J7A+sASz50GXAt8BawBbJu6bxpwK3Bi6vu0LImO+ll74CZT2l+Za+rMOG/r84sy5zuZBhMcZYDjDAeB+4DZQD9guARi7EuAZ4ENgMOAI70vUvUqBva6mfF+z7rTfFNazPjIAgTQF3c1KYwqcH5eZtYnm1E2dubDwK2Nj+eAfUyddwFtdbdYhTFW+E6p1ews+QRR9ddPk5gAjNPvcca2KmBkgq4u9dGZ4y9qaI3fLwIWA18buLsA+zoB0z02HjjICn0gcDvQH9gJmAls6Pe1gVtSY4WNmyYk+sv0cAcZ56wsbIBE4GmzRxqRRVYzdrAH2lUXWgp8BmxvG4HGxzOm6gVB8B3/r3O1Ir4+BnbIZK5IOiOSBvBlBxnp0g52VpaXILGTqbRL5tquGrWWSaEpcJWZq0uVbFdIINLiJjLOU0O+AxZobBrv2RZ8rv9XGhdo6/Fo6Fmp9Bau9KX//55j4BqzSn2Bc7Hs69i5VgrtjZfj9YKPsHeK4H0hioZN2qAyBjzBVcgSjpQ4Q5ecWgECYeOd1qwa9yHTk5MtrcBTzL89DLh3Czwo9gNnGjMJBri0WUQVvtqimOAME0i9q/Sme4uoM79aB9o5KWH0diaA1azk882EE7KDJSS+c+YeS1XaLFqn0m/gdQ0ZnbluoC55ld93sQPoZsJYKpmoD7/qyn9oaLVGV6fITjYFLy62TC0N6mW6U+tMMUljqkWou8a0sWXIop8TsblVP6551Aqc4D4/V7e1aK0d9ZJaWszgQqiyKtc4K3UOXgjfWpiiTXjFpS6EgR67wRWutfkrhCVW+OjR5tgElkUgWYnlzm5sYp7ItMhZRM5/MtOhZjFD361ydpscVQZMrQOF397rEhfCBFuAPGgWAkhisq0wSiPvFpBQEgy3L8oWt/8VQWKEgYWqxJ+22G0KGBYB28GucqUiMcdgOsls0cHs0a3IPUPd0Kw0SLrY8ba4S6yMA+w2C2GE5b7ZpMNSSEjca6rtZ7obZ3oshgvNZu1WJhJYRW/Oed9bVuoXmsiuspAmMcmsNDTnA/obPw810oZIIBs3ptvN7hm6uwe4B/gkx/21KhvhgleUOXZvE8QWttiLHHu0Ek9uZEl8Y6c6o4FakcZCW5a56kh9cw4804buIZWTBe6faxXZrpdcLhR7P/GwD6nN+Zw17WiXKmHOLHJdO6WeWa5Esao+zna8WwFJ6F8opgAOsDK/kpPEYpWPN9ysDLalzuJOO+ZeJdqSvna0uZT0hmTMzrrKazmJBM629d5SdWKEogGubGf373nQ1YnYvTEkVvigjYBHyiAyVbHsbPfYsad4wJ4sXGQ/j+fBrWoADaKUovGbblWvFrRbGWQm+ddK5WOBEzLcND6kxP2hP51uW9QoEgnaKmIlhizJd9s/WKYkOSZ1rLqB60lJnW1NGg2inDdFPdWT5jSwq8uL30pc18qt8Ng8E1bu666eKtwfllETysUq1qioO3u7PagoCUyfvXWPYRU0vpWfByo2P2NL8i+JJov/+uJxourIseq4jUHYsK7xOdRdYy/F6nnpV72VJoHqRyJhLku9vsqL5LVBGH+PyngH3/700bZcLluJF4993K4+rwsU6mP2skudaY3Yzgx1lCrhJJ9zmMphvNNYL68BlXqPfZ0usK8Vvs7UuJYxdIjd7hT12VN1k7V9pfWSNWG2ck9N6tcFzUYC3WATg/J2i+NcRbmJEhui0d1tTQb564CQROMNU7QpUYeCcG401a9sItOEEhh9VKh6offGCkQLEm4TLUik0k09F9eEKJf+TUc+AH8DJu6XCnzb/8QAAAAASUVORK5CYII=\"\r\n                            />\r\n                          </g>\r\n                        </g>\r\n                      </svg>\r\n                      <ul>\r\n                        <li className=\"clr-pri-2\">Hotline</li>\r\n                        <li className=\"clr-pri-2\">+012 (345) 678</li>\r\n                      </ul>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <Sidebar show={sidebarToggle} close={() => setSidebarToggle(false)} />\r\n      </div>\r\n      <div />\r\n    </header>\r\n  );\r\n};\r\nexport default Header;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;;AAaA,MAAM,SAAS;IACb,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS,aAAa,CAAC,QAAQ,SAAS,GACtC;IACJ,GAAG,EAAE;IACL,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,qBACE,8OAAC;QAAO,IAAG;QAAS,WAAU;;0BAC5B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAAY;;;;;;;;;;;;;;;;0BAK7B,8OAAC;gBAAI,IAAG;gBAAc,WAAU;;kCAC9B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,IAAG;4CAAY,WAAU;sDAC5B,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;0DACT,cAAA,8OAAC;oDAAE,WAAU;8DACX,cAAA,8OAAC;wDAAI,KAAI;wDAAkC,KAAI;;;;;;;;;;;;;;;;;;;;;sDAIrD,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;;;;;;;;;;sDAEH,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,IAAG;gDAAU,WAAU;0DAC1B,cAAA,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEAAE,MAAK;8EAAI;;;;;;8EACZ,8OAAC;oEAAG,WAAU;8EACZ,cAAA,8OAAC,wIAAA,CAAA,OAAI;;;;;;;;;;;;;;;;sEAGT,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC,wIAAA,CAAA,QAAK;;;;;;;;;;sEAER,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;8EAAE;;;;;;8EACH,8OAAC;oEAAG,WAAU;;sFACZ,8OAAC;4EAAG,WAAU;;8FACZ,8OAAC;oFAAE,MAAK;8FAAI;;;;;;8FACZ,8OAAC;oFAAG,WAAU;8FACZ,cAAA,8OAAC,wIAAA,CAAA,UAAO;;;;;;;;;;;;;;;;sFAGZ,8OAAC;4EAAG,WAAU;;8FACZ,8OAAC;oFAAE,MAAK;8FAAI;;;;;;8FACZ,8OAAC;oFAAG,WAAU;8FACZ,cAAA,8OAAC,wIAAA,CAAA,UAAO;;;;;;;;;;;;;;;;sFAGZ,8OAAC;4EAAG,WAAU;;8FACZ,8OAAC;oFAAE,MAAK;8FAAI;;;;;;8FACZ,8OAAC;oFAAG,WAAU;8FACZ,cAAA,8OAAC,wIAAA,CAAA,QAAK;;;;;;;;;;;;;;;;sFAGV,8OAAC,wIAAA,CAAA,QAAK;;;;;;;;;;;;;;;;;sEAGV,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;8EAAE;;;;;;8EACH,8OAAC;oEAAG,WAAU;8EACZ,cAAA,8OAAC,wIAAA,CAAA,UAAO;;;;;;;;;;;;;;;;sEAGZ,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;8EAAE;;;;;;8EACH,8OAAC;oEAAG,WAAU;8EACZ,cAAA,8OAAC,wIAAA,CAAA,OAAI;;;;;;;;;;;;;;;;sEAGT,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;8EAAE;;;;;;8EACH,8OAAC;oEAAG,WAAU;8EACZ,cAAA,8OAAC,wIAAA,CAAA,OAAI;;;;;;;;;;;;;;;;sEAGT,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC,wIAAA,CAAA,UAAO;;;;;;;;;;;;;;;;;;;;;;;;;;sDAMhB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,IAAG;;sEACN,8OAAC;4DACC,MAAK;4DACL,WAAU;4DACV,SAAS,IAAM,QAAQ,CAAC;sEAExB,cAAA,8OAAC;gEAAE,WAAU;;;;;;;;;;;sEAEf,8OAAC,4IAAA,CAAA,UAAgB;4DACf,MAAM;4DACN,aAAa,IAAM,QAAQ;;;;;;;;;;;;8DAG/B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,MAAK;4DACL,WAAU;4DACV,SAAS,IAAM,iBAAiB;sEAEhC,cAAA,8OAAC;gEACC,aAAU;gEACV,OAAM;gEACN,OAAO;gEACP,QAAQ;gEACR,SAAQ;;kFAER,8OAAC;kFACC,cAAA,8OAAC;4EACC,yBAAyB;gFACvB,QACE;4EACJ;;;;;;;;;;;kFAGJ,8OAAC;wEAAE,IAAG;wEAAa,aAAU;kFAC3B,cAAA,8OAAC;4EAAE,IAAG;4EAAW,aAAU;;8FACzB,8OAAC;oFACC,IAAG;oFACH,WAAU;oFACV,GAAE;;;;;;8FAEJ,8OAAC;oFACC,IAAG;oFACH,WAAU;oFACV,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;sEAMZ,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEACC,IAAG;oEACH,aAAU;oEACV,OAAM;oEACN,YAAW;oEACX,OAAO;oEACP,QAAQ;oEACR,SAAQ;8EAER,cAAA,8OAAC;wEAAE,aAAU;kFACX,cAAA,8OAAC;sFACC,cAAA,8OAAC;gFACC,IAAG;gFACH,OAAO;gFACP,QAAQ;gFACR,WAAU;;;;;;;;;;;;;;;;;;;;;8EAKlB,8OAAC;;sFACC,8OAAC;4EAAG,WAAU;sFAAY;;;;;;sFAC1B,8OAAC;4EAAG,WAAU;sFAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS1C,8OAAC,mIAAA,CAAA,UAAO;wBAAC,MAAM;wBAAe,OAAO,IAAM,iBAAiB;;;;;;;;;;;;0BAE9D,8OAAC;;;;;;;;;;;AAGP;uCACe", "debugId": null}}, {"offset": {"line": 3441, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kinco-day-care-kindergarten-react-template-30-06-2025/Kinco/React%20Template/kinco-modern/src/lib/layouts/header/MobileMenu.js"], "sourcesContent": ["import Link from \"next/link\";\r\nimport { useState } from \"react\";\r\nimport {\r\n  About,\r\n  Blog,\r\n  Classes,\r\n  Contact,\r\n  Event,\r\n  Home,\r\n  Pages,\r\n  Program,\r\n  Shop,\r\n  Teacher,\r\n} from \"./Menus\";\r\n\r\nconst MobileMenu = () => {\r\n  const [toggle, setToggle] = useState(false);\r\n  const [activeMenu, setActiveMenu] = useState(\"\");\r\n  const [subMenus, setSubMenus] = useState(\"\");\r\n  const activeMenuSet = (value) =>\r\n      setActiveMenu(activeMenu === value ? \"\" : value),\r\n    activeBtn = (value) => (value === activeMenu ? \"active\" : \"\"),\r\n    activeLi = (value) =>\r\n      value === activeMenu ? { display: \"block\" } : { display: \"none\" },\r\n    setSub = (value, motherMenu) =>\r\n      motherMenu === activeMenu && value == subMenus\r\n        ? setSubMenus(\"\")\r\n        : setSubMenus(value),\r\n    activeSub = (value) =>\r\n      value === subMenus ? { display: \"block\" } : { display: \"none\" };\r\n  return (\r\n    <header id=\"header\" className=\"d-block d-xl-none\">\r\n      <div className=\"top-bar\">\r\n        <div className=\"inner jus-ct\">\r\n          <p className=\"clr-pri-1\">\r\n            Working Jours : Sun - Friday, 08:00 am - 05:00 pm\r\n          </p>\r\n        </div>\r\n      </div>\r\n      <div id=\"site-header\" className=\"\">\r\n        <div className=\"container-fluid\">\r\n          <div className=\"row\">\r\n            <div className=\"col-md-12\">\r\n              <div className=\"site-header-inner fx\">\r\n                <div id=\"site-logo\" className=\"clearfix\">\r\n                  <Link href=\"/\">\r\n                    <a className=\"logo\">\r\n                      <img src=\"assets/images/logo/logodark.png\" alt=\"Kinco\" />\r\n                    </a>\r\n                  </Link>\r\n                </div>\r\n                <div\r\n                  className=\"btn-menu\"\r\n                  style={{ display: \"block\" }}\r\n                  onClick={() => setToggle(!toggle)}\r\n                >\r\n                  <span />\r\n                </div>\r\n                <div className=\"nav-wrap\"></div>\r\n                <div className=\"header-right fx\">\r\n                  <div id=\"header-search\">\r\n                    <a href=\"#\" className=\"search-box header-search-icon\">\r\n                      <i className=\"far fa-search\" />\r\n                    </a>\r\n                    <form\r\n                      role=\"search\"\r\n                      method=\"get\"\r\n                      className=\"header-search-form\"\r\n                      action=\"#\"\r\n                    >\r\n                      <input\r\n                        type=\"text\"\r\n                        defaultValue=\"\"\r\n                        name=\"s\"\r\n                        className=\"header-search-field\"\r\n                        placeholder=\"Search...\"\r\n                      />\r\n                      <button\r\n                        type=\"submit\"\r\n                        className=\"header-search-submit\"\r\n                        title=\"Search\"\r\n                      >\r\n                        <i className=\"fa fa-search\" />\r\n                      </button>\r\n                    </form>\r\n                  </div>\r\n                  <div className=\"header-contact fx\">\r\n                    <a href=\"#\" className=\"menu-bar-right header-menu\">\r\n                      <svg\r\n                        data-name=\"Hero Area\"\r\n                        xmlns=\"http://www.w3.org/2000/svg\"\r\n                        width={58}\r\n                        height={58}\r\n                        viewBox=\"0 0 58 58\"\r\n                      >\r\n                        <defs>\r\n                          <style\r\n                            dangerouslySetInnerHTML={{\r\n                              __html:\r\n                                \"\\n                                                        .cls-1 {\\n                                                        fill: #b250fe;\\n                                                        }\\n                                                \\n                                                        .cls-1, .cls-2 {\\n                                                        fill-rule: evenodd;\\n                                                        }\\n                                                \\n                                                        .cls-2 {\\n                                                        fill: #fff;\\n                                                        }\\n                                                    \",\r\n                            }}\r\n                          />\r\n                        </defs>\r\n                        <g id=\"Menu_Area2\" data-name=\"Menu Area\">\r\n                          <g id=\"Menu_bar\" data-name=\"Menu bar\">\r\n                            <path\r\n                              id=\"Bg\"\r\n                              className=\"cls-1\"\r\n                              d=\"M7.853,2.283c14.9-3.89,29.969-1.4,43.467.819a7.923,7.923,0,0,1,5.735,5.422c3.111,14.141-.428,28.636-1.166,42.981a5.157,5.157,0,0,1-4.773,4.875c-13.49.568-23.463,3.285-41.787,0.9C5.948,56.807,2.348,54.2,1.9,51.7-0.683,37.877.2,23.508,2.194,8.757a8.71,8.71,0,0,1,5.66-6.473\"\r\n                            />\r\n                            <path\r\n                              id=\"Bar\"\r\n                              className=\"cls-2\"\r\n                              d=\"M16,17H42a2,2,0,0,1,0,4H16A2,2,0,0,1,16,17Zm0,10H42a2,2,0,0,1,0,4H16A2,2,0,0,1,16,27Zm0,10H42a2,2,0,0,1,0,4H16A2,2,0,0,1,16,37Z\"\r\n                            />\r\n                          </g>\r\n                        </g>\r\n                      </svg>\r\n                    </a>\r\n                    <div className=\"inner-contact fx\">\r\n                      <svg\r\n                        id=\"Hero_Area2\"\r\n                        data-name=\"Hero Area\"\r\n                        xmlns=\"http://www.w3.org/2000/svg\"\r\n                        xmlnsXlink=\"http://www.w3.org/1999/xlink\"\r\n                        width={49}\r\n                        height={47}\r\n                        viewBox=\"0 0 49 47\"\r\n                      >\r\n                        <g data-name=\"Menu Area\">\r\n                          <g>\r\n                            <image\r\n                              id=\"helpline2\"\r\n                              width={49}\r\n                              height={47}\r\n                              xlinkHref=\"data:img/png;base64,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\"\r\n                            />\r\n                          </g>\r\n                        </g>\r\n                      </svg>\r\n                      <ul>\r\n                        <li className=\"clr-pri-2\">Hotline</li>\r\n                        <li className=\"clr-pri-2\">+012 (345) 678</li>\r\n                      </ul>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <nav\r\n          id=\"mainnav-mobi\"\r\n          className=\"mainnav st-2\"\r\n          style={{ display: toggle ? \"block\" : \"none\" }}\r\n        >\r\n          <ul className=\"menu\" id=\"mainnav\">\r\n            <li className=\"menu-item-has-children\">\r\n              <a href=\"#\">HOME</a>\r\n              <span\r\n                className={`btn-submenu ${activeBtn(\"Home\")}`}\r\n                onClick={() => activeMenuSet(\"Home\")}\r\n              />\r\n              <ul className=\"sub-menu\" style={activeLi(\"Home\")}>\r\n                <Home />\r\n              </ul>\r\n            </li>\r\n            <li className=\"menu-item\">\r\n              <About />\r\n            </li>\r\n            <li className=\"menu-item-has-children\">\r\n              <a>PAGES</a>\r\n              <span\r\n                className={`btn-submenu ${activeBtn(\"PAGES\")}`}\r\n                onClick={() => activeMenuSet(\"PAGES\")}\r\n              />\r\n              <ul className=\"sub-menu\" style={activeLi(\"PAGES\")}>\r\n                <li className=\"inner-menu-item\">\r\n                  <a href=\"#\">Teachers</a>\r\n                  <span\r\n                    className=\"btn-submenu\"\r\n                    onClick={() => setSub(\"Teachers\", \"PAGES\")}\r\n                  />\r\n                  <ul className=\"sub-menu\" style={activeSub(\"Teachers\")}>\r\n                    <Teacher />\r\n                  </ul>\r\n                </li>\r\n                <li className=\"inner-menu-item \">\r\n                  <a href=\"#\">Classes</a>\r\n                  <span\r\n                    className=\"btn-submenu\"\r\n                    onClick={() => setSub(\"Classes\", \"PAGES\")}\r\n                  />\r\n                  <ul className=\"sub-menu\" style={activeSub(\"Classes\")}>\r\n                    <Classes />\r\n                  </ul>\r\n                </li>\r\n                <li className=\"inner-menu-item\">\r\n                  <a href=\"#\">Events</a>\r\n                  <span\r\n                    className=\"btn-submenu\"\r\n                    onClick={() => setSub(\"Events\", \"PAGES\")}\r\n                  />\r\n                  <ul className=\"sub-menu\" style={activeSub(\"Events\")}>\r\n                    <Event />\r\n                  </ul>\r\n                </li>\r\n                <Pages />\r\n              </ul>\r\n            </li>\r\n            <li className=\"menu-item-has-children\">\r\n              <a>Programs</a>\r\n              <span\r\n                className={`btn-submenu ${activeBtn(\"Programs\")}`}\r\n                onClick={() => activeMenuSet(\"Programs\")}\r\n              />\r\n              <ul className=\"sub-menu\" style={activeLi(\"Programs\")}>\r\n                <Program />\r\n              </ul>\r\n            </li>\r\n            <li className=\"menu-item-has-children\">\r\n              <a>BLOG</a>\r\n              <span\r\n                className={`btn-submenu ${activeBtn(\"BLOG\")}`}\r\n                onClick={() => activeMenuSet(\"BLOG\")}\r\n              />\r\n              <ul className=\"sub-menu\" style={activeLi(\"BLOG\")}>\r\n                <Blog />\r\n              </ul>\r\n            </li>\r\n            <li className=\"menu-item-has-children\">\r\n              <a>SHOP</a>\r\n              <span\r\n                className={`btn-submenu ${activeBtn(\"SHOP\")}`}\r\n                onClick={() => activeMenuSet(\"SHOP\")}\r\n              />\r\n              <ul className=\"sub-menu\" style={activeLi(\"SHOP\")}>\r\n                <Shop />\r\n              </ul>\r\n            </li>\r\n            <li className=\"inner\">\r\n              <Contact />\r\n            </li>\r\n          </ul>\r\n          {/* /.menu */}\r\n        </nav>\r\n      </div>\r\n      <div style={{ display: \"none\" }} />\r\n    </header>\r\n  );\r\n};\r\nexport default MobileMenu;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAaA,MAAM,aAAa;IACjB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,gBAAgB,CAAC,QACnB,cAAc,eAAe,QAAQ,KAAK,QAC5C,YAAY,CAAC,QAAW,UAAU,aAAa,WAAW,IAC1D,WAAW,CAAC,QACV,UAAU,aAAa;YAAE,SAAS;QAAQ,IAAI;YAAE,SAAS;QAAO,GAClE,SAAS,CAAC,OAAO,aACf,eAAe,cAAc,SAAS,WAClC,YAAY,MACZ,YAAY,QAClB,YAAY,CAAC,QACX,UAAU,WAAW;YAAE,SAAS;QAAQ,IAAI;YAAE,SAAS;QAAO;IAClE,qBACE,8OAAC;QAAO,IAAG;QAAS,WAAU;;0BAC5B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAAY;;;;;;;;;;;;;;;;0BAK7B,8OAAC;gBAAI,IAAG;gBAAc,WAAU;;kCAC9B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,IAAG;4CAAY,WAAU;sDAC5B,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;0DACT,cAAA,8OAAC;oDAAE,WAAU;8DACX,cAAA,8OAAC;wDAAI,KAAI;wDAAkC,KAAI;;;;;;;;;;;;;;;;;;;;;sDAIrD,8OAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,SAAS;4CAAQ;4CAC1B,SAAS,IAAM,UAAU,CAAC;sDAE1B,cAAA,8OAAC;;;;;;;;;;sDAEH,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,IAAG;;sEACN,8OAAC;4DAAE,MAAK;4DAAI,WAAU;sEACpB,cAAA,8OAAC;gEAAE,WAAU;;;;;;;;;;;sEAEf,8OAAC;4DACC,MAAK;4DACL,QAAO;4DACP,WAAU;4DACV,QAAO;;8EAEP,8OAAC;oEACC,MAAK;oEACL,cAAa;oEACb,MAAK;oEACL,WAAU;oEACV,aAAY;;;;;;8EAEd,8OAAC;oEACC,MAAK;oEACL,WAAU;oEACV,OAAM;8EAEN,cAAA,8OAAC;wEAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;8DAInB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,MAAK;4DAAI,WAAU;sEACpB,cAAA,8OAAC;gEACC,aAAU;gEACV,OAAM;gEACN,OAAO;gEACP,QAAQ;gEACR,SAAQ;;kFAER,8OAAC;kFACC,cAAA,8OAAC;4EACC,yBAAyB;gFACvB,QACE;4EACJ;;;;;;;;;;;kFAGJ,8OAAC;wEAAE,IAAG;wEAAa,aAAU;kFAC3B,cAAA,8OAAC;4EAAE,IAAG;4EAAW,aAAU;;8FACzB,8OAAC;oFACC,IAAG;oFACH,WAAU;oFACV,GAAE;;;;;;8FAEJ,8OAAC;oFACC,IAAG;oFACH,WAAU;oFACV,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;sEAMZ,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEACC,IAAG;oEACH,aAAU;oEACV,OAAM;oEACN,YAAW;oEACX,OAAO;oEACP,QAAQ;oEACR,SAAQ;8EAER,cAAA,8OAAC;wEAAE,aAAU;kFACX,cAAA,8OAAC;sFACC,cAAA,8OAAC;gFACC,IAAG;gFACH,OAAO;gFACP,QAAQ;gFACR,WAAU;;;;;;;;;;;;;;;;;;;;;8EAKlB,8OAAC;;sFACC,8OAAC;4EAAG,WAAU;sFAAY;;;;;;sFAC1B,8OAAC;4EAAG,WAAU;sFAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS1C,8OAAC;wBACC,IAAG;wBACH,WAAU;wBACV,OAAO;4BAAE,SAAS,SAAS,UAAU;wBAAO;kCAE5C,cAAA,8OAAC;4BAAG,WAAU;4BAAO,IAAG;;8CACtB,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAE,MAAK;sDAAI;;;;;;sDACZ,8OAAC;4CACC,WAAW,CAAC,YAAY,EAAE,UAAU,SAAS;4CAC7C,SAAS,IAAM,cAAc;;;;;;sDAE/B,8OAAC;4CAAG,WAAU;4CAAW,OAAO,SAAS;sDACvC,cAAA,8OAAC,wIAAA,CAAA,OAAI;;;;;;;;;;;;;;;;8CAGT,8OAAC;oCAAG,WAAU;8CACZ,cAAA,8OAAC,wIAAA,CAAA,QAAK;;;;;;;;;;8CAER,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAE;;;;;;sDACH,8OAAC;4CACC,WAAW,CAAC,YAAY,EAAE,UAAU,UAAU;4CAC9C,SAAS,IAAM,cAAc;;;;;;sDAE/B,8OAAC;4CAAG,WAAU;4CAAW,OAAO,SAAS;;8DACvC,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAE,MAAK;sEAAI;;;;;;sEACZ,8OAAC;4DACC,WAAU;4DACV,SAAS,IAAM,OAAO,YAAY;;;;;;sEAEpC,8OAAC;4DAAG,WAAU;4DAAW,OAAO,UAAU;sEACxC,cAAA,8OAAC,wIAAA,CAAA,UAAO;;;;;;;;;;;;;;;;8DAGZ,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAE,MAAK;sEAAI;;;;;;sEACZ,8OAAC;4DACC,WAAU;4DACV,SAAS,IAAM,OAAO,WAAW;;;;;;sEAEnC,8OAAC;4DAAG,WAAU;4DAAW,OAAO,UAAU;sEACxC,cAAA,8OAAC,wIAAA,CAAA,UAAO;;;;;;;;;;;;;;;;8DAGZ,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAE,MAAK;sEAAI;;;;;;sEACZ,8OAAC;4DACC,WAAU;4DACV,SAAS,IAAM,OAAO,UAAU;;;;;;sEAElC,8OAAC;4DAAG,WAAU;4DAAW,OAAO,UAAU;sEACxC,cAAA,8OAAC,wIAAA,CAAA,QAAK;;;;;;;;;;;;;;;;8DAGV,8OAAC,wIAAA,CAAA,QAAK;;;;;;;;;;;;;;;;;8CAGV,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAE;;;;;;sDACH,8OAAC;4CACC,WAAW,CAAC,YAAY,EAAE,UAAU,aAAa;4CACjD,SAAS,IAAM,cAAc;;;;;;sDAE/B,8OAAC;4CAAG,WAAU;4CAAW,OAAO,SAAS;sDACvC,cAAA,8OAAC,wIAAA,CAAA,UAAO;;;;;;;;;;;;;;;;8CAGZ,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAE;;;;;;sDACH,8OAAC;4CACC,WAAW,CAAC,YAAY,EAAE,UAAU,SAAS;4CAC7C,SAAS,IAAM,cAAc;;;;;;sDAE/B,8OAAC;4CAAG,WAAU;4CAAW,OAAO,SAAS;sDACvC,cAAA,8OAAC,wIAAA,CAAA,OAAI;;;;;;;;;;;;;;;;8CAGT,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAE;;;;;;sDACH,8OAAC;4CACC,WAAW,CAAC,YAAY,EAAE,UAAU,SAAS;4CAC7C,SAAS,IAAM,cAAc;;;;;;sDAE/B,8OAAC;4CAAG,WAAU;4CAAW,OAAO,SAAS;sDACvC,cAAA,8OAAC,wIAAA,CAAA,OAAI;;;;;;;;;;;;;;;;8CAGT,8OAAC;oCAAG,WAAU;8CACZ,cAAA,8OAAC,wIAAA,CAAA,UAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAMhB,8OAAC;gBAAI,OAAO;oBAAE,SAAS;gBAAO;;;;;;;;;;;;AAGpC;uCACe", "debugId": null}}, {"offset": {"line": 4176, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kinco-day-care-kindergarten-react-template-30-06-2025/Kinco/React%20Template/kinco-modern/src/lib/layouts/ScrollTop.js"], "sourcesContent": ["import { Fragment, useEffect } from \"react\";\r\nimport { scrollTopFun } from \"../utils\";\r\n\r\nconst ScrollTop = () => {\r\n  useEffect(() => {\r\n    scrollTopFun();\r\n  }, []);\r\n  const onClick = () => {\r\n    window.scrollTo({ top: 0, behavior: \"smooth\" });\r\n  };\r\n  return (\r\n    <Fragment>\r\n      <a id=\"scroll-top\" onClick={() => onClick()}></a>\r\n    </Fragment>\r\n  );\r\n};\r\nexport default ScrollTop;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEA,MAAM,YAAY;IAChB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD;IACb,GAAG,EAAE;IACL,MAAM,UAAU;QACd,OAAO,QAAQ,CAAC;YAAE,KAAK;YAAG,UAAU;QAAS;IAC/C;IACA,qBACE,8OAAC,qMAAA,CAAA,WAAQ;kBACP,cAAA,8OAAC;YAAE,IAAG;YAAa,SAAS,IAAM;;;;;;;;;;;AAGxC;uCACe", "debugId": null}}, {"offset": {"line": 4217, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kinco-day-care-kindergarten-react-template-30-06-2025/Kinco/React%20Template/kinco-modern/src/lib/layouts/Layout.js"], "sourcesContent": ["import { useEffect } from \"react\";\r\nimport VideoPopup from \"../components/VideoPopup\";\r\nimport { activeNavMenu, animation, stickyNav } from \"../utils\";\r\nimport Footer from \"./Footer\";\r\nimport Header from \"./header/Header\";\r\nimport MobileMenu from \"./header/MobileMenu\";\r\nimport ScrollTop from \"./ScrollTop\";\r\n\r\nconst Layout = ({ children, noHeader, noFooter, bodyClass }) => {\r\n  useEffect(() => {\r\n    animation();\r\n    stickyNav();\r\n    if (typeof bodyClass === \"object\") {\r\n      document.querySelector(\"body\").classList.add(...bodyClass);\r\n    } else {\r\n      console.log(typeof bodyClass);\r\n      document.querySelector(\"body\").classList.add(bodyClass);\r\n    }\r\n    activeNavMenu();\r\n  }, []);\r\n\r\n  return (\r\n    <div id=\"wrapper\">\r\n      <VideoPopup />\r\n      <div id=\"page\" className=\"clearfix\">\r\n        {!noHeader && <Header />}\r\n        <MobileMenu /> {children} {!noFooter && <Footer />}\r\n      </div>\r\n      <ScrollTop />\r\n    </div>\r\n  );\r\n};\r\nexport default Layout;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AAEA,MAAM,SAAS,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE;IACzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,CAAA,GAAA,mHAAA,CAAA,YAAS,AAAD;QACR,CAAA,GAAA,mHAAA,CAAA,YAAS,AAAD;QACR,IAAI,OAAO,cAAc,UAAU;YACjC,SAAS,aAAa,CAAC,QAAQ,SAAS,CAAC,GAAG,IAAI;QAClD,OAAO;YACL,QAAQ,GAAG,CAAC,OAAO;YACnB,SAAS,aAAa,CAAC,QAAQ,SAAS,CAAC,GAAG,CAAC;QAC/C;QACA,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD;IACd,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,IAAG;;0BACN,8OAAC,sIAAA,CAAA,UAAU;;;;;0BACX,8OAAC;gBAAI,IAAG;gBAAO,WAAU;;oBACtB,CAAC,0BAAY,8OAAC,yIAAA,CAAA,UAAM;;;;;kCACrB,8OAAC,6IAAA,CAAA,UAAU;;;;;oBAAG;oBAAE;oBAAS;oBAAE,CAAC,0BAAY,8OAAC,+HAAA,CAAA,UAAM;;;;;;;;;;;0BAEjD,8OAAC,kIAAA,CAAA,UAAS;;;;;;;;;;;AAGhB;uCACe", "debugId": null}}, {"offset": {"line": 4303, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kinco-day-care-kindergarten-react-template-30-06-2025/Kinco/React%20Template/kinco-modern/src/lib/layouts/header/Header1.js"], "sourcesContent": ["import Link from \"next/link\";\r\nimport { useEffect, useState } from \"react\";\r\nimport Sidebar from \"../../components/Sidebar\";\r\nimport {\r\n  About,\r\n  Blog,\r\n  Classes,\r\n  Contact,\r\n  Event,\r\n  Home,\r\n  Pages,\r\n  Program,\r\n  Shop,\r\n  Teacher,\r\n} from \"./Menus\";\r\n\r\nconst Header1 = () => {\r\n  const [sidebarToggle, setSidebarToggle] = useState(false);\r\n  useEffect(() => {\r\n    document.querySelector(\"body\").className =\r\n      \"counter-scroll header-fixed main\";\r\n  }, []);\r\n  return (\r\n    <header id=\"site-header\" className=\"header_main d-none d-xl-block\">\r\n      <div className=\"container\">\r\n        <div className=\"row\">\r\n          <div className=\"col-xl-12 col-lg-12 col-md-6 col-sm-6 col-6\">\r\n            <div className=\"top-bar-2 fx\">\r\n              <div id=\"site-logo\" className=\"clearfix\">\r\n                <Link href=\"/\" className=\"logo st-2\">\r\n                  <img src=\"assets/images/logo/logodark-2.png\" alt=\"Kinco\" />\r\n                </Link>\r\n              </div>\r\n              <div className=\"header-contact fx\">\r\n                <div className=\"inner-contact fx\">\r\n                  <svg\r\n                    data-name=\"Hero Area\"\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                    xmlnsXlink=\"http://www.w3.org/1999/xlink\"\r\n                    width={35}\r\n                    height={53}\r\n                    viewBox=\"0 0 35 53\"\r\n                  >\r\n                    <g id=\"Address\">\r\n                      <image\r\n                        id=\"map-pin\"\r\n                        width={35}\r\n                        height={53}\r\n                        xlinkHref=\"data:img/png;base64,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\"\r\n                      />\r\n                    </g>\r\n                  </svg>\r\n                  <ul>\r\n                    <li className=\"clr-pri-4\">School Location</li>\r\n                    <li className=\"clr-pri-2\">55 Main Street, New York</li>\r\n                  </ul>\r\n                </div>\r\n                <div className=\"inner-contact fx\">\r\n                  <svg\r\n                    data-name=\"Hero Area\"\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                    xmlnsXlink=\"http://www.w3.org/1999/xlink\"\r\n                    width={49}\r\n                    height={47}\r\n                    viewBox=\"0 0 49 47\"\r\n                  >\r\n                    <g>\r\n                      <image\r\n                        width={49}\r\n                        height={47}\r\n                        xlinkHref=\"data:img/png;base64,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\"\r\n                      />\r\n                    </g>\r\n                  </svg>\r\n                  <ul>\r\n                    <li className=\"clr-pri-4\">Hotline</li>\r\n                    <li className=\"clr-pri-2\">+012 (345) 678</li>\r\n                  </ul>\r\n                </div>\r\n                <a\r\n                  href=\"#\"\r\n                  className=\"menu-bar-right header-menu\"\r\n                  onClick={() => setSidebarToggle(true)}\r\n                >\r\n                  <svg\r\n                    data-name=\"Hero Area\"\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                    width={58}\r\n                    height={58}\r\n                    viewBox=\"0 0 58 58\"\r\n                  >\r\n                    <defs>\r\n                      <style\r\n                        dangerouslySetInnerHTML={{\r\n                          __html:\r\n                            \"\\n                                                .cls-1 {\\n                                                fill: #b250fe;\\n                                                }\\n                                        \\n                                                .cls-1, .cls-2 {\\n                                                fill-rule: evenodd;\\n                                                }\\n                                        \\n                                                .cls-2 {\\n                                                fill: #fff;\\n                                                }\\n                                            \",\r\n                        }}\r\n                      />\r\n                    </defs>\r\n                    <g data-name=\"Menu Area\">\r\n                      <g id=\"Menu_bar\" data-name=\"Menu bar\">\r\n                        <path\r\n                          id=\"Bg\"\r\n                          className=\"cls-1\"\r\n                          d=\"M7.853,2.283c14.9-3.89,29.969-1.4,43.467.819a7.923,7.923,0,0,1,5.735,5.422c3.111,14.141-.428,28.636-1.166,42.981a5.157,5.157,0,0,1-4.773,4.875c-13.49.568-23.463,3.285-41.787,0.9C5.948,56.807,2.348,54.2,1.9,51.7-0.683,37.877.2,23.508,2.194,8.757a8.71,8.71,0,0,1,5.66-6.473\"\r\n                        />\r\n                        <path\r\n                          id=\"Bar\"\r\n                          className=\"cls-2\"\r\n                          d=\"M16,17H42a2,2,0,0,1,0,4H16A2,2,0,0,1,16,17Zm0,10H42a2,2,0,0,1,0,4H16A2,2,0,0,1,16,27Zm0,10H42a2,2,0,0,1,0,4H16A2,2,0,0,1,16,37Z\"\r\n                        />\r\n                      </g>\r\n                    </g>\r\n                  </svg>\r\n                </a>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div className=\"col-xl-12 col-lg-12 col-md-6 col-sm-6 col-6\">\r\n            <div className=\"site-header-inner st-2 fx\">\r\n              <div className=\"btn-menu\">\r\n                <span />\r\n              </div>\r\n              <div className=\"nav-wrap\">\r\n                <nav id=\"mainnav\" className=\"mainnav st-2\">\r\n                  <ul className=\"menu\">\r\n                    <li className=\"menu-item-has-children current-menu-item\">\r\n                      <a href=\"#\">HOME</a>\r\n                      <ul className=\"sub-menu\">\r\n                        <Home />\r\n                      </ul>\r\n                    </li>\r\n                    <li className=\"menu-item\">\r\n                      <About />\r\n                    </li>\r\n                    <li className=\"menu-item-has-children\">\r\n                      <a>PAGES</a>\r\n                      <ul className=\"sub-menu\">\r\n                        <li className=\"inner-menu-item\">\r\n                          <a href=\"#\">Teachers</a>\r\n                          <ul className=\"sub-menu\">\r\n                            <Teacher />\r\n                          </ul>\r\n                        </li>\r\n                        <li className=\"inner-menu-item\">\r\n                          <a href=\"#\">Classes</a>\r\n                          <ul className=\"sub-menu\">\r\n                            <Classes />\r\n                          </ul>\r\n                        </li>\r\n                        <li className=\"inner-menu-item\">\r\n                          <a href=\"#\">Events</a>\r\n                          <ul className=\"sub-menu\">\r\n                            <Event />\r\n                          </ul>\r\n                        </li>\r\n                        <Pages />\r\n                      </ul>\r\n                    </li>\r\n                    <li className=\"menu-item-has-children\">\r\n                      <a>Programs</a>\r\n                      <ul className=\"sub-menu\">\r\n                        <Program />\r\n                      </ul>\r\n                    </li>\r\n                    <li className=\"menu-item-has-children\">\r\n                      <a>BLOG</a>\r\n                      <ul className=\"sub-menu\">\r\n                        <Blog />\r\n                      </ul>\r\n                    </li>\r\n                    <li className=\"menu-item-has-children\">\r\n                      <a>SHOP</a>\r\n                      <ul className=\"sub-menu\">\r\n                        <Shop />\r\n                      </ul>\r\n                    </li>\r\n                    <li className=\"inner\">\r\n                      <Contact />\r\n                    </li>\r\n                  </ul>\r\n                  {/* /.menu */}\r\n                </nav>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <Sidebar show={sidebarToggle} close={() => setSidebarToggle(false)} />\r\n    </header>\r\n  );\r\n};\r\nexport default Header1;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;AAaA,MAAM,UAAU;IACd,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS,aAAa,CAAC,QAAQ,SAAS,GACtC;IACJ,GAAG,EAAE;IACL,qBACE,8OAAC;QAAO,IAAG;QAAc,WAAU;;0BACjC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,IAAG;wCAAY,WAAU;kDAC5B,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;sDACvB,cAAA,8OAAC;gDAAI,KAAI;gDAAoC,KAAI;;;;;;;;;;;;;;;;kDAGrD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,aAAU;wDACV,OAAM;wDACN,YAAW;wDACX,OAAO;wDACP,QAAQ;wDACR,SAAQ;kEAER,cAAA,8OAAC;4DAAE,IAAG;sEACJ,cAAA,8OAAC;gEACC,IAAG;gEACH,OAAO;gEACP,QAAQ;gEACR,WAAU;;;;;;;;;;;;;;;;kEAIhB,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAY;;;;;;0EAC1B,8OAAC;gEAAG,WAAU;0EAAY;;;;;;;;;;;;;;;;;;0DAG9B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,aAAU;wDACV,OAAM;wDACN,YAAW;wDACX,OAAO;wDACP,QAAQ;wDACR,SAAQ;kEAER,cAAA,8OAAC;sEACC,cAAA,8OAAC;gEACC,OAAO;gEACP,QAAQ;gEACR,WAAU;;;;;;;;;;;;;;;;kEAIhB,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAY;;;;;;0EAC1B,8OAAC;gEAAG,WAAU;0EAAY;;;;;;;;;;;;;;;;;;0DAG9B,8OAAC;gDACC,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,iBAAiB;0DAEhC,cAAA,8OAAC;oDACC,aAAU;oDACV,OAAM;oDACN,OAAO;oDACP,QAAQ;oDACR,SAAQ;;sEAER,8OAAC;sEACC,cAAA,8OAAC;gEACC,yBAAyB;oEACvB,QACE;gEACJ;;;;;;;;;;;sEAGJ,8OAAC;4DAAE,aAAU;sEACX,cAAA,8OAAC;gEAAE,IAAG;gEAAW,aAAU;;kFACzB,8OAAC;wEACC,IAAG;wEACH,WAAU;wEACV,GAAE;;;;;;kFAEJ,8OAAC;wEACC,IAAG;wEACH,WAAU;wEACV,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCASlB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;;;;;;;;;;kDAEH,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,IAAG;4CAAU,WAAU;sDAC1B,cAAA,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAE,MAAK;0EAAI;;;;;;0EACZ,8OAAC;gEAAG,WAAU;0EACZ,cAAA,8OAAC,wIAAA,CAAA,OAAI;;;;;;;;;;;;;;;;kEAGT,8OAAC;wDAAG,WAAU;kEACZ,cAAA,8OAAC,wIAAA,CAAA,QAAK;;;;;;;;;;kEAER,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;0EAAE;;;;;;0EACH,8OAAC;gEAAG,WAAU;;kFACZ,8OAAC;wEAAG,WAAU;;0FACZ,8OAAC;gFAAE,MAAK;0FAAI;;;;;;0FACZ,8OAAC;gFAAG,WAAU;0FACZ,cAAA,8OAAC,wIAAA,CAAA,UAAO;;;;;;;;;;;;;;;;kFAGZ,8OAAC;wEAAG,WAAU;;0FACZ,8OAAC;gFAAE,MAAK;0FAAI;;;;;;0FACZ,8OAAC;gFAAG,WAAU;0FACZ,cAAA,8OAAC,wIAAA,CAAA,UAAO;;;;;;;;;;;;;;;;kFAGZ,8OAAC;wEAAG,WAAU;;0FACZ,8OAAC;gFAAE,MAAK;0FAAI;;;;;;0FACZ,8OAAC;gFAAG,WAAU;0FACZ,cAAA,8OAAC,wIAAA,CAAA,QAAK;;;;;;;;;;;;;;;;kFAGV,8OAAC,wIAAA,CAAA,QAAK;;;;;;;;;;;;;;;;;kEAGV,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;0EAAE;;;;;;0EACH,8OAAC;gEAAG,WAAU;0EACZ,cAAA,8OAAC,wIAAA,CAAA,UAAO;;;;;;;;;;;;;;;;kEAGZ,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;0EAAE;;;;;;0EACH,8OAAC;gEAAG,WAAU;0EACZ,cAAA,8OAAC,wIAAA,CAAA,OAAI;;;;;;;;;;;;;;;;kEAGT,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;0EAAE;;;;;;0EACH,8OAAC;gEAAG,WAAU;0EACZ,cAAA,8OAAC,wIAAA,CAAA,OAAI;;;;;;;;;;;;;;;;kEAGT,8OAAC;wDAAG,WAAU;kEACZ,cAAA,8OAAC,wIAAA,CAAA,UAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUxB,8OAAC,mIAAA,CAAA,UAAO;gBAAC,MAAM;gBAAe,OAAO,IAAM,iBAAiB;;;;;;;;;;;;AAGlE;uCACe", "debugId": null}}, {"offset": {"line": 4912, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kinco-day-care-kindergarten-react-template-30-06-2025/Kinco/React%20Template/kinco-modern/src/lib/layouts/Footer2.js"], "sourcesContent": ["import Link from \"next/link\";\r\nconst Footer2 = () => {\r\n  return (\r\n    <footer id=\"footer\" className=\"st-2\">\r\n      <section className=\"tf-subcribe-2\">\r\n        <div className=\"container\">\r\n          <div className=\"row\">\r\n            <div className=\"col-12\">\r\n              <div\r\n                className=\"fl-subcribe wow fadeIn   animated\"\r\n                data-wow-delay=\"0.3ms\"\r\n                data-wow-duration=\"1200ms\"\r\n              >\r\n                <div className=\"subcribe-wp\">\r\n                  <h2 className=\"title clr-pri-5\">Subscribe Our Newsletter</h2>\r\n                  <p className=\"sub f-mulish clr-pri-2\">\r\n                    Amet consectetur adipiscing elit, sed do eiusmod tempor\r\n                    incididunt ut labore et dolore magna aliqua suspendisse\r\n                    ultrices gravida. Risus commodo\r\n                  </p>\r\n                </div>\r\n                <div className=\"subcribe-form fx\">\r\n                  <form\r\n                    onSubmit={(e) => e.preventDefault()}\r\n                    action=\"#\"\r\n                    id=\"subscribe-form\"\r\n                  >\r\n                    <input\r\n                      type=\"email\"\r\n                      id=\"subscribe-email\"\r\n                      placeholder=\"Email Address\"\r\n                    />\r\n                    <button className=\"fl-btn st-7\" id=\"subscribe-button\">\r\n                      <span className=\"inner\">Subscribe</span>\r\n                    </button>\r\n                  </form>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n      <div className=\"footer-inner st-2\">\r\n        <div className=\"container\">\r\n          <div className=\"row\">\r\n            <div className=\"col-12\">\r\n              <div className=\"widget-footer\">\r\n                <div className=\"widget widget-logo\">\r\n                  <div className=\"logo-bottom\" id=\"logo-footer\">\r\n                    <Link href=\"/\">\r\n                      <img\r\n                        src=\"assets/images/logo/logofootert.png\"\r\n                        alt=\"kinco\"\r\n                      />\r\n                    </Link>\r\n                  </div>\r\n                  <p className=\"wrap f-mulish\">\r\n                    Sit amet consectetur adipiscing elit sed do eiusmod teminci\r\n                    idunt ut labore et dolore magna\r\n                  </p>\r\n                  <div className=\"list-contact\">\r\n                    <ul>\r\n                      <li className=\"fx\">\r\n                        <span>\r\n                          <i className=\"far fa-map-marker-alt\" /> 55 Main\r\n                          Street, New York\r\n                        </span>\r\n                      </li>\r\n                      <li className=\"fx\">\r\n                        <a href=\"mailto:<EMAIL>\">\r\n                          <i className=\"far fa-envelope\" /> <EMAIL>\r\n                        </a>\r\n                      </li>\r\n                      <li className=\"fx\">\r\n                        <a href=\"tel:012345678\">\r\n                          <i className=\"fal fa-phone\" /> +012 (345) 678\r\n                        </a>\r\n                      </li>\r\n                    </ul>\r\n                  </div>\r\n                </div>\r\n                <div className=\"widget widget-business\">\r\n                  <div className=\"inner\">\r\n                    <div className=\"op-time\">\r\n                      <h4 className=\"title-widget\">opening hours</h4>\r\n                      <ul>\r\n                        <li>\r\n                          <span className=\"f-mulish\">Sunday - Friday</span>\r\n                        </li>\r\n                        <li>\r\n                          <span className=\"f-mulish\">08 am - 05 pm</span>\r\n                        </li>\r\n                      </ul>\r\n                    </div>\r\n                    <div className=\"cls-time\">\r\n                      <p>Every Satarday and Govt Holiday</p>\r\n                      <h4 className=\"title-widget\">closed</h4>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <div className=\"widget widget-link\">\r\n                  <h4 className=\"title-widget\">Our Program</h4>\r\n                  <ul className=\"list-link\">\r\n                    <li className=\"fx\">\r\n                      <Link href=\"/program\">\r\n                        <a className=\"wd-ctm f-mulish\">Arts &amp; Drawing</a>\r\n                      </Link>\r\n                    </li>\r\n                    <li className=\"fx\">\r\n                      <Link href=\"/program\">\r\n                        <a className=\"wd-ctm f-mulish\">Computer Engineering </a>\r\n                      </Link>\r\n                    </li>\r\n                    <li className=\"fx\">\r\n                      <Link href=\"/program\">\r\n                        <a className=\"wd-ctm f-mulish\">Digital Mathematics</a>\r\n                      </Link>\r\n                    </li>\r\n                    <li className=\"fx\">\r\n                      <Link href=\"/program\">\r\n                        <a className=\"wd-ctm f-mulish\">Physical Exercise</a>\r\n                      </Link>\r\n                    </li>\r\n                    <li className=\"fx\">\r\n                      <Link href=\"/program\">\r\n                        <a className=\"wd-ctm f-mulish\">General Science</a>\r\n                      </Link>\r\n                    </li>\r\n                    <li className=\"fx\">\r\n                      <Link href=\"/program\">\r\n                        <a className=\"wd-ctm f-mulish\">English Basic</a>\r\n                      </Link>\r\n                    </li>\r\n                    <li className=\"fx\">\r\n                      <Link href=\"/program\">\r\n                        <a className=\"wd-ctm f-mulish\">Social Science</a>\r\n                      </Link>\r\n                    </li>\r\n                  </ul>\r\n                </div>\r\n                <div className=\"widget widget-news st-3\">\r\n                  <h4 className=\"title-widget\">recent news</h4>\r\n                  <ul className=\"list-news\">\r\n                    <li className=\"fx\">\r\n                      <img\r\n                        src=\"assets/images/thumbnails/widget9.jpg\"\r\n                        alt=\"Image\"\r\n                        className=\"feature\"\r\n                      />\r\n                      <ul className=\"box-content\">\r\n                        <li>\r\n                          <h6 className=\"title\">\r\n                            <Link href=\"/blog-grid\">\r\n                              <a>Useful Code Extened End Developers</a>\r\n                            </Link>\r\n                          </h6>\r\n                        </li>\r\n                        <li>\r\n                          <Link href=\"/blog-grid\">\r\n                            <a className=\"fx meta-news clr-pri-6\">\r\n                              <i className=\"far fa-calendar-alt\" />\r\n                              25 dec 2021\r\n                            </a>\r\n                          </Link>\r\n                        </li>\r\n                      </ul>\r\n                    </li>\r\n                    <li className=\"fx\">\r\n                      <img\r\n                        src=\"assets/images/thumbnails/widget10.jpg\"\r\n                        alt=\"Image\"\r\n                        className=\"feature\"\r\n                      />\r\n                      <ul className=\"box-content\">\r\n                        <li>\r\n                          <h6 className=\"title\">\r\n                            <Link href=\"/blog-grid\">\r\n                              <a>Useful Code Extened End Developers</a>\r\n                            </Link>\r\n                          </h6>\r\n                        </li>\r\n                        <li>\r\n                          <Link href=\"/blog-grid\">\r\n                            <a className=\"fx meta-news clr-pri-6\">\r\n                              <i className=\"far fa-calendar-alt\" />\r\n                              25 dec 2021\r\n                            </a>\r\n                          </Link>\r\n                        </li>\r\n                      </ul>\r\n                    </li>\r\n                  </ul>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"col-12\">\r\n              <div className=\"footer-bottom jus-ct\">\r\n                <p className=\"copy-right\">\r\n                  Copyright © {new Date().getFullYear()}, Kinco - Kindergarten\r\n                  HTML Template. Designed by{\" \"}\r\n                  <a href=\"https://themeforest.net/user/webtend/portfolio\">\r\n                    Webtend\r\n                  </a>\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </footer>\r\n  );\r\n};\r\nexport default Footer2;\r\n"], "names": [], "mappings": ";;;;AAAA;;;AACA,MAAM,UAAU;IACd,qBACE,8OAAC;QAAO,IAAG;QAAS,WAAU;;0BAC5B,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,WAAU;gCACV,kBAAe;gCACf,qBAAkB;;kDAElB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAkB;;;;;;0DAChC,8OAAC;gDAAE,WAAU;0DAAyB;;;;;;;;;;;;kDAMxC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,UAAU,CAAC,IAAM,EAAE,cAAc;4CACjC,QAAO;4CACP,IAAG;;8DAEH,8OAAC;oDACC,MAAK;oDACL,IAAG;oDACH,aAAY;;;;;;8DAEd,8OAAC;oDAAO,WAAU;oDAAc,IAAG;8DACjC,cAAA,8OAAC;wDAAK,WAAU;kEAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASxC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;oDAAc,IAAG;8DAC9B,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;kEACT,cAAA,8OAAC;4DACC,KAAI;4DACJ,KAAI;;;;;;;;;;;;;;;;8DAIV,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;8DAI7B,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EACZ,cAAA,8OAAC;;sFACC,8OAAC;4EAAE,WAAU;;;;;;wEAA0B;;;;;;;;;;;;0EAI3C,8OAAC;gEAAG,WAAU;0EACZ,cAAA,8OAAC;oEAAE,MAAK;;sFACN,8OAAC;4EAAE,WAAU;;;;;;wEAAoB;;;;;;;;;;;;0EAGrC,8OAAC;gEAAG,WAAU;0EACZ,cAAA,8OAAC;oEAAE,MAAK;;sFACN,8OAAC;4EAAE,WAAU;;;;;;wEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAMxC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAe;;;;;;0EAC7B,8OAAC;;kFACC,8OAAC;kFACC,cAAA,8OAAC;4EAAK,WAAU;sFAAW;;;;;;;;;;;kFAE7B,8OAAC;kFACC,cAAA,8OAAC;4EAAK,WAAU;sFAAW;;;;;;;;;;;;;;;;;;;;;;;kEAIjC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAE;;;;;;0EACH,8OAAC;gEAAG,WAAU;0EAAe;;;;;;;;;;;;;;;;;;;;;;;sDAInC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAe;;;;;;8DAC7B,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;gEAAC,MAAK;0EACT,cAAA,8OAAC;oEAAE,WAAU;8EAAkB;;;;;;;;;;;;;;;;sEAGnC,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;gEAAC,MAAK;0EACT,cAAA,8OAAC;oEAAE,WAAU;8EAAkB;;;;;;;;;;;;;;;;sEAGnC,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;gEAAC,MAAK;0EACT,cAAA,8OAAC;oEAAE,WAAU;8EAAkB;;;;;;;;;;;;;;;;sEAGnC,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;gEAAC,MAAK;0EACT,cAAA,8OAAC;oEAAE,WAAU;8EAAkB;;;;;;;;;;;;;;;;sEAGnC,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;gEAAC,MAAK;0EACT,cAAA,8OAAC;oEAAE,WAAU;8EAAkB;;;;;;;;;;;;;;;;sEAGnC,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;gEAAC,MAAK;0EACT,cAAA,8OAAC;oEAAE,WAAU;8EAAkB;;;;;;;;;;;;;;;;sEAGnC,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;gEAAC,MAAK;0EACT,cAAA,8OAAC;oEAAE,WAAU;8EAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAKvC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAe;;;;;;8DAC7B,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEACC,KAAI;oEACJ,KAAI;oEACJ,WAAU;;;;;;8EAEZ,8OAAC;oEAAG,WAAU;;sFACZ,8OAAC;sFACC,cAAA,8OAAC;gFAAG,WAAU;0FACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;oFAAC,MAAK;8FACT,cAAA,8OAAC;kGAAE;;;;;;;;;;;;;;;;;;;;;sFAIT,8OAAC;sFACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gFAAC,MAAK;0FACT,cAAA,8OAAC;oFAAE,WAAU;;sGACX,8OAAC;4FAAE,WAAU;;;;;;wFAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sEAO/C,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEACC,KAAI;oEACJ,KAAI;oEACJ,WAAU;;;;;;8EAEZ,8OAAC;oEAAG,WAAU;;sFACZ,8OAAC;sFACC,cAAA,8OAAC;gFAAG,WAAU;0FACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;oFAAC,MAAK;8FACT,cAAA,8OAAC;kGAAE;;;;;;;;;;;;;;;;;;;;;sFAIT,8OAAC;sFACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gFAAC,MAAK;0FACT,cAAA,8OAAC;oFAAE,WAAU;;sGACX,8OAAC;4FAAE,WAAU;;;;;;wFAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAWvD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;;4CAAa;4CACX,IAAI,OAAO,WAAW;4CAAG;4CACX;0DAC3B,8OAAC;gDAAE,MAAK;0DAAiD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW3E;uCACe", "debugId": null}}, {"offset": {"line": 5712, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kinco-day-care-kindergarten-react-template-30-06-2025/Kinco/React%20Template/kinco-modern/src/app/page.js"], "sourcesContent": ["'use client';\n\nimport { useEffect } from \"react\";\nimport Link from \"next/link\";\nimport { Swiper, SwiperSlide } from \"swiper/react\";\nimport { Navigation, Pagination } from \"swiper/modules\";\nimport Layout from \"../lib/layouts/Layout\";\nimport Header1 from \"../lib/layouts/header/Header1\";\nimport Footer2 from \"../lib/layouts/Footer2\";\nimport { activeNavMenu } from \"../lib/utils\";\n\nexport default function Home() {\n  useEffect(() => {\n    activeNavMenu();\n  }, []);\n\n  return (\n    <Layout noFooter noHeader bodyClass={\"main\"}>\n      <Header1 />\n\n      {/* Hero Section */}\n      <section className=\"tf-slider-1\">\n        <div className=\"overlay\" />\n        <div className=\"container-fluid\">\n          <div className=\"row\">\n            <div className=\"col-12\">\n              <div className=\"slider-1\">\n                <div className=\"themesflat-carousel clearfix\">\n                  <Swiper\n                    modules={[Navigation, Pagination]}\n                    spaceBetween={0}\n                    slidesPerView={1}\n                    navigation\n                    pagination={{ clickable: true }}\n                    loop={true}\n                    className=\"owl-carousel owl-theme none dots-none\"\n                  >\n                    <SwiperSlide className=\"owl-item\">\n                      <div className=\"item-slider-1\">\n                        <div className=\"box-content\">\n                          <div className=\"sub clr-pri-2\">\n                            We Care Child Study\n                          </div>\n                          <div className=\"title clr-pri-2\">\n                            Start Learning With\n                          </div>\n                          <div className=\"box-custom\">\n                            <div className=\"wrap clr-pri-1\">Kinco School</div>\n                          </div>\n                          <div className=\"des clr-pri-2\">\n                            Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut elit tellus, luctus nec ullamcorper mattis, pulvinar dapibus leo.\n                          </div>\n                          <div className=\"box-btn\">\n                            <Link href=\"/about\" className=\"tf-btn style-1\">\n                              <span>Learn More</span>\n                            </Link>\n                          </div>\n                        </div>\n                        <div className=\"box-feature\">\n                          <img\n                            src=\"/assets/images/common/slider-1.png\"\n                            alt=\"Kinco\"\n                          />\n                        </div>\n                      </div>\n                    </SwiperSlide>\n\n                    <SwiperSlide className=\"owl-item\">\n                      <div className=\"item-slider-1\">\n                        <div className=\"box-content\">\n                          <div className=\"sub clr-pri-2\">\n                            Quality Education\n                          </div>\n                          <div className=\"title clr-pri-2\">\n                            Best Care For Your\n                          </div>\n                          <div className=\"box-custom\">\n                            <div className=\"wrap clr-pri-1\">Little Ones</div>\n                          </div>\n                          <div className=\"des clr-pri-2\">\n                            Providing a safe, nurturing environment where children can learn, grow, and develop their full potential.\n                          </div>\n                          <div className=\"box-btn\">\n                            <Link href=\"/contact\" className=\"tf-btn style-1\">\n                              <span>Contact Us</span>\n                            </Link>\n                          </div>\n                        </div>\n                        <div className=\"box-feature\">\n                          <img\n                            src=\"/assets/images/common/slider-2.jpg\"\n                            alt=\"Kinco\"\n                          />\n                        </div>\n                      </div>\n                    </SwiperSlide>\n                  </Swiper>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* About Section */}\n      <section className=\"tf-section tf-about-us-1\">\n        <div className=\"container\">\n          <div className=\"row\">\n            <div className=\"col-12\">\n              <div className=\"about-us-1\">\n                <div className=\"box-content\">\n                  <div className=\"box-sub-tag\">\n                    <div className=\"sub-tag-icon\">\n                      <i className=\"flaticon-kindergarten\" />\n                    </div>\n                    <div className=\"sub-tag-title\">\n                      <p>About Kinco</p>\n                    </div>\n                  </div>\n                  <div className=\"title\">\n                    We Provide The Best Education For Your Children\n                  </div>\n                  <div className=\"des\">\n                    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut elit tellus, luctus nec ullamcorper mattis, pulvinar dapibus leo. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.\n                  </div>\n                  <div className=\"box-btn\">\n                    <Link href=\"/about\" className=\"tf-btn style-2\">\n                      <span>Learn More</span>\n                    </Link>\n                  </div>\n                </div>\n                <div className=\"box-feature\">\n                  <img\n                    src=\"/assets/images/common/sc-about1.jpg\"\n                    alt=\"About Kinco\"\n                  />\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <Footer2 />\n    </Layout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAWe,SAAS;IACtB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD;IACd,GAAG,EAAE;IAEL,qBACE,8OAAC,+HAAA,CAAA,UAAM;QAAC,QAAQ;QAAC,QAAQ;QAAC,WAAW;;0BACnC,8OAAC,0IAAA,CAAA,UAAO;;;;;0BAGR,8OAAC;gBAAQ,WAAU;;kCACjB,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,0IAAA,CAAA,SAAM;4CACL,SAAS;gDAAC,yLAAA,CAAA,aAAU;gDAAE,yLAAA,CAAA,aAAU;6CAAC;4CACjC,cAAc;4CACd,eAAe;4CACf,UAAU;4CACV,YAAY;gDAAE,WAAW;4CAAK;4CAC9B,MAAM;4CACN,WAAU;;8DAEV,8OAAC,0IAAA,CAAA,cAAW;oDAAC,WAAU;8DACrB,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFAAgB;;;;;;kFAG/B,8OAAC;wEAAI,WAAU;kFAAkB;;;;;;kFAGjC,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EAAI,WAAU;sFAAiB;;;;;;;;;;;kFAElC,8OAAC;wEAAI,WAAU;kFAAgB;;;;;;kFAG/B,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4EAAC,MAAK;4EAAS,WAAU;sFAC5B,cAAA,8OAAC;0FAAK;;;;;;;;;;;;;;;;;;;;;;0EAIZ,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEACC,KAAI;oEACJ,KAAI;;;;;;;;;;;;;;;;;;;;;;8DAMZ,8OAAC,0IAAA,CAAA,cAAW;oDAAC,WAAU;8DACrB,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFAAgB;;;;;;kFAG/B,8OAAC;wEAAI,WAAU;kFAAkB;;;;;;kFAGjC,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EAAI,WAAU;sFAAiB;;;;;;;;;;;kFAElC,8OAAC;wEAAI,WAAU;kFAAgB;;;;;;kFAG/B,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4EAAC,MAAK;4EAAW,WAAU;sFAC9B,cAAA,8OAAC;0FAAK;;;;;;;;;;;;;;;;;;;;;;0EAIZ,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEACC,KAAI;oEACJ,KAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAc1B,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAE,WAAU;;;;;;;;;;;kEAEf,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;sEAAE;;;;;;;;;;;;;;;;;0DAGP,8OAAC;gDAAI,WAAU;0DAAQ;;;;;;0DAGvB,8OAAC;gDAAI,WAAU;0DAAM;;;;;;0DAGrB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAS,WAAU;8DAC5B,cAAA,8OAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;kDAIZ,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,KAAI;4CACJ,KAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASlB,8OAAC,gIAAA,CAAA,UAAO;;;;;;;;;;;AAGd", "debugId": null}}]}