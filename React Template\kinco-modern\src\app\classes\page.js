'use client';

import Link from "next/link";

export default function Classes() {
  const classes = [
    {
      id: 1,
      title: "Drawing & Painting",
      age: "3-5 years",
      time: "9:00 AM - 11:00 AM",
      description: "Creative art classes to develop imagination and fine motor skills",
      image: "/assets/images/common/sc-program1.jpg"
    },
    {
      id: 2,
      title: "Computer Learning",
      age: "4-6 years", 
      time: "2:00 PM - 4:00 PM",
      description: "Introduction to basic computer skills and digital literacy",
      image: "/assets/images/common/sc-program2.jpg"
    },
    {
      id: 3,
      title: "Basic English JR",
      age: "3-4 years",
      time: "10:00 AM - 12:00 PM", 
      description: "Foundation English language learning through fun activities",
      image: "/assets/images/common/sc-program3.jpg"
    },
    {
      id: 4,
      title: "Music & Dance",
      age: "3-6 years",
      time: "3:00 PM - 5:00 PM",
      description: "Develop rhythm, coordination and musical appreciation",
      image: "/assets/images/common/sc-program4.jpg"
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex justify-between items-center">
            <Link href="/" className="text-2xl font-bold text-blue-600">
              Kinco School
            </Link>
            <nav className="hidden md:flex space-x-6">
              <Link href="/about" className="text-gray-600 hover:text-blue-600">About</Link>
              <Link href="/classes" className="text-blue-600 font-semibold">Classes</Link>
              <Link href="/contact" className="text-gray-600 hover:text-blue-600">Contact</Link>
            </nav>
          </div>
        </div>
      </header>

      {/* Page Banner */}
      <section className="bg-gradient-to-r from-blue-500 to-purple-600 text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl font-bold mb-4">Our Classes</h1>
          <nav className="text-blue-200">
            <Link href="/" className="hover:text-white">Home</Link>
            <span className="mx-2">/</span>
            <span>Classes</span>
          </nav>
        </div>
      </section>

      {/* Classes Grid */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold mb-4 text-gray-800">Available Classes</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Discover our comprehensive range of classes designed to nurture your child's development
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-2 gap-8">
            {classes.map((classItem) => (
              <div key={classItem.id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                <img 
                  src={classItem.image} 
                  alt={classItem.title}
                  className="w-full h-48 object-cover"
                />
                <div className="p-6">
                  <h3 className="text-xl font-bold mb-2 text-gray-800">{classItem.title}</h3>
                  <div className="flex justify-between text-sm text-gray-500 mb-3">
                    <span>Age: {classItem.age}</span>
                    <span>Time: {classItem.time}</span>
                  </div>
                  <p className="text-gray-600 mb-4">{classItem.description}</p>
                  <Link 
                    href={`/classes/${classItem.id}`}
                    className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors inline-block"
                  >
                    Learn More
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-blue-600 text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">Ready to Enroll Your Child?</h2>
          <p className="text-xl mb-8 text-blue-100">
            Join our community of happy families and give your child the best start in life
          </p>
          <Link 
            href="/contact" 
            className="bg-yellow-400 text-blue-900 px-8 py-3 rounded-lg font-semibold hover:bg-yellow-300 transition-colors"
          >
            Contact Us Today
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-800 text-white py-8">
        <div className="container mx-auto px-4 text-center">
          <div className="mb-4">
            <Link href="/" className="text-2xl font-bold text-blue-400">
              Kinco School
            </Link>
          </div>
          <p className="text-gray-400 mb-4">
            Providing quality education and care for your children
          </p>
          <div className="flex justify-center space-x-6">
            <Link href="/about" className="text-gray-400 hover:text-white">About</Link>
            <Link href="/classes" className="text-white">Classes</Link>
            <Link href="/contact" className="text-gray-400 hover:text-white">Contact</Link>
          </div>
          <div className="mt-6 pt-6 border-t border-gray-700">
            <p className="text-gray-400">© 2024 Kinco School. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
