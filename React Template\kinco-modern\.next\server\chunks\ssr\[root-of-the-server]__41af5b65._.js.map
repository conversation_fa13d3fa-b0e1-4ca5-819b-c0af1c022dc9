{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kinco-day-care-kindergarten-react-template-30-06-2025/Kinco/React%20Template/kinco-modern/src/components/Header.js"], "sourcesContent": ["'use client';\n\nimport Link from \"next/link\";\nimport { usePathname } from \"next/navigation\";\n\nexport default function Header() {\n  const pathname = usePathname();\n\n  const isActive = (path) => {\n    return pathname === path;\n  };\n\n  return (\n    <header className=\"header-style-1 bg-white shadow-lg fixed-top\">\n      <div className=\"container-fluid\">\n        <div className=\"row\">\n          <div className=\"col-12\">\n            <div className=\"header-inner d-flex justify-content-between align-items-center py-3\">\n              <div className=\"header-logo\">\n                <Link href=\"/\" className=\"d-flex align-items-center text-decoration-none\">\n                  <div className=\"logo-placeholder d-flex align-items-center\">\n                    <div className=\"logo-icon bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2\"\n                         style={{width: '40px', height: '40px'}}>\n                      <i className=\"fa fa-graduation-cap\"></i>\n                    </div>\n                    <div>\n                      <h4 className=\"mb-0 text-primary fw-bold\">Smart</h4>\n                      <small className=\"text-muted\">Kinco</small>\n                    </div>\n                  </div>\n                </Link>\n              </div>\n              <nav className=\"main-menu d-none d-lg-block\">\n                <ul className=\"d-flex list-unstyled mb-0 align-items-center\">\n                  <li className=\"menu-item\">\n                    <Link href=\"/\" className={`menu-link text-decoration-none px-3 py-2 rounded-pill transition-all ${isActive('/') ? 'fw-semibold text-white bg-primary' : 'text-dark hover-primary'}`}>\n                      Home\n                    </Link>\n                  </li>\n                  <li className=\"menu-item\">\n                    <Link href=\"/about\" className={`menu-link text-decoration-none px-3 py-2 rounded-pill transition-all ${isActive('/about') ? 'fw-semibold text-white bg-primary' : 'text-dark hover-primary'}`}>\n                      About\n                    </Link>\n                  </li>\n                  <li className=\"menu-item\">\n                    <Link href=\"/classes\" className={`menu-link text-decoration-none px-3 py-2 rounded-pill transition-all ${isActive('/classes') ? 'fw-semibold text-white bg-primary' : 'text-dark hover-primary'}`}>\n                      Classes\n                    </Link>\n                  </li>\n                  <li className=\"menu-item\">\n                    <Link href=\"/program\" className={`menu-link text-decoration-none px-3 py-2 rounded-pill transition-all ${isActive('/program') ? 'fw-semibold text-white bg-primary' : 'text-dark hover-primary'}`}>\n                      Programs\n                    </Link>\n                  </li>\n                  <li className=\"menu-item\">\n                    <Link href=\"/teacher\" className={`menu-link text-decoration-none px-3 py-2 rounded-pill transition-all ${isActive('/teacher') ? 'fw-semibold text-white bg-primary' : 'text-dark hover-primary'}`}>\n                      Teachers\n                    </Link>\n                  </li>\n                  <li className=\"menu-item\">\n                    <Link href=\"/gallery\" className={`menu-link text-decoration-none px-3 py-2 rounded-pill transition-all ${isActive('/gallery') ? 'fw-semibold text-white bg-primary' : 'text-dark hover-primary'}`}>\n                      Gallery\n                    </Link>\n                  </li>\n                  <li className=\"menu-item\">\n                    <Link href=\"/blog\" className={`menu-link text-decoration-none px-3 py-2 rounded-pill transition-all ${isActive('/blog') ? 'fw-semibold text-white bg-primary' : 'text-dark hover-primary'}`}>\n                      Blog\n                    </Link>\n                  </li>\n                  <li className=\"menu-item\">\n                    <Link href=\"/events\" className={`menu-link text-decoration-none px-3 py-2 rounded-pill transition-all ${isActive('/events') ? 'fw-semibold text-white bg-primary' : 'text-dark hover-primary'}`}>\n                      Events\n                    </Link>\n                  </li>\n                  <li className=\"menu-item\">\n                    <Link href=\"/contact\" className={`menu-link text-decoration-none px-3 py-2 rounded-pill transition-all ${isActive('/contact') ? 'fw-semibold text-white bg-primary' : 'text-dark hover-primary'}`}>\n                      Contact\n                    </Link>\n                  </li>\n                </ul>\n              </nav>\n              <div className=\"header-mobile d-lg-none\">\n                <button className=\"mobile-menu-toggle btn btn-primary\">\n                  <i className=\"fa fa-bars\"></i>\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,WAAW,CAAC;QAChB,OAAO,aAAa;IACtB;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CACvB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;gDACV,OAAO;oDAAC,OAAO;oDAAQ,QAAQ;gDAAM;0DACxC,cAAA,8OAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA4B;;;;;;kEAC1C,8OAAC;wDAAM,WAAU;kEAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAKtC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAI,WAAW,CAAC,qEAAqE,EAAE,SAAS,OAAO,sCAAsC,2BAA2B;0DAAE;;;;;;;;;;;sDAIvL,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAW,CAAC,qEAAqE,EAAE,SAAS,YAAY,sCAAsC,2BAA2B;0DAAE;;;;;;;;;;;sDAIjM,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAW,CAAC,qEAAqE,EAAE,SAAS,cAAc,sCAAsC,2BAA2B;0DAAE;;;;;;;;;;;sDAIrM,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAW,CAAC,qEAAqE,EAAE,SAAS,cAAc,sCAAsC,2BAA2B;0DAAE;;;;;;;;;;;sDAIrM,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAW,CAAC,qEAAqE,EAAE,SAAS,cAAc,sCAAsC,2BAA2B;0DAAE;;;;;;;;;;;sDAIrM,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAW,CAAC,qEAAqE,EAAE,SAAS,cAAc,sCAAsC,2BAA2B;0DAAE;;;;;;;;;;;sDAIrM,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAW,CAAC,qEAAqE,EAAE,SAAS,WAAW,sCAAsC,2BAA2B;0DAAE;;;;;;;;;;;sDAI/L,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAU,WAAW,CAAC,qEAAqE,EAAE,SAAS,aAAa,sCAAsC,2BAA2B;0DAAE;;;;;;;;;;;sDAInM,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAW,CAAC,qEAAqE,EAAE,SAAS,cAAc,sCAAsC,2BAA2B;0DAAE;;;;;;;;;;;;;;;;;;;;;;0CAMzM,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAO,WAAU;8CAChB,cAAA,8OAAC;wCAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS/B", "debugId": null}}, {"offset": {"line": 345, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kinco-day-care-kindergarten-react-template-30-06-2025/Kinco/React%20Template/kinco-modern/src/components/Footer.js"], "sourcesContent": ["'use client';\n\nimport Link from \"next/link\";\n\nexport default function Footer() {\n  return (\n    <footer className=\"footer-style-2 bg-dark text-white\">\n      <div className=\"container\">\n        <div className=\"footer-content py-5\">\n          <div className=\"row g-4\">\n            <div className=\"col-lg-3 col-md-6\">\n              <div className=\"footer-widget\">\n                <div className=\"footer-logo mb-4\">\n                  <Link href=\"/\">\n                    <img src=\"/assets/images/logo/logofootert.png\" alt=\"Kinco School\" className=\"img-fluid\" style={{maxHeight: '50px'}} />\n                  </Link>\n                </div>\n                <p className=\"text-white-50 mb-4\">\n                  Providing quality education and care for your children in a safe, nurturing environment.\n                </p>\n                <div className=\"footer-social d-flex gap-3\">\n                  <a href=\"#\" className=\"social-link text-white-50 hover-primary\">\n                    <i className=\"fa fa-facebook\"></i>\n                  </a>\n                  <a href=\"#\" className=\"social-link text-white-50 hover-primary\">\n                    <i className=\"fa fa-twitter\"></i>\n                  </a>\n                  <a href=\"#\" className=\"social-link text-white-50 hover-primary\">\n                    <i className=\"fa fa-instagram\"></i>\n                  </a>\n                  <a href=\"#\" className=\"social-link text-white-50 hover-primary\">\n                    <i className=\"fa fa-linkedin\"></i>\n                  </a>\n                </div>\n              </div>\n            </div>\n            <div className=\"col-lg-3 col-md-6\">\n              <div className=\"footer-widget\">\n                <h3 className=\"footer-title h5 fw-bold mb-4\">Quick Links</h3>\n                <ul className=\"footer-menu list-unstyled\">\n                  <li className=\"mb-2\"><Link href=\"/about\" className=\"text-white-50 text-decoration-none hover-primary\">About Us</Link></li>\n                  <li className=\"mb-2\"><Link href=\"/classes\" className=\"text-white-50 text-decoration-none hover-primary\">Classes</Link></li>\n                  <li className=\"mb-2\"><Link href=\"/program\" className=\"text-white-50 text-decoration-none hover-primary\">Programs</Link></li>\n                  <li className=\"mb-2\"><Link href=\"/teacher\" className=\"text-white-50 text-decoration-none hover-primary\">Teachers</Link></li>\n                  <li className=\"mb-2\"><Link href=\"/gallery\" className=\"text-white-50 text-decoration-none hover-primary\">Gallery</Link></li>\n                </ul>\n              </div>\n            </div>\n            <div className=\"col-lg-3 col-md-6\">\n              <div className=\"footer-widget\">\n                <h3 className=\"footer-title h5 fw-bold mb-4\">Programs</h3>\n                <ul className=\"footer-menu list-unstyled\">\n                  <li className=\"mb-2\"><a href=\"#\" className=\"text-white-50 text-decoration-none hover-primary\">Drawing & Painting</a></li>\n                  <li className=\"mb-2\"><a href=\"#\" className=\"text-white-50 text-decoration-none hover-primary\">Computer Learning</a></li>\n                  <li className=\"mb-2\"><a href=\"#\" className=\"text-white-50 text-decoration-none hover-primary\">Basic English</a></li>\n                  <li className=\"mb-2\"><a href=\"#\" className=\"text-white-50 text-decoration-none hover-primary\">Music & Dance</a></li>\n                  <li className=\"mb-2\"><a href=\"#\" className=\"text-white-50 text-decoration-none hover-primary\">Sports Activities</a></li>\n                </ul>\n              </div>\n            </div>\n            <div className=\"col-lg-3 col-md-6\">\n              <div className=\"footer-widget\">\n                <h3 className=\"footer-title h5 fw-bold mb-4\">Contact Info</h3>\n                <div className=\"footer-contact\">\n                  <div className=\"contact-item d-flex align-items-start mb-3\">\n                    <i className=\"fa fa-map-marker text-primary me-3 mt-1\"></i>\n                    <span className=\"text-white-50\">123 Education Street, Learning City, LC 12345</span>\n                  </div>\n                  <div className=\"contact-item d-flex align-items-center mb-3\">\n                    <i className=\"fa fa-phone text-primary me-3\"></i>\n                    <span className=\"text-white-50\">(*************</span>\n                  </div>\n                  <div className=\"contact-item d-flex align-items-center mb-3\">\n                    <i className=\"fa fa-envelope text-primary me-3\"></i>\n                    <span className=\"text-white-50\"><EMAIL></span>\n                  </div>\n                  <div className=\"contact-item d-flex align-items-center\">\n                    <i className=\"fa fa-clock-o text-primary me-3\"></i>\n                    <span className=\"text-white-50\">Mon-Fri: 7AM-6PM</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n        <div className=\"footer-bottom border-top border-secondary pt-4\">\n          <div className=\"row align-items-center\">\n            <div className=\"col-12 text-center\">\n              <p className=\"text-white-50 mb-0\">© 2024 Kinco School. All rights reserved.</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;0DACT,cAAA,8OAAC;oDAAI,KAAI;oDAAsC,KAAI;oDAAe,WAAU;oDAAY,OAAO;wDAAC,WAAW;oDAAM;;;;;;;;;;;;;;;;sDAGrH,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAGlC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,MAAK;oDAAI,WAAU;8DACpB,cAAA,8OAAC;wDAAE,WAAU;;;;;;;;;;;8DAEf,8OAAC;oDAAE,MAAK;oDAAI,WAAU;8DACpB,cAAA,8OAAC;wDAAE,WAAU;;;;;;;;;;;8DAEf,8OAAC;oDAAE,MAAK;oDAAI,WAAU;8DACpB,cAAA,8OAAC;wDAAE,WAAU;;;;;;;;;;;8DAEf,8OAAC;oDAAE,MAAK;oDAAI,WAAU;8DACpB,cAAA,8OAAC;wDAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAKrB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA+B;;;;;;sDAC7C,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAG,WAAU;8DAAO,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAS,WAAU;kEAAmD;;;;;;;;;;;8DACtG,8OAAC;oDAAG,WAAU;8DAAO,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;kEAAmD;;;;;;;;;;;8DACxG,8OAAC;oDAAG,WAAU;8DAAO,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;kEAAmD;;;;;;;;;;;8DACxG,8OAAC;oDAAG,WAAU;8DAAO,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;kEAAmD;;;;;;;;;;;8DACxG,8OAAC;oDAAG,WAAU;8DAAO,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;kEAAmD;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAI9G,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA+B;;;;;;sDAC7C,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAG,WAAU;8DAAO,cAAA,8OAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAmD;;;;;;;;;;;8DAC9F,8OAAC;oDAAG,WAAU;8DAAO,cAAA,8OAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAmD;;;;;;;;;;;8DAC9F,8OAAC;oDAAG,WAAU;8DAAO,cAAA,8OAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAmD;;;;;;;;;;;8DAC9F,8OAAC;oDAAG,WAAU;8DAAO,cAAA,8OAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAmD;;;;;;;;;;;8DAC9F,8OAAC;oDAAG,WAAU;8DAAO,cAAA,8OAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAmD;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAIpG,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA+B;;;;;;sDAC7C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;;;;;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;;8DAElC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;;;;;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;;8DAElC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;;;;;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;;8DAElC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;;;;;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAO5C,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOhD", "debugId": null}}, {"offset": {"line": 896, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kinco-day-care-kindergarten-react-template-30-06-2025/Kinco/React%20Template/kinco-modern/src/app/page.js"], "sourcesContent": ["'use client';\n\nimport { useEffect } from \"react\";\nimport Link from \"next/link\";\nimport { Swiper, SwiperSlide } from \"swiper/react\";\nimport { Navigation, Pagination, Autoplay } from \"swiper/modules\";\nimport Header from \"../components/Header\";\nimport Footer from \"../components/Footer\";\n\nexport default function Home() {\n  useEffect(() => {\n    // Basic setup\n    document.querySelector(\"body\").className = \"main\";\n  }, []);\n\n  const programs = [\n    {\n      id: 1,\n      title: \"Drawing & Painting\",\n      description: \"Creative art classes to develop imagination and fine motor skills\",\n      icon: \"fa-paint-brush\",\n      color: \"primary\"\n    },\n    {\n      id: 2,\n      title: \"Computer Learning\",\n      description: \"Introduction to basic computer skills and digital literacy\",\n      icon: \"fa-laptop\",\n      color: \"info\"\n    },\n    {\n      id: 3,\n      title: \"Basic English JR\",\n      description: \"Foundation English language learning through fun activities\",\n      icon: \"fa-book\",\n      color: \"success\"\n    },\n    {\n      id: 4,\n      title: \"Music & Dance\",\n      description: \"Develop rhythm, coordination and musical appreciation\",\n      icon: \"fa-music\",\n      color: \"warning\"\n    }\n  ];\n\n  return (\n    <div className=\"min-h-screen\">\n      {/* Header */}\n      <Header />\n\n      {/* Hero Slider */}\n      <section className=\"hero-section\" style={{ paddingTop: '80px' }}>\n        <Swiper\n          modules={[Navigation, Pagination, Autoplay]}\n          spaceBetween={0}\n          slidesPerView={1}\n          navigation={{\n            nextEl: '.swiper-button-next',\n            prevEl: '.swiper-button-prev',\n          }}\n          pagination={{\n            clickable: true,\n            el: '.swiper-pagination'\n          }}\n          autoplay={{ delay: 5000, disableOnInteraction: false }}\n          loop={true}\n          className=\"hero-swiper\"\n          style={{ height: '100vh' }}\n        >\n          <SwiperSlide>\n            <div\n              className=\"hero-slide d-flex align-items-center\"\n              style={{\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                minHeight: '100vh',\n                position: 'relative'\n              }}\n            >\n              <div className=\"container\">\n                <div className=\"row align-items-center\">\n                  <div className=\"col-lg-6\">\n                    <div className=\"hero-content text-white\">\n                      <p className=\"hero-subtitle text-warning fw-semibold mb-3 fs-5\">🌟 We Care Child Study</p>\n                      <h1 className=\"hero-title display-3 fw-bold mb-4\">\n                        Best Care For Your <span className=\"text-warning\">Little Ones</span>\n                      </h1>\n                      <p className=\"hero-description lead mb-4\">\n                        Providing a safe, nurturing environment where children can learn, grow,\n                        and develop their full potential through innovative teaching methods.\n                      </p>\n                      <ul className=\"hero-features list-unstyled mb-4\">\n                        <li className=\"mb-2 fs-5\">\n                          <i className=\"fa fa-check-circle text-success me-3\"></i>\n                          <span>🎮 Outdoor Games & Activities</span>\n                        </li>\n                        <li className=\"mb-2 fs-5\">\n                          <i className=\"fa fa-check-circle text-success me-3\"></i>\n                          <span>🏃 Sport & Physical Development</span>\n                        </li>\n                        <li className=\"mb-2 fs-5\">\n                          <i className=\"fa fa-check-circle text-success me-3\"></i>\n                          <span>🥗 Nutritious & Healthy Meals</span>\n                        </li>\n                      </ul>\n                      <div className=\"hero-buttons\">\n                        <Link href=\"/contact\" className=\"btn btn-warning btn-lg me-3 px-5 py-3 rounded-pill fw-semibold\">\n                          📞 Contact Us\n                        </Link>\n                        <Link href=\"/about\" className=\"btn btn-outline-light btn-lg px-5 py-3 rounded-pill fw-semibold\">\n                          📚 Learn More\n                        </Link>\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"col-lg-6\">\n                    <div className=\"hero-image text-center\">\n                      <div\n                        className=\"hero-image-placeholder d-flex align-items-center justify-content-center rounded-3 shadow-lg\"\n                        style={{\n                          height: '500px',\n                          background: 'linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.1) 100%)',\n                          border: '2px dashed rgba(255,255,255,0.3)'\n                        }}\n                      >\n                        <div className=\"text-center text-white\">\n                          <i className=\"fa fa-graduation-cap\" style={{fontSize: '4rem', marginBottom: '1rem'}}></i>\n                          <h4 className=\"fw-bold\">Happy Children Learning</h4>\n                          <p className=\"mb-0\">Building bright futures together</p>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </SwiperSlide>\n\n          <SwiperSlide>\n            <div\n              className=\"hero-slide d-flex align-items-center\"\n              style={{\n                background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',\n                minHeight: '100vh',\n                position: 'relative'\n              }}\n            >\n              <div className=\"container\">\n                <div className=\"row align-items-center\">\n                  <div className=\"col-lg-6\">\n                    <div className=\"hero-content text-white\">\n                      <p className=\"hero-subtitle text-warning fw-semibold mb-3 fs-5\">🎓 Quality Education</p>\n                      <h1 className=\"hero-title display-3 fw-bold mb-4\">\n                        Start Learning With <span className=\"text-warning\">Smart Kinco</span>\n                      </h1>\n                      <p className=\"hero-description lead mb-4\">\n                        Building bright futures through innovative teaching methods, creative learning\n                        experiences, and a nurturing environment where every child can thrive.\n                      </p>\n                      <div className=\"hero-buttons\">\n                        <Link href=\"/contact\" className=\"btn btn-warning btn-lg me-3 px-5 py-3 rounded-pill fw-semibold\">\n                          📞 Contact Us\n                        </Link>\n                        <Link href=\"/program\" className=\"btn btn-outline-light btn-lg px-5 py-3 rounded-pill fw-semibold\">\n                          🎯 Our Programs\n                        </Link>\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"col-lg-6\">\n                    <div className=\"hero-image text-center\">\n                      <div\n                        className=\"hero-image-placeholder d-flex align-items-center justify-content-center rounded-3 shadow-lg\"\n                        style={{\n                          height: '500px',\n                          background: 'linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.1) 100%)',\n                          border: '2px dashed rgba(255,255,255,0.3)'\n                        }}\n                      >\n                        <div className=\"text-center text-white\">\n                          <i className=\"fa fa-heart\" style={{fontSize: '4rem', marginBottom: '1rem'}}></i>\n                          <h4 className=\"fw-bold\">Caring Environment</h4>\n                          <p className=\"mb-0\">Where every child feels loved</p>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </SwiperSlide>\n\n          {/* Navigation */}\n          <div className=\"swiper-button-next\"></div>\n          <div className=\"swiper-button-prev\"></div>\n          <div className=\"swiper-pagination\"></div>\n        </Swiper>\n      </section>\n\n      {/* Discovery/Features Section */}\n      <section className=\"features-section py-5 bg-light\">\n        <div className=\"container\">\n          <div className=\"row\">\n            <div className=\"col-12\">\n              <div className=\"text-center mb-5\">\n                <div className=\"d-flex align-items-center justify-content-center mb-3\">\n                  <div className=\"me-3\">\n                    <i className=\"fa fa-graduation-cap text-primary\" style={{fontSize: '2rem'}}></i>\n                  </div>\n                  <p className=\"text-primary fw-semibold mb-0 fs-5\">Why Choose Us</p>\n                </div>\n                <h2 className=\"display-4 fw-bold mb-4\">Why Choose Kinco School?</h2>\n                <p className=\"text-muted lead mx-auto\" style={{maxWidth: '600px'}}>\n                  We offer comprehensive programs designed to nurture your child's development\n                </p>\n              </div>\n              <div className=\"row g-4\">\n                <div className=\"col-lg-3 col-md-6\">\n                  <div className=\"feature-card bg-white p-4 rounded-3 shadow-sm text-center h-100 border-0\">\n                    <div className=\"feature-icon mb-4\">\n                      <div className=\"icon-circle bg-primary bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center\" style={{width: '80px', height: '80px'}}>\n                        <i className=\"fa fa-gamepad text-primary\" style={{fontSize: '2rem'}}></i>\n                      </div>\n                    </div>\n                    <h3 className=\"h5 fw-bold mb-3\">Study & Game</h3>\n                    <p className=\"text-muted mb-4\">Sed ut perspiciatis unde omnis iste natu voluptatem accus antium dolorem</p>\n                    <Link href=\"/classes\" className=\"btn btn-outline-primary btn-sm rounded-pill\">\n                      Read More <i className=\"fa fa-arrow-right ms-1\"></i>\n                    </Link>\n                  </div>\n                </div>\n                <div className=\"col-lg-3 col-md-6\">\n                  <div className=\"feature-card bg-white p-4 rounded-3 shadow-sm text-center h-100 border-0\">\n                    <div className=\"feature-icon mb-4\">\n                      <div className=\"icon-circle bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center\" style={{width: '80px', height: '80px'}}>\n                        <i className=\"fa fa-book text-success\" style={{fontSize: '2rem'}}></i>\n                      </div>\n                    </div>\n                    <h3 className=\"h5 fw-bold mb-3\">A to Z Programs</h3>\n                    <p className=\"text-muted mb-4\">Sed ut perspiciatis unde omnis iste natu voluptatem accus antium dolorem</p>\n                    <Link href=\"/program\" className=\"btn btn-outline-success btn-sm rounded-pill\">\n                      Read More <i className=\"fa fa-arrow-right ms-1\"></i>\n                    </Link>\n                  </div>\n                </div>\n                <div className=\"col-lg-3 col-md-6\">\n                  <div className=\"feature-card bg-white p-4 rounded-3 shadow-sm text-center h-100 border-0\">\n                    <div className=\"feature-icon mb-4\">\n                      <div className=\"icon-circle bg-info bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center\" style={{width: '80px', height: '80px'}}>\n                        <i className=\"fa fa-user text-info\" style={{fontSize: '2rem'}}></i>\n                      </div>\n                    </div>\n                    <h3 className=\"h5 fw-bold mb-3\">Expert Teacher</h3>\n                    <p className=\"text-muted mb-4\">Sed ut perspiciatis unde omnis iste natu voluptatem accus antium dolorem</p>\n                    <Link href=\"/teacher\" className=\"btn btn-outline-info btn-sm rounded-pill\">\n                      Read More <i className=\"fa fa-arrow-right ms-1\"></i>\n                    </Link>\n                  </div>\n                </div>\n                <div className=\"col-lg-3 col-md-6\">\n                  <div className=\"feature-card bg-white p-4 rounded-3 shadow-sm text-center h-100 border-0\">\n                    <div className=\"feature-icon mb-4\">\n                      <div className=\"icon-circle bg-warning bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center\" style={{width: '80px', height: '80px'}}>\n                        <i className=\"fa fa-trophy text-warning\" style={{fontSize: '2rem'}}></i>\n                      </div>\n                    </div>\n                    <h3 className=\"h5 fw-bold mb-3\">Best Awards</h3>\n                    <p className=\"text-muted mb-4\">Sed ut perspiciatis unde omnis iste natu voluptatem accus antium dolorem</p>\n                    <Link href=\"/about\" className=\"btn btn-outline-warning btn-sm rounded-pill\">\n                      Read More <i className=\"fa fa-arrow-right ms-1\"></i>\n                    </Link>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* About Section */}\n      <section className=\"about-section py-5\">\n        <div className=\"container\">\n          <div className=\"row align-items-center\">\n            <div className=\"col-lg-6 mb-4 mb-lg-0\">\n              <div className=\"about-content\">\n                <div className=\"d-flex align-items-center mb-3\">\n                  <div className=\"me-3\">\n                    <i className=\"fa fa-heart text-primary\" style={{fontSize: '2rem'}}></i>\n                  </div>\n                  <p className=\"text-primary fw-semibold mb-0 fs-5\">About Kinco</p>\n                </div>\n                <h2 className=\"display-5 fw-bold mb-4\">\n                  We Provide The Best Education For Your Children\n                </h2>\n                <p className=\"text-muted mb-4 lead\">\n                  Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut elit tellus,\n                  luctus nec ullamcorper mattis, pulvinar dapibus leo. Sed do eiusmod tempor\n                  incididunt ut labore et dolore magna aliqua.\n                </p>\n                <p className=\"text-muted mb-4\">\n                  Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut\n                  aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit.\n                </p>\n                <div className=\"about-features mb-4\">\n                  <div className=\"row g-3\">\n                    <div className=\"col-6\">\n                      <div className=\"feature-item d-flex align-items-center\">\n                        <i className=\"fa fa-check-circle text-success me-2\"></i>\n                        <span>Safe Environment</span>\n                      </div>\n                    </div>\n                    <div className=\"col-6\">\n                      <div className=\"feature-item d-flex align-items-center\">\n                        <i className=\"fa fa-check-circle text-success me-2\"></i>\n                        <span>Expert Teachers</span>\n                      </div>\n                    </div>\n                    <div className=\"col-6\">\n                      <div className=\"feature-item d-flex align-items-center\">\n                        <i className=\"fa fa-check-circle text-success me-2\"></i>\n                        <span>Quality Education</span>\n                      </div>\n                    </div>\n                    <div className=\"col-6\">\n                      <div className=\"feature-item d-flex align-items-center\">\n                        <i className=\"fa fa-check-circle text-success me-2\"></i>\n                        <span>Fun Activities</span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n                <Link href=\"/about\" className=\"btn btn-primary btn-lg rounded-pill px-4\">\n                  Learn More <i className=\"fa fa-arrow-right ms-2\"></i>\n                </Link>\n              </div>\n            </div>\n            <div className=\"col-lg-6\">\n              <div className=\"about-image position-relative\">\n                <div\n                  className=\"about-image-placeholder d-flex align-items-center justify-content-center rounded-3 shadow-lg\"\n                  style={{\n                    height: '400px',\n                    background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',\n                    border: '2px dashed #dee2e6'\n                  }}\n                >\n                  <div className=\"text-center text-muted\">\n                    <i className=\"fa fa-users\" style={{fontSize: '4rem', marginBottom: '1rem'}}></i>\n                    <h4 className=\"fw-bold\">About Our School</h4>\n                    <p className=\"mb-0\">Creating memorable learning experiences</p>\n                  </div>\n                </div>\n                <div className=\"experience-badge position-absolute bg-warning text-dark p-4 rounded-3 shadow-lg\"\n                     style={{bottom: '-30px', right: '-30px', minWidth: '120px'}}>\n                  <div className=\"text-center\">\n                    <div className=\"h2 fw-bold mb-1\">15+</div>\n                    <div className=\"small fw-semibold\">Years Experience</div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Latest Programs Section */}\n      <section className=\"tf-section tf-latest-program bg-light\">\n        <div className=\"container\">\n          <div className=\"row\">\n            <div className=\"col-12\">\n              <div className=\"latest-program-1\">\n                <div className=\"text-center mb-5\">\n                  <div className=\"box-sub-tag d-flex align-items-center justify-content-center mb-3\">\n                    <div className=\"sub-tag-icon me-3\">\n                      <i className=\"flaticon-book text-primary fs-2\"></i>\n                    </div>\n                    <div className=\"sub-tag-title\">\n                      <p className=\"text-primary fw-semibold mb-0\">Latest Programs</p>\n                    </div>\n                  </div>\n                  <h2 className=\"h1 fw-bold mb-4\">Our Latest Programs</h2>\n                  <p className=\"text-muted lead\">\n                    Discover our comprehensive range of educational programs designed to nurture your child's development\n                  </p>\n                </div>\n                <div className=\"row g-4\">\n                  {programs.map((program) => (\n                    <div key={program.id} className=\"col-lg-3 col-md-6\">\n                      <div className=\"program-item bg-white rounded shadow-sm overflow-hidden h-100 border-0\">\n                        <div className=\"program-image-placeholder d-flex align-items-center justify-content-center\"\n                             style={{height: '200px', background: `var(--bs-${program.color})`}}>\n                          <div className=\"text-center text-white\">\n                            <i className={`fa ${program.icon}`} style={{fontSize: '3rem'}}></i>\n                          </div>\n                        </div>\n                        <div className=\"program-content p-4\">\n                          <h3 className=\"h5 fw-bold mb-3\">{program.title}</h3>\n                          <p className=\"text-muted mb-3\">{program.description}</p>\n                          <Link\n                            href=\"/classes\"\n                            className={`text-${program.color} text-decoration-none fw-semibold`}\n                          >\n                            Learn More <i className=\"fa fa-arrow-right ms-1\"></i>\n                          </Link>\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Statistics Section */}\n      <section className=\"tf-section tf-counter bg-primary text-white\">\n        <div className=\"container\">\n          <div className=\"row\">\n            <div className=\"col-12\">\n              <div className=\"counter-1\">\n                <div className=\"row g-4 text-center\">\n                  <div className=\"col-lg-3 col-md-6\">\n                    <div className=\"counter-item\">\n                      <div className=\"counter-number h1 fw-bold mb-2\">500+</div>\n                      <div className=\"counter-title text-white-50\">Happy Students</div>\n                    </div>\n                  </div>\n                  <div className=\"col-lg-3 col-md-6\">\n                    <div className=\"counter-item\">\n                      <div className=\"counter-number h1 fw-bold mb-2\">50+</div>\n                      <div className=\"counter-title text-white-50\">Expert Teachers</div>\n                    </div>\n                  </div>\n                  <div className=\"col-lg-3 col-md-6\">\n                    <div className=\"counter-item\">\n                      <div className=\"counter-number h1 fw-bold mb-2\">15+</div>\n                      <div className=\"counter-title text-white-50\">Years Experience</div>\n                    </div>\n                  </div>\n                  <div className=\"col-lg-3 col-md-6\">\n                    <div className=\"counter-item\">\n                      <div className=\"counter-number h1 fw-bold mb-2\">25+</div>\n                      <div className=\"counter-title text-white-50\">Awards Won</div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"tf-section tf-cta\">\n        <div className=\"container\">\n          <div className=\"row\">\n            <div className=\"col-12\">\n              <div className=\"cta-1 text-center\">\n                <h2 className=\"h1 fw-bold mb-4\">Ready to Enroll Your Child?</h2>\n                <p className=\"lead text-muted mb-5 mx-auto\" style={{maxWidth: '600px'}}>\n                  Join our community of happy families and give your child the best start in life with our comprehensive educational programs.\n                </p>\n                <div className=\"cta-buttons\">\n                  <Link\n                    href=\"/contact\"\n                    className=\"tf-btn style-1 me-3\"\n                  >\n                    <span>Contact Us Today</span>\n                  </Link>\n                  <Link\n                    href=\"/about\"\n                    className=\"tf-btn style-2\"\n                  >\n                    <span>Learn More</span>\n                  </Link>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAPA;;;;;;;;AASe,SAAS;IACtB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,cAAc;QACd,SAAS,aAAa,CAAC,QAAQ,SAAS,GAAG;IAC7C,GAAG,EAAE;IAEL,MAAM,WAAW;QACf;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;QACT;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,2HAAA,CAAA,UAAM;;;;;0BAGP,8OAAC;gBAAQ,WAAU;gBAAe,OAAO;oBAAE,YAAY;gBAAO;0BAC5D,cAAA,8OAAC,0IAAA,CAAA,SAAM;oBACL,SAAS;wBAAC,yLAAA,CAAA,aAAU;wBAAE,yLAAA,CAAA,aAAU;wBAAE,qLAAA,CAAA,WAAQ;qBAAC;oBAC3C,cAAc;oBACd,eAAe;oBACf,YAAY;wBACV,QAAQ;wBACR,QAAQ;oBACV;oBACA,YAAY;wBACV,WAAW;wBACX,IAAI;oBACN;oBACA,UAAU;wBAAE,OAAO;wBAAM,sBAAsB;oBAAM;oBACrD,MAAM;oBACN,WAAU;oBACV,OAAO;wBAAE,QAAQ;oBAAQ;;sCAEzB,8OAAC,0IAAA,CAAA,cAAW;sCACV,cAAA,8OAAC;gCACC,WAAU;gCACV,OAAO;oCACL,YAAY;oCACZ,WAAW;oCACX,UAAU;gCACZ;0CAEA,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAAmD;;;;;;sEAChE,8OAAC;4DAAG,WAAU;;gEAAoC;8EAC7B,8OAAC;oEAAK,WAAU;8EAAe;;;;;;;;;;;;sEAEpD,8OAAC;4DAAE,WAAU;sEAA6B;;;;;;sEAI1C,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEAAG,WAAU;;sFACZ,8OAAC;4EAAE,WAAU;;;;;;sFACb,8OAAC;sFAAK;;;;;;;;;;;;8EAER,8OAAC;oEAAG,WAAU;;sFACZ,8OAAC;4EAAE,WAAU;;;;;;sFACb,8OAAC;sFAAK;;;;;;;;;;;;8EAER,8OAAC;oEAAG,WAAU;;sFACZ,8OAAC;4EAAE,WAAU;;;;;;sFACb,8OAAC;sFAAK;;;;;;;;;;;;;;;;;;sEAGV,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,4JAAA,CAAA,UAAI;oEAAC,MAAK;oEAAW,WAAU;8EAAiE;;;;;;8EAGjG,8OAAC,4JAAA,CAAA,UAAI;oEAAC,MAAK;oEAAS,WAAU;8EAAkE;;;;;;;;;;;;;;;;;;;;;;;0DAMtG,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDACC,WAAU;wDACV,OAAO;4DACL,QAAQ;4DACR,YAAY;4DACZ,QAAQ;wDACV;kEAEA,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAE,WAAU;oEAAuB,OAAO;wEAAC,UAAU;wEAAQ,cAAc;oEAAM;;;;;;8EAClF,8OAAC;oEAAG,WAAU;8EAAU;;;;;;8EACxB,8OAAC;oEAAE,WAAU;8EAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAUpC,8OAAC,0IAAA,CAAA,cAAW;sCACV,cAAA,8OAAC;gCACC,WAAU;gCACV,OAAO;oCACL,YAAY;oCACZ,WAAW;oCACX,UAAU;gCACZ;0CAEA,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAAmD;;;;;;sEAChE,8OAAC;4DAAG,WAAU;;gEAAoC;8EAC5B,8OAAC;oEAAK,WAAU;8EAAe;;;;;;;;;;;;sEAErD,8OAAC;4DAAE,WAAU;sEAA6B;;;;;;sEAI1C,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,4JAAA,CAAA,UAAI;oEAAC,MAAK;oEAAW,WAAU;8EAAiE;;;;;;8EAGjG,8OAAC,4JAAA,CAAA,UAAI;oEAAC,MAAK;oEAAW,WAAU;8EAAkE;;;;;;;;;;;;;;;;;;;;;;;0DAMxG,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDACC,WAAU;wDACV,OAAO;4DACL,QAAQ;4DACR,YAAY;4DACZ,QAAQ;wDACV;kEAEA,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAE,WAAU;oEAAc,OAAO;wEAAC,UAAU;wEAAQ,cAAc;oEAAM;;;;;;8EACzE,8OAAC;oEAAG,WAAU;8EAAU;;;;;;8EACxB,8OAAC;oEAAE,WAAU;8EAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAWpC,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;0BAKnB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAE,WAAU;wDAAoC,OAAO;4DAAC,UAAU;wDAAM;;;;;;;;;;;8DAE3E,8OAAC;oDAAE,WAAU;8DAAqC;;;;;;;;;;;;sDAEpD,8OAAC;4CAAG,WAAU;sDAAyB;;;;;;sDACvC,8OAAC;4CAAE,WAAU;4CAA0B,OAAO;gDAAC,UAAU;4CAAO;sDAAG;;;;;;;;;;;;8CAIrE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAA8G,OAAO;gEAAC,OAAO;gEAAQ,QAAQ;4DAAM;sEAChK,cAAA,8OAAC;gEAAE,WAAU;gEAA6B,OAAO;oEAAC,UAAU;gEAAM;;;;;;;;;;;;;;;;kEAGtE,8OAAC;wDAAG,WAAU;kEAAkB;;;;;;kEAChC,8OAAC;wDAAE,WAAU;kEAAkB;;;;;;kEAC/B,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;;4DAA8C;0EAClE,8OAAC;gEAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;sDAI7B,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAA8G,OAAO;gEAAC,OAAO;gEAAQ,QAAQ;4DAAM;sEAChK,cAAA,8OAAC;gEAAE,WAAU;gEAA0B,OAAO;oEAAC,UAAU;gEAAM;;;;;;;;;;;;;;;;kEAGnE,8OAAC;wDAAG,WAAU;kEAAkB;;;;;;kEAChC,8OAAC;wDAAE,WAAU;kEAAkB;;;;;;kEAC/B,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;;4DAA8C;0EAClE,8OAAC;gEAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;sDAI7B,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAA2G,OAAO;gEAAC,OAAO;gEAAQ,QAAQ;4DAAM;sEAC7J,cAAA,8OAAC;gEAAE,WAAU;gEAAuB,OAAO;oEAAC,UAAU;gEAAM;;;;;;;;;;;;;;;;kEAGhE,8OAAC;wDAAG,WAAU;kEAAkB;;;;;;kEAChC,8OAAC;wDAAE,WAAU;kEAAkB;;;;;;kEAC/B,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;;4DAA2C;0EAC/D,8OAAC;gEAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;sDAI7B,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAA8G,OAAO;gEAAC,OAAO;gEAAQ,QAAQ;4DAAM;sEAChK,cAAA,8OAAC;gEAAE,WAAU;gEAA4B,OAAO;oEAAC,UAAU;gEAAM;;;;;;;;;;;;;;;;kEAGrE,8OAAC;wDAAG,WAAU;kEAAkB;;;;;;kEAChC,8OAAC;wDAAE,WAAU;kEAAkB;;;;;;kEAC/B,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAS,WAAU;;4DAA8C;0EAChE,8OAAC;gEAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWvC,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAE,WAAU;wDAA2B,OAAO;4DAAC,UAAU;wDAAM;;;;;;;;;;;8DAElE,8OAAC;oDAAE,WAAU;8DAAqC;;;;;;;;;;;;sDAEpD,8OAAC;4CAAG,WAAU;sDAAyB;;;;;;sDAGvC,8OAAC;4CAAE,WAAU;sDAAuB;;;;;;sDAKpC,8OAAC;4CAAE,WAAU;sDAAkB;;;;;;sDAI/B,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAE,WAAU;;;;;;8EACb,8OAAC;8EAAK;;;;;;;;;;;;;;;;;kEAGV,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAE,WAAU;;;;;;8EACb,8OAAC;8EAAK;;;;;;;;;;;;;;;;;kEAGV,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAE,WAAU;;;;;;8EACb,8OAAC;8EAAK;;;;;;;;;;;;;;;;;kEAGV,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAE,WAAU;;;;;;8EACb,8OAAC;8EAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAKd,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAS,WAAU;;gDAA2C;8DAC5D,8OAAC;oDAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;0CAI9B,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,WAAU;4CACV,OAAO;gDACL,QAAQ;gDACR,YAAY;gDACZ,QAAQ;4CACV;sDAEA,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;wDAAc,OAAO;4DAAC,UAAU;4DAAQ,cAAc;wDAAM;;;;;;kEACzE,8OAAC;wDAAG,WAAU;kEAAU;;;;;;kEACxB,8OAAC;wDAAE,WAAU;kEAAO;;;;;;;;;;;;;;;;;sDAGxB,8OAAC;4CAAI,WAAU;4CACV,OAAO;gDAAC,QAAQ;gDAAS,OAAO;gDAAS,UAAU;4CAAO;sDAC7D,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAkB;;;;;;kEACjC,8OAAC;wDAAI,WAAU;kEAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUjD,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAE,WAAU;;;;;;;;;;;kEAEf,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAE,WAAU;sEAAgC;;;;;;;;;;;;;;;;;0DAGjD,8OAAC;gDAAG,WAAU;0DAAkB;;;;;;0DAChC,8OAAC;gDAAE,WAAU;0DAAkB;;;;;;;;;;;;kDAIjC,8OAAC;wCAAI,WAAU;kDACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;gDAAqB,WAAU;0DAC9B,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;4DACV,OAAO;gEAAC,QAAQ;gEAAS,YAAY,CAAC,SAAS,EAAE,QAAQ,KAAK,CAAC,CAAC,CAAC;4DAAA;sEACpE,cAAA,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAE,WAAW,CAAC,GAAG,EAAE,QAAQ,IAAI,EAAE;oEAAE,OAAO;wEAAC,UAAU;oEAAM;;;;;;;;;;;;;;;;sEAGhE,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EAAmB,QAAQ,KAAK;;;;;;8EAC9C,8OAAC;oEAAE,WAAU;8EAAmB,QAAQ,WAAW;;;;;;8EACnD,8OAAC,4JAAA,CAAA,UAAI;oEACH,MAAK;oEACL,WAAW,CAAC,KAAK,EAAE,QAAQ,KAAK,CAAC,iCAAiC,CAAC;;wEACpE;sFACY,8OAAC;4EAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;+CAftB,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BA6BlC,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAiC;;;;;;kEAChD,8OAAC;wDAAI,WAAU;kEAA8B;;;;;;;;;;;;;;;;;sDAGjD,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAiC;;;;;;kEAChD,8OAAC;wDAAI,WAAU;kEAA8B;;;;;;;;;;;;;;;;;sDAGjD,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAiC;;;;;;kEAChD,8OAAC;wDAAI,WAAU;kEAA8B;;;;;;;;;;;;;;;;;sDAGjD,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAiC;;;;;;kEAChD,8OAAC;wDAAI,WAAU;kEAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAW7D,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAkB;;;;;;kDAChC,8OAAC;wCAAE,WAAU;wCAA+B,OAAO;4CAAC,UAAU;wCAAO;kDAAG;;;;;;kDAGxE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DAEV,cAAA,8OAAC;8DAAK;;;;;;;;;;;0DAER,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DAEV,cAAA,8OAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUpB,8OAAC,2HAAA,CAAA,UAAM;;;;;;;;;;;AAGb", "debugId": null}}]}