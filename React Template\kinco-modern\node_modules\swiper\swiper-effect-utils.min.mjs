/**
 * Swiper 11.2.10
 * Most modern mobile touch slider and framework with hardware accelerated transitions
 * https://swiperjs.com
 *
 * Copyright 2014-2025 <PERSON>
 *
 * Released under the MIT License
 *
 * Released on: June 28, 2025
 */

export{c as createShadow}from"./shared/create-shadow.min.mjs";export{e as effectInit}from"./shared/effect-init.min.mjs";export{e as effectTarget}from"./shared/effect-target.min.mjs";export{e as effectVirtualTransitionEnd}from"./shared/effect-virtual-transition-end.min.mjs";export{g as getSlideTransformEl}from"./shared/utils.min.mjs";
//# sourceMappingURL=swiper-effect-utils.min.mjs.map