{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kinco-day-care-kindergarten-react-template-30-06-2025/Kinco/React%20Template/kinco-modern/src/app/gallery/page.js"], "sourcesContent": ["'use client';\n\nimport Link from \"next/link\";\nimport { useState } from \"react\";\n\nexport default function Gallery() {\n  const [selectedCategory, setSelectedCategory] = useState('all');\n\n  const categories = [\n    { id: 'all', name: 'All Photos' },\n    { id: 'classroom', name: 'Classroom' },\n    { id: 'activities', name: 'Activities' },\n    { id: 'events', name: 'Events' },\n    { id: 'outdoor', name: 'Outdoor Play' }\n  ];\n\n  const galleryItems = [\n    { id: 1, category: 'classroom', image: '/assets/images/common/sc-gallery-1.jpg', title: 'Reading Time' },\n    { id: 2, category: 'activities', image: '/assets/images/common/sc-gallery-2.jpg', title: 'Art Class' },\n    { id: 3, category: 'outdoor', image: '/assets/images/common/sc-gallery-3.jpg', title: 'Playground Fun' },\n    { id: 4, category: 'events', image: '/assets/images/common/sc-gallery-4.jpg', title: 'School Event' },\n    { id: 5, category: 'classroom', image: '/assets/images/common/sc-gallery-5.jpg', title: 'Learning Together' },\n    { id: 6, category: 'activities', image: '/assets/images/common/sc-gallery-6.jpg', title: 'Music Class' },\n    { id: 7, category: 'outdoor', image: '/assets/images/common/sc-gallery-7.jpg', title: 'Garden Time' },\n    { id: 8, category: 'events', image: '/assets/images/common/sc-gallery-8.jpg', title: 'Graduation Day' },\n    { id: 9, category: 'classroom', image: '/assets/images/common/sc-gallery-9.jpg', title: 'Science Exploration' },\n    { id: 10, category: 'activities', image: '/assets/images/common/sc-program1.jpg', title: 'Creative Play' },\n    { id: 11, category: 'outdoor', image: '/assets/images/common/sc-program2.jpg', title: 'Sports Day' },\n    { id: 12, category: 'events', image: '/assets/images/common/sc-program3.jpg', title: 'Parent Day' }\n  ];\n\n  const filteredItems = selectedCategory === 'all' \n    ? galleryItems \n    : galleryItems.filter(item => item.category === selectedCategory);\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm\">\n        <div className=\"container mx-auto px-4 py-4\">\n          <div className=\"flex justify-between items-center\">\n            <Link href=\"/\" className=\"text-2xl font-bold text-blue-600\">\n              Kinco School\n            </Link>\n            <nav className=\"hidden md:flex space-x-6\">\n              <Link href=\"/about\" className=\"text-gray-600 hover:text-blue-600\">About</Link>\n              <Link href=\"/classes\" className=\"text-gray-600 hover:text-blue-600\">Classes</Link>\n              <Link href=\"/gallery\" className=\"text-blue-600 font-semibold\">Gallery</Link>\n              <Link href=\"/contact\" className=\"text-gray-600 hover:text-blue-600\">Contact</Link>\n            </nav>\n          </div>\n        </div>\n      </header>\n\n      {/* Page Banner */}\n      <section className=\"bg-gradient-to-r from-blue-500 to-purple-600 text-white py-16\">\n        <div className=\"container mx-auto px-4 text-center\">\n          <h1 className=\"text-4xl font-bold mb-4\">Photo Gallery</h1>\n          <nav className=\"text-blue-200\">\n            <Link href=\"/\" className=\"hover:text-white\">Home</Link>\n            <span className=\"mx-2\">/</span>\n            <span>Gallery</span>\n          </nav>\n        </div>\n      </section>\n\n      {/* Gallery Section */}\n      <section className=\"py-16\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-4xl font-bold mb-4 text-gray-800\">Moments at Kinco School</h2>\n            <p className=\"text-gray-600 max-w-2xl mx-auto\">\n              Take a look at the wonderful moments and activities that happen every day at our school\n            </p>\n          </div>\n\n          {/* Filter Buttons */}\n          <div className=\"flex flex-wrap justify-center gap-4 mb-12\">\n            {categories.map((category) => (\n              <button\n                key={category.id}\n                onClick={() => setSelectedCategory(category.id)}\n                className={`px-6 py-2 rounded-full font-semibold transition-colors ${\n                  selectedCategory === category.id\n                    ? 'bg-blue-600 text-white'\n                    : 'bg-white text-gray-600 hover:bg-blue-50 hover:text-blue-600'\n                }`}\n              >\n                {category.name}\n              </button>\n            ))}\n          </div>\n\n          {/* Gallery Grid */}\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n            {filteredItems.map((item) => (\n              <div key={item.id} className=\"group relative overflow-hidden rounded-lg shadow-md hover:shadow-lg transition-shadow\">\n                <img \n                  src={item.image} \n                  alt={item.title}\n                  className=\"w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300\"\n                />\n                <div className=\"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-300 flex items-center justify-center\">\n                  <div className=\"text-white text-center opacity-0 group-hover:opacity-100 transition-opacity duration-300\">\n                    <h3 className=\"text-lg font-semibold mb-2\">{item.title}</h3>\n                    <button className=\"bg-white text-blue-600 px-4 py-2 rounded-lg font-semibold hover:bg-blue-50 transition-colors\">\n                      View\n                    </button>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n\n          {filteredItems.length === 0 && (\n            <div className=\"text-center py-12\">\n              <p className=\"text-gray-500 text-lg\">No photos found in this category.</p>\n            </div>\n          )}\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"bg-blue-600 text-white py-16\">\n        <div className=\"container mx-auto px-4 text-center\">\n          <h2 className=\"text-3xl font-bold mb-4\">Want to See More?</h2>\n          <p className=\"text-xl mb-8 text-blue-100\">\n            Visit our school to experience the joy and learning firsthand\n          </p>\n          <Link \n            href=\"/contact\" \n            className=\"bg-yellow-400 text-blue-900 px-8 py-3 rounded-lg font-semibold hover:bg-yellow-300 transition-colors\"\n          >\n            Schedule a Visit\n          </Link>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"bg-gray-800 text-white py-8\">\n        <div className=\"container mx-auto px-4 text-center\">\n          <div className=\"mb-4\">\n            <Link href=\"/\" className=\"text-2xl font-bold text-blue-400\">\n              Kinco School\n            </Link>\n          </div>\n          <p className=\"text-gray-400 mb-4\">\n            Providing quality education and care for your children\n          </p>\n          <div className=\"flex justify-center space-x-6\">\n            <Link href=\"/about\" className=\"text-gray-400 hover:text-white\">About</Link>\n            <Link href=\"/classes\" className=\"text-gray-400 hover:text-white\">Classes</Link>\n            <Link href=\"/gallery\" className=\"text-white\">Gallery</Link>\n            <Link href=\"/contact\" className=\"text-gray-400 hover:text-white\">Contact</Link>\n          </div>\n          <div className=\"mt-6 pt-6 border-t border-gray-700\">\n            <p className=\"text-gray-400\">© 2024 Kinco School. All rights reserved.</p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,aAAa;QACjB;YAAE,IAAI;YAAO,MAAM;QAAa;QAChC;YAAE,IAAI;YAAa,MAAM;QAAY;QACrC;YAAE,IAAI;YAAc,MAAM;QAAa;QACvC;YAAE,IAAI;YAAU,MAAM;QAAS;QAC/B;YAAE,IAAI;YAAW,MAAM;QAAe;KACvC;IAED,MAAM,eAAe;QACnB;YAAE,IAAI;YAAG,UAAU;YAAa,OAAO;YAA0C,OAAO;QAAe;QACvG;YAAE,IAAI;YAAG,UAAU;YAAc,OAAO;YAA0C,OAAO;QAAY;QACrG;YAAE,IAAI;YAAG,UAAU;YAAW,OAAO;YAA0C,OAAO;QAAiB;QACvG;YAAE,IAAI;YAAG,UAAU;YAAU,OAAO;YAA0C,OAAO;QAAe;QACpG;YAAE,IAAI;YAAG,UAAU;YAAa,OAAO;YAA0C,OAAO;QAAoB;QAC5G;YAAE,IAAI;YAAG,UAAU;YAAc,OAAO;YAA0C,OAAO;QAAc;QACvG;YAAE,IAAI;YAAG,UAAU;YAAW,OAAO;YAA0C,OAAO;QAAc;QACpG;YAAE,IAAI;YAAG,UAAU;YAAU,OAAO;YAA0C,OAAO;QAAiB;QACtG;YAAE,IAAI;YAAG,UAAU;YAAa,OAAO;YAA0C,OAAO;QAAsB;QAC9G;YAAE,IAAI;YAAI,UAAU;YAAc,OAAO;YAAyC,OAAO;QAAgB;QACzG;YAAE,IAAI;YAAI,UAAU;YAAW,OAAO;YAAyC,OAAO;QAAa;QACnG;YAAE,IAAI;YAAI,UAAU;YAAU,OAAO;YAAyC,OAAO;QAAa;KACnG;IAED,MAAM,gBAAgB,qBAAqB,QACvC,eACA,aAAa,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK;IAElD,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CAAmC;;;;;;0CAG5D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAAoC;;;;;;kDAClE,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAoC;;;;;;kDACpE,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAA8B;;;;;;kDAC9D,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO5E,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA0B;;;;;;sCACxC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAmB;;;;;;8CAC5C,8OAAC;oCAAK,WAAU;8CAAO;;;;;;8CACvB,8OAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;0BAMZ,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAE,WAAU;8CAAkC;;;;;;;;;;;;sCAMjD,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC;oCAEC,SAAS,IAAM,oBAAoB,SAAS,EAAE;oCAC9C,WAAW,CAAC,uDAAuD,EACjE,qBAAqB,SAAS,EAAE,GAC5B,2BACA,+DACJ;8CAED,SAAS,IAAI;mCART,SAAS,EAAE;;;;;;;;;;sCActB,8OAAC;4BAAI,WAAU;sCACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,8OAAC;oCAAkB,WAAU;;sDAC3B,8OAAC;4CACC,KAAK,KAAK,KAAK;4CACf,KAAK,KAAK,KAAK;4CACf,WAAU;;;;;;sDAEZ,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAA8B,KAAK,KAAK;;;;;;kEACtD,8OAAC;wDAAO,WAAU;kEAA+F;;;;;;;;;;;;;;;;;;mCAT7G,KAAK,EAAE;;;;;;;;;;wBAkBpB,cAAc,MAAM,KAAK,mBACxB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;0BAO7C,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA0B;;;;;;sCACxC,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAG1C,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;;;;;;0BAOL,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CAAmC;;;;;;;;;;;sCAI9D,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAGlC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAAiC;;;;;;8CAC/D,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAiC;;;;;;8CACjE,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAa;;;;;;8CAC7C,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAiC;;;;;;;;;;;;sCAEnE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMzC", "debugId": null}}]}