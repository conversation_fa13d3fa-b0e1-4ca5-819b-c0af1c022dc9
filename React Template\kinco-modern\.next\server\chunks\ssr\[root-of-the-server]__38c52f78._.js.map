{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kinco-day-care-kindergarten-react-template-30-06-2025/Kinco/React%20Template/kinco-modern/src/app/page.js"], "sourcesContent": ["'use client';\n\nimport { useEffect } from \"react\";\nimport Link from \"next/link\";\n\nexport default function Home() {\n  useEffect(() => {\n    // Basic setup\n    document.querySelector(\"body\").className = \"main\";\n  }, []);\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Simple Header */}\n      <header className=\"bg-white shadow-sm\">\n        <div className=\"container mx-auto px-4 py-4\">\n          <div className=\"flex justify-between items-center\">\n            <Link href=\"/\" className=\"text-2xl font-bold text-blue-600\">\n              Kinco School\n            </Link>\n            <nav className=\"hidden md:flex space-x-6\">\n              <Link href=\"/about\" className=\"text-gray-600 hover:text-blue-600\">About</Link>\n              <Link href=\"/classes\" className=\"text-gray-600 hover:text-blue-600\">Classes</Link>\n              <Link href=\"/contact\" className=\"text-gray-600 hover:text-blue-600\">Contact</Link>\n            </nav>\n          </div>\n        </div>\n      </header>\n\n      {/* Hero Section */}\n      <section className=\"bg-gradient-to-r from-blue-500 to-purple-600 text-white py-20\">\n        <div className=\"container mx-auto px-4 text-center\">\n          <h1 className=\"text-5xl font-bold mb-6\">\n            Start Learning With <span className=\"text-yellow-300\">Kinco School</span>\n          </h1>\n          <p className=\"text-xl mb-8 max-w-2xl mx-auto\">\n            We provide the best education for your children in a safe, nurturing environment where they can learn, grow, and develop their full potential.\n          </p>\n          <div className=\"space-x-4\">\n            <Link href=\"/about\" className=\"bg-yellow-400 text-blue-900 px-8 py-3 rounded-lg font-semibold hover:bg-yellow-300 transition-colors\">\n              Learn More\n            </Link>\n            <Link href=\"/contact\" className=\"border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors\">\n              Contact Us\n            </Link>\n          </div>\n        </div>\n      </section>\n\n      {/* About Section */}\n      <section className=\"py-16\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"grid md:grid-cols-2 gap-12 items-center\">\n            <div>\n              <div className=\"text-blue-600 font-semibold mb-2\">About Kinco</div>\n              <h2 className=\"text-4xl font-bold mb-6 text-gray-800\">\n                We Provide The Best Education For Your Children\n              </h2>\n              <p className=\"text-gray-600 mb-8 leading-relaxed\">\n                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut elit tellus, luctus nec ullamcorper mattis, pulvinar dapibus leo. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.\n              </p>\n              <Link href=\"/about\" className=\"bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors\">\n                Learn More\n              </Link>\n            </div>\n            <div>\n              <img\n                src=\"/assets/images/common/sc-about1.jpg\"\n                alt=\"About Kinco\"\n                className=\"rounded-lg shadow-lg w-full\"\n              />\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Features Section */}\n      <section className=\"bg-gray-100 py-16\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-4xl font-bold mb-4 text-gray-800\">Why Choose Kinco?</h2>\n            <p className=\"text-gray-600 max-w-2xl mx-auto\">\n              We offer comprehensive programs designed to nurture your child's development\n            </p>\n          </div>\n          <div className=\"grid md:grid-cols-3 gap-8\">\n            <div className=\"bg-white p-6 rounded-lg shadow-md text-center\">\n              <div className=\"text-blue-600 text-4xl mb-4\">🎨</div>\n              <h3 className=\"text-xl font-semibold mb-2\">Creative Learning</h3>\n              <p className=\"text-gray-600\">Encouraging creativity through art, music, and imaginative play</p>\n            </div>\n            <div className=\"bg-white p-6 rounded-lg shadow-md text-center\">\n              <div className=\"text-blue-600 text-4xl mb-4\">👥</div>\n              <h3 className=\"text-xl font-semibold mb-2\">Small Classes</h3>\n              <p className=\"text-gray-600\">Low student-to-teacher ratios for personalized attention</p>\n            </div>\n            <div className=\"bg-white p-6 rounded-lg shadow-md text-center\">\n              <div className=\"text-blue-600 text-4xl mb-4\">🏆</div>\n              <h3 className=\"text-xl font-semibold mb-2\">Quality Education</h3>\n              <p className=\"text-gray-600\">Experienced teachers and proven educational methods</p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Simple Footer */}\n      <footer className=\"bg-gray-800 text-white py-8\">\n        <div className=\"container mx-auto px-4 text-center\">\n          <div className=\"mb-4\">\n            <Link href=\"/\" className=\"text-2xl font-bold text-blue-400\">\n              Kinco School\n            </Link>\n          </div>\n          <p className=\"text-gray-400 mb-4\">\n            Providing quality education and care for your children\n          </p>\n          <div className=\"flex justify-center space-x-6\">\n            <Link href=\"/about\" className=\"text-gray-400 hover:text-white\">About</Link>\n            <Link href=\"/classes\" className=\"text-gray-400 hover:text-white\">Classes</Link>\n            <Link href=\"/contact\" className=\"text-gray-400 hover:text-white\">Contact</Link>\n          </div>\n          <div className=\"mt-6 pt-6 border-t border-gray-700\">\n            <p className=\"text-gray-400\">© 2024 Kinco School. All rights reserved.</p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,cAAc;QACd,SAAS,aAAa,CAAC,QAAQ,SAAS,GAAG;IAC7C,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CAAmC;;;;;;0CAG5D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAAoC;;;;;;kDAClE,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAoC;;;;;;kDACpE,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO5E,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;gCAA0B;8CAClB,8OAAC;oCAAK,WAAU;8CAAkB;;;;;;;;;;;;sCAExD,8OAAC;4BAAE,WAAU;sCAAiC;;;;;;sCAG9C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAAuG;;;;;;8CAGrI,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAA2H;;;;;;;;;;;;;;;;;;;;;;;0BAQjK,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;kDAAmC;;;;;;kDAClD,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDAGtD,8OAAC;wCAAE,WAAU;kDAAqC;;;;;;kDAGlD,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAAgG;;;;;;;;;;;;0CAIhI,8OAAC;0CACC,cAAA,8OAAC;oCACC,KAAI;oCACJ,KAAI;oCACJ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQpB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAE,WAAU;8CAAkC;;;;;;;;;;;;sCAIjD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAA8B;;;;;;sDAC7C,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAE/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAA8B;;;;;;sDAC7C,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAE/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAA8B;;;;;;sDAC7C,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOrC,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CAAmC;;;;;;;;;;;sCAI9D,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAGlC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAAiC;;;;;;8CAC/D,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAiC;;;;;;8CACjE,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAiC;;;;;;;;;;;;sCAEnE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMzC", "debugId": null}}]}