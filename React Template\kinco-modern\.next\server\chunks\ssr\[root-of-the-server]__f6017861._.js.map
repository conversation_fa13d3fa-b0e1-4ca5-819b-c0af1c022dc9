{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kinco-day-care-kindergarten-react-template-30-06-2025/Kinco/React%20Template/kinco-modern/src/app/teacher/page.js"], "sourcesContent": ["'use client';\n\nimport Link from \"next/link\";\n\nexport default function Teachers() {\n  const teachers = [\n    {\n      id: 1,\n      name: \"<PERSON>\",\n      position: \"Lead Teacher\",\n      experience: \"8 years\",\n      specialization: \"Early Childhood Development\",\n      image: \"/assets/images/common/sc-employee-1.jpg\",\n      bio: \"<PERSON> has a passion for nurturing young minds and creating engaging learning environments.\"\n    },\n    {\n      id: 2,\n      name: \"<PERSON>\",\n      position: \"Art Teacher\",\n      experience: \"5 years\",\n      specialization: \"Creative Arts & Crafts\",\n      image: \"/assets/images/common/sc-employee-2.jpg\",\n      bio: \"<PERSON> brings creativity and imagination to every art class, inspiring children to express themselves.\"\n    },\n    {\n      id: 3,\n      name: \"<PERSON>\",\n      position: \"Music Teacher\",\n      experience: \"6 years\",\n      specialization: \"Music & Movement\",\n      image: \"/assets/images/common/sc-employee-3.jpg\",\n      bio: \"Michael helps children discover the joy of music through interactive and fun learning experiences.\"\n    },\n    {\n      id: 4,\n      name: \"<PERSON>\",\n      position: \"Language Teacher\",\n      experience: \"7 years\",\n      specialization: \"English & Communication\",\n      image: \"/assets/images/common/sc-employee-4.jpg\",\n      bio: \"<PERSON> focuses on building strong communication skills and language development in young learners.\"\n    },\n    {\n      id: 5,\n      name: \"<PERSON>\",\n      position: \"Physical Education\",\n      experience: \"4 years\",\n      specialization: \"Sports & Physical Development\",\n      image: \"/assets/images/common/sc-employee-5.jpg\",\n      bio: \"David promotes healthy living and physical development through fun sports and activities.\"\n    },\n    {\n      id: 6,\n      name: \"Jennifer Lee\",\n      position: \"Science Teacher\",\n      experience: \"6 years\",\n      specialization: \"STEM Education\",\n      image: \"/assets/images/common/sc-employee-6.jpg\",\n      bio: \"Jennifer makes science fun and accessible, encouraging curiosity and exploration in young minds.\"\n    }\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm\">\n        <div className=\"container mx-auto px-4 py-4\">\n          <div className=\"flex justify-between items-center\">\n            <Link href=\"/\" className=\"text-2xl font-bold text-blue-600\">\n              Kinco School\n            </Link>\n            <nav className=\"hidden md:flex space-x-6\">\n              <Link href=\"/about\" className=\"text-gray-600 hover:text-blue-600\">About</Link>\n              <Link href=\"/classes\" className=\"text-gray-600 hover:text-blue-600\">Classes</Link>\n              <Link href=\"/teacher\" className=\"text-blue-600 font-semibold\">Teachers</Link>\n              <Link href=\"/contact\" className=\"text-gray-600 hover:text-blue-600\">Contact</Link>\n            </nav>\n          </div>\n        </div>\n      </header>\n\n      {/* Page Banner */}\n      <section className=\"bg-gradient-to-r from-blue-500 to-purple-600 text-white py-16\">\n        <div className=\"container mx-auto px-4 text-center\">\n          <h1 className=\"text-4xl font-bold mb-4\">Our Teachers</h1>\n          <nav className=\"text-blue-200\">\n            <Link href=\"/\" className=\"hover:text-white\">Home</Link>\n            <span className=\"mx-2\">/</span>\n            <span>Teachers</span>\n          </nav>\n        </div>\n      </section>\n\n      {/* Teachers Section */}\n      <section className=\"py-16\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-4xl font-bold mb-4 text-gray-800\">Meet Our Dedicated Team</h2>\n            <p className=\"text-gray-600 max-w-2xl mx-auto\">\n              Our experienced and passionate teachers are committed to providing the best educational experience for your children\n            </p>\n          </div>\n\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {teachers.map((teacher) => (\n              <div key={teacher.id} className=\"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow\">\n                <img \n                  src={teacher.image} \n                  alt={teacher.name}\n                  className=\"w-full h-64 object-cover\"\n                />\n                <div className=\"p-6\">\n                  <h3 className=\"text-xl font-bold mb-1 text-gray-800\">{teacher.name}</h3>\n                  <p className=\"text-blue-600 font-semibold mb-2\">{teacher.position}</p>\n                  <div className=\"text-sm text-gray-500 mb-3\">\n                    <p>Experience: {teacher.experience}</p>\n                    <p>Specialization: {teacher.specialization}</p>\n                  </div>\n                  <p className=\"text-gray-600 text-sm mb-4\">{teacher.bio}</p>\n                  <Link \n                    href={`/teacher/${teacher.id}`}\n                    className=\"text-blue-600 hover:text-blue-800 font-semibold text-sm\"\n                  >\n                    View Profile →\n                  </Link>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Join Our Team Section */}\n      <section className=\"bg-blue-600 text-white py-16\">\n        <div className=\"container mx-auto px-4 text-center\">\n          <h2 className=\"text-3xl font-bold mb-4\">Join Our Teaching Team</h2>\n          <p className=\"text-xl mb-8 text-blue-100\">\n            Are you passionate about early childhood education? We're always looking for dedicated teachers to join our team.\n          </p>\n          <Link \n            href=\"/contact\" \n            className=\"bg-yellow-400 text-blue-900 px-8 py-3 rounded-lg font-semibold hover:bg-yellow-300 transition-colors\"\n          >\n            Apply Now\n          </Link>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"bg-gray-800 text-white py-8\">\n        <div className=\"container mx-auto px-4 text-center\">\n          <div className=\"mb-4\">\n            <Link href=\"/\" className=\"text-2xl font-bold text-blue-400\">\n              Kinco School\n            </Link>\n          </div>\n          <p className=\"text-gray-400 mb-4\">\n            Providing quality education and care for your children\n          </p>\n          <div className=\"flex justify-center space-x-6\">\n            <Link href=\"/about\" className=\"text-gray-400 hover:text-white\">About</Link>\n            <Link href=\"/classes\" className=\"text-gray-400 hover:text-white\">Classes</Link>\n            <Link href=\"/teacher\" className=\"text-white\">Teachers</Link>\n            <Link href=\"/contact\" className=\"text-gray-400 hover:text-white\">Contact</Link>\n          </div>\n          <div className=\"mt-6 pt-6 border-t border-gray-700\">\n            <p className=\"text-gray-400\">© 2024 Kinco School. All rights reserved.</p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,MAAM,WAAW;QACf;YACE,IAAI;YACJ,MAAM;YACN,UAAU;YACV,YAAY;YACZ,gBAAgB;YAChB,OAAO;YACP,KAAK;QACP;QACA;YACE,IAAI;YACJ,MAAM;YACN,UAAU;YACV,YAAY;YACZ,gBAAgB;YAChB,OAAO;YACP,KAAK;QACP;QACA;YACE,IAAI;YACJ,MAAM;YACN,UAAU;YACV,YAAY;YACZ,gBAAgB;YAChB,OAAO;YACP,KAAK;QACP;QACA;YACE,IAAI;YACJ,MAAM;YACN,UAAU;YACV,YAAY;YACZ,gBAAgB;YAChB,OAAO;YACP,KAAK;QACP;QACA;YACE,IAAI;YACJ,MAAM;YACN,UAAU;YACV,YAAY;YACZ,gBAAgB;YAChB,OAAO;YACP,KAAK;QACP;QACA;YACE,IAAI;YACJ,MAAM;YACN,UAAU;YACV,YAAY;YACZ,gBAAgB;YAChB,OAAO;YACP,KAAK;QACP;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CAAmC;;;;;;0CAG5D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAAoC;;;;;;kDAClE,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAoC;;;;;;kDACpE,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAA8B;;;;;;kDAC9D,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO5E,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA0B;;;;;;sCACxC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAmB;;;;;;8CAC5C,8OAAC;oCAAK,WAAU;8CAAO;;;;;;8CACvB,8OAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;0BAMZ,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAE,WAAU;8CAAkC;;;;;;;;;;;;sCAKjD,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;oCAAqB,WAAU;;sDAC9B,8OAAC;4CACC,KAAK,QAAQ,KAAK;4CAClB,KAAK,QAAQ,IAAI;4CACjB,WAAU;;;;;;sDAEZ,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAwC,QAAQ,IAAI;;;;;;8DAClE,8OAAC;oDAAE,WAAU;8DAAoC,QAAQ,QAAQ;;;;;;8DACjE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;gEAAE;gEAAa,QAAQ,UAAU;;;;;;;sEAClC,8OAAC;;gEAAE;gEAAiB,QAAQ,cAAc;;;;;;;;;;;;;8DAE5C,8OAAC;oDAAE,WAAU;8DAA8B,QAAQ,GAAG;;;;;;8DACtD,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,CAAC,SAAS,EAAE,QAAQ,EAAE,EAAE;oDAC9B,WAAU;8DACX;;;;;;;;;;;;;mCAjBK,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;0BA4B5B,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA0B;;;;;;sCACxC,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAG1C,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;;;;;;0BAOL,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CAAmC;;;;;;;;;;;sCAI9D,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAGlC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAAiC;;;;;;8CAC/D,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAiC;;;;;;8CACjE,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAa;;;;;;8CAC7C,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAiC;;;;;;;;;;;;sCAEnE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMzC", "debugId": null}}]}